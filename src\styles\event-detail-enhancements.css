/* Event Detail Page Visual Enhancements */

/* 英雄区域渐变背景 */
.hero-gradient {
  background: linear-gradient(135deg, 
    hsl(var(--primary) / 0.05) 0%, 
    hsl(var(--primary) / 0.1) 50%, 
    hsl(var(--secondary) / 0.05) 100%);
}

/* 卡片悬停效果 */
.card-hover {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.card-hover:hover {
  transform: translateY(-2px);
  box-shadow: 0 10px 25px -5px rgba(0, 0, 0, 0.1), 
              0 10px 10px -5px rgba(0, 0, 0, 0.04);
}

/* 信息卡片动画 */
.info-card {
  transition: all 0.2s ease-in-out;
  border: 1px solid hsl(var(--border));
  background: hsl(var(--card));
}

.info-card:hover {
  border-color: hsl(var(--primary) / 0.3);
  background: hsl(var(--card) / 0.8);
  backdrop-filter: blur(8px);
}

/* 标签页导航增强 */
.tabs-list-enhanced {
  background: hsl(var(--muted) / 0.5);
  backdrop-filter: blur(8px);
  border: 1px solid hsl(var(--border));
}

.tabs-trigger-enhanced {
  transition: all 0.2s ease-in-out;
  border-radius: 6px;
  margin: 2px;
}

.tabs-trigger-enhanced[data-state="active"] {
  background: hsl(var(--background));
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

/* 搜索框增强 */
.search-input-enhanced {
  transition: all 0.2s ease-in-out;
  border: 1px solid hsl(var(--border));
}

.search-input-enhanced:focus {
  border-color: hsl(var(--primary));
  box-shadow: 0 0 0 3px hsl(var(--primary) / 0.1);
}

/* 筛选标签动画 */
.filter-badge {
  transition: all 0.2s ease-in-out;
  cursor: pointer;
}

.filter-badge:hover {
  transform: scale(1.05);
}

.filter-badge.active {
  background: hsl(var(--primary));
  color: hsl(var(--primary-foreground));
  box-shadow: 0 2px 8px hsl(var(--primary) / 0.3);
}

/* 加载动画增强 */
.loading-spinner {
  animation: spin 1s linear infinite;
}

@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

.loading-pulse {
  animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}

@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
}

/* 骨架屏动画 */
.skeleton {
  background: linear-gradient(90deg, 
    hsl(var(--muted)) 25%, 
    hsl(var(--muted) / 0.5) 50%, 
    hsl(var(--muted)) 75%);
  background-size: 200% 100%;
  animation: skeleton-loading 1.5s ease-in-out infinite;
}

@keyframes skeleton-loading {
  0% {
    background-position: 200% 0;
  }
  100% {
    background-position: -200% 0;
  }
}

/* 按钮增强效果 */
.button-enhanced {
  transition: all 0.2s ease-in-out;
  position: relative;
  overflow: hidden;
}

.button-enhanced::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, 
    transparent, 
    rgba(255, 255, 255, 0.2), 
    transparent);
  transition: left 0.5s;
}

.button-enhanced:hover::before {
  left: 100%;
}

/* 地图容器增强 */
.map-container {
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 
              0 2px 4px -1px rgba(0, 0, 0, 0.06);
  transition: box-shadow 0.3s ease-in-out;
}

.map-container:hover {
  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 
              0 4px 6px -2px rgba(0, 0, 0, 0.05);
}

/* 响应式增强 */
@media (max-width: 768px) {
  .hero-gradient {
    background: linear-gradient(180deg, 
      hsl(var(--primary) / 0.05) 0%, 
      hsl(var(--background)) 100%);
  }
  
  .card-hover:hover {
    transform: none;
  }
  
  .info-card {
    padding: 12px;
  }
}

/* 深色模式适配 */
@media (prefers-color-scheme: dark) {
  .hero-gradient {
    background: linear-gradient(135deg, 
      hsl(var(--primary) / 0.1) 0%, 
      hsl(var(--primary) / 0.15) 50%, 
      hsl(var(--secondary) / 0.1) 100%);
  }
  
  .card-hover:hover {
    box-shadow: 0 10px 25px -5px rgba(0, 0, 0, 0.3), 
                0 10px 10px -5px rgba(0, 0, 0, 0.1);
  }
  
  .map-container {
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.3), 
                0 2px 4px -1px rgba(0, 0, 0, 0.2);
  }
}

/* 无障碍增强 */
@media (prefers-reduced-motion: reduce) {
  .card-hover,
  .info-card,
  .filter-badge,
  .button-enhanced {
    transition: none;
  }
  
  .loading-spinner,
  .loading-pulse,
  .skeleton {
    animation: none;
  }
}

/* 高对比度模式 */
@media (prefers-contrast: high) {
  .info-card {
    border-width: 2px;
  }
  
  .filter-badge.active {
    border: 2px solid hsl(var(--primary));
  }
  
  .search-input-enhanced:focus {
    border-width: 2px;
  }
}

/* 地图容器样式 */
.map-container {
  @apply relative overflow-hidden rounded-lg border border-gray-200;
}

/* 地图预览容器样式 */
.map-preview-container {
  @apply relative overflow-hidden rounded-lg border border-gray-200 transition-colors;
}

.map-preview-container:hover {
  @apply border-primary/50;
}
