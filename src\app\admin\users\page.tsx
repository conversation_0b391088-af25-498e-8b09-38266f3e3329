"use client";

import Link from "next/link";
import { useRouter } from "next/navigation";
import React from "react";

import { Button } from "@/components/ui/button";
import { useAdminUsers } from "@/hooks/admin/useAdminUsers";
import { useDeleteUser } from "@/hooks/admin/useDeleteUser";

export default function AdminUsersPage() {
  const router = useRouter();
  const { data: users = [], isLoading } = useAdminUsers();
  const deleteUser = useDeleteUser();

  function handleDelete(id: string) {
    if (!confirm("确定删除该用户吗？")) return;
    deleteUser.mutate(id);
  }

  return (
    <div className="space-y-4">
      <div className="flex justify-between items-center">
        <h1 className="text-2xl font-bold">用户管理</h1>
        <Button onClick={() => { router.push("/admin/users/new"); }}>新增用户</Button>
      </div>

      {isLoading ? (
        <p>加载中...</p>
      ) : (
        <table className="w-full border text-sm">
          <thead>
            <tr className="bg-muted">
              <th className="border px-2 py-1">ID</th>
              <th className="border px-2 py-1">用户名</th>
              <th className="border px-2 py-1">角色</th>
              <th className="border px-2 py-1 w-32">操作</th>
            </tr>
          </thead>
          <tbody>
            {users.map((u) => (
              <tr key={u.id}>
                <td className="border px-2 py-1">{u.id}</td>
                <td className="border px-2 py-1">{u.username}</td>
                <td className="border px-2 py-1">{u.role}</td>
                <td className="border px-2 py-1 space-x-2 text-center">
                  <Link
                    href={`/admin/users/${u.id}/edit`}
                    className="text-primary hover:underline"
                  >
                    编辑
                  </Link>
                  <button
                    onClick={() => { handleDelete(u.id); }}
                    className="text-destructive hover:underline"
                    disabled={deleteUser.isPending}
                  >
                    删除
                  </button>
                </td>
              </tr>
            ))}
          </tbody>
        </table>
      )}
    </div>
  );
} 