---
id: faq
title: 常见问题
sidebar_label: 常见问题
sidebar_position: 10
description: Ayafeed 前端开发常见问题解答
keywords: [faq, 常见问题, 前端开发, 故障排除]
---

# 常见问题

## 环境配置

### Q: 安装依赖时遇到网络问题怎么办？

A: 建议使用国内镜像源：

```bash
# 使用淘宝镜像
pnpm config set registry https://registry.npmmirror.com/

# 或者使用 cnpm
npm install -g cnpm --registry=https://registry.npmmirror.com
cnpm install
```

### Q: Node.js 版本要求是什么？

A: 项目要求 Node.js 18.0 或更高版本。推荐使用 LTS 版本。

```bash
# 检查当前版本
node --version

# 使用 nvm 切换版本（如果已安装）
nvm use 18
```

## API 集成

### Q: API 客户端生成失败怎么办？

A: 确保 OpenAPI 规范文件路径正确，并且网络连接正常：

```bash
# 检查配置文件
cat openapi-codegen.config.ts

# 重新生成客户端
pnpm gen:rq
```

### Q: 如何处理 API 认证？

A: 项目使用基于 Cookie 的认证方式。详见 [认证指南](./authentication.md)。

### Q: API 请求超时怎么办？

A: 可以在客户端配置中调整超时时间：

```typescript
const client = new ApiClient({
  baseURL: process.env.NEXT_PUBLIC_API_URL,
  timeout: 10000, // 10秒超时
});
```

## 开发问题

### Q: 热重载不工作怎么办？

A: 尝试以下解决方案：

1. 重启开发服务器
2. 清除 Next.js 缓存：`rm -rf .next`
3. 检查文件监听限制（Linux/macOS）

### Q: TypeScript 类型错误怎么解决？

A: 确保类型定义是最新的：

```bash
# 重新生成 API 客户端类型
pnpm gen:rq

# 检查 TypeScript 配置
npx tsc --noEmit
```

### Q: 样式不生效怎么办？

A: 检查 CSS 模块和 Tailwind CSS 配置：

1. 确保 CSS 文件正确导入
2. 检查 Tailwind CSS 类名是否正确
3. 验证 CSS 模块命名约定

## 国际化

### Q: 如何添加新的语言？

A: 参考 [国际化指南](./i18n-integration.md) 中的详细步骤。

### Q: 翻译文件在哪里？

A: 翻译文件位于 `locales/` 目录下，按语言代码组织。

## 部署问题

### Q: 构建失败怎么办？

A: 检查以下常见问题：

1. 环境变量是否正确设置
2. 依赖是否完整安装
3. TypeScript 类型检查是否通过

```bash
# 检查构建
pnpm build

# 检查类型
pnpm type-check
```

### Q: 生产环境 API 连接问题？

A: 确保环境变量正确配置：

```bash
# 检查环境变量
echo $NEXT_PUBLIC_API_URL
```

## 性能优化

### Q: 页面加载慢怎么优化？

A: 参考 [性能指南](../guides/performance.md) 中的优化建议。

### Q: 如何减少包体积？

A: 使用以下策略：

1. 启用 Tree Shaking
2. 使用动态导入
3. 优化图片资源
4. 分析包体积：`pnpm analyze`

## 获取帮助

如果以上解答没有解决你的问题，可以：

1. 查看 [完整文档](../README.md)
2. 提交 [GitHub Issue](https://github.com/your-org/ayafeed/issues)
3. 参与 [GitHub Discussions](https://github.com/your-org/ayafeed/discussions)

---

*最后更新：2025-07-28*
