'use client'

import { But<PERSON> } from "@/components/ui/button"
import { toast } from "sonner"

export default function ToastTestPage() {
  const testToast = () => {
    toast.success("这是一个测试 Toast 消息！")
  }

  const testErrorToast = () => {
    toast.error("这是一个错误 Toast 消息！")
  }

  const testInfoToast = () => {
    toast.info("这是一个信息 Toast 消息！")
  }

  const testWarningToast = () => {
    toast.warning("这是一个警告 Toast 消息！")
  }

  return (
    <div className="container mx-auto p-8">
      <h1 className="text-2xl font-bold mb-6">Toast 测试页面</h1>
      
      <div className="space-y-4">
        <div>
          <Button onClick={testToast} className="mr-4">
            测试成功 Toast
          </Button>
          <Button onClick={testErrorToast} variant="destructive" className="mr-4">
            测试错误 Toast
          </Button>
          <Button onClick={testInfoToast} variant="outline" className="mr-4">
            测试信息 Toast
          </Button>
          <Button onClick={testWarningToast} variant="secondary">
            测试警告 Toast
          </Button>
        </div>
        
        <div className="mt-8">
          <p className="text-sm text-muted-foreground">
            点击按钮测试不同类型的 Toast 消息。
            如果 Toast 正常工作，你应该在页面顶部中央看到弹窗消息。
          </p>
        </div>
      </div>
    </div>
  )
}
