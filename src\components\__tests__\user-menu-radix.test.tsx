import { describe, test, expect, vi, beforeEach } from 'vitest'
import { render, screen, fireEvent, waitFor } from '@testing-library/react'
import { renderWithProviders } from '@/__test__/test-utils'
import UserMenu from '../user-menu'

// Mock Next.js Link
vi.mock('next/link', () => ({
  default: ({ children, href, ...props }: any) => (
    <a href={href} {...props}>
      {children}
    </a>
  )
}))

// Mock useAuth hook
const mockLogout = vi.fn()
const mockUseAuth = vi.fn()

vi.mock('@/contexts/user', async (importOriginal) => {
  const actual = await importOriginal()
  return {
    ...actual,
    useAuth: () => mockUseAuth()
  }
})

describe('UserMenu (Radix Migration)', () => {
  beforeEach(() => {
    vi.clearAllMocks()
  })

  test('shows loading state when isLoading is true', () => {
    mockUseAuth.mockReturnValue({
      user: null,
      isLoading: true,
      logout: mockLogout
    })

    renderWithProviders(<UserMenu />)
    
    // 检查加载状态的按钮
    const loadingButton = screen.getByRole('button')
    expect(loadingButton).toBeInTheDocument()
    expect(loadingButton).toBeDisabled()
    expect(loadingButton).toHaveClass('opacity-50')
    
    // 检查用户图标
    const userIcon = screen.getByRole('button').querySelector('svg')
    expect(userIcon).toBeInTheDocument()
  })

  test('renders menu button when user is not logged in', () => {
    mockUseAuth.mockReturnValue({
      user: null,
      isLoading: false,
      logout: mockLogout
    })

    renderWithProviders(<UserMenu />)

    // 检查菜单按钮存在
    const menuButton = screen.getByRole('button')
    expect(menuButton).toBeInTheDocument()
    expect(menuButton).toHaveAttribute('aria-haspopup', 'menu')
    expect(menuButton).toHaveAttribute('data-state', 'closed')
  })

  test('renders menu button when user is logged in', () => {
    const mockUser = {
      username: 'testuser',
      email: '<EMAIL>'
    }

    mockUseAuth.mockReturnValue({
      user: mockUser,
      isLoading: false,
      logout: mockLogout
    })

    renderWithProviders(<UserMenu />)

    // 检查菜单按钮存在
    const menuButton = screen.getByRole('button')
    expect(menuButton).toBeInTheDocument()
    expect(menuButton).toHaveAttribute('aria-haspopup', 'menu')
    expect(menuButton).toHaveAttribute('data-state', 'closed')
  })

  test('renders correct user icon', () => {
    const mockUser = {
      username: 'testuser'
      // no email
    }

    mockUseAuth.mockReturnValue({
      user: mockUser,
      isLoading: false,
      logout: mockLogout
    })

    renderWithProviders(<UserMenu />)

    // 检查用户图标
    const menuButton = screen.getByRole('button')
    const userIcon = menuButton.querySelector('svg')
    expect(userIcon).toBeInTheDocument()
    expect(userIcon).toHaveClass('lucide-circle-user')
  })

  test('button is clickable when not loading', () => {
    const mockUser = {
      username: 'testuser',
      email: '<EMAIL>'
    }

    mockUseAuth.mockReturnValue({
      user: mockUser,
      isLoading: false,
      logout: mockLogout
    })

    renderWithProviders(<UserMenu />)

    const menuButton = screen.getByRole('button')
    expect(menuButton).not.toBeDisabled()

    // 测试点击功能
    fireEvent.click(menuButton)
    // 验证按钮状态可能会改变（但我们不测试菜单内容）
  })

  test('menu button has correct styling and hover effects', () => {
    mockUseAuth.mockReturnValue({
      user: null,
      isLoading: false,
      logout: mockLogout
    })

    renderWithProviders(<UserMenu />)

    const menuButton = screen.getByRole('button')

    // 检查基本样式类
    expect(menuButton).toHaveClass('w-10', 'h-10', 'px-0', 'transition-all')

    // 检查用户图标的悬停效果
    const userIcon = menuButton.querySelector('svg')
    expect(userIcon).toHaveClass('transition-transform', 'hover:scale-110')
  })

  test('component renders without crashing', () => {
    mockUseAuth.mockReturnValue({
      user: null,
      isLoading: false,
      logout: mockLogout
    })

    const { container } = renderWithProviders(<UserMenu />)
    expect(container.firstChild).toBeInTheDocument()
  })

  test('handles different user states correctly', () => {
    // Test with user
    mockUseAuth.mockReturnValue({
      user: { username: 'test' },
      isLoading: false,
      logout: mockLogout
    })

    const { rerender } = renderWithProviders(<UserMenu />)
    expect(screen.getByRole('button')).toBeInTheDocument()

    // Test without user
    mockUseAuth.mockReturnValue({
      user: null,
      isLoading: false,
      logout: mockLogout
    })

    rerender(<UserMenu />)
    expect(screen.getByRole('button')).toBeInTheDocument()
  })
})
