"use client";

import { useRouter } from "next/navigation";
import React, { useEffect, useState } from "react";

import { useAuth } from "@/contexts/user";
import { authService } from "@/services/auth";
import type { UserRole } from "@/types/user";

interface RoleGuardProps {
  allow: UserRole[];
  /** 当权限不足时的替代渲染，默认跳转到 /403 */
  fallback?: React.ReactNode;
  children: React.ReactNode;
}

export default function RoleGuard({ allow, children, fallback }: RoleGuardProps) {
  const router = useRouter();
  const { user, isLoading } = useAuth();
  const [hasPermission, setHasPermission] = useState<boolean | null>(null);

  // 异步权限检查
  useEffect(() => {
    let isCancelled = false;

    const checkPermission = async () => {
      if (isLoading) return; // Wait for user session to be checked
      if (isCancelled) return; // 防止组件卸载后继续执行

      if (!user) {
        // 用户未登录，重定向到登录页面
        const currentPath = window.location.pathname;
        const loginUrl = `/login?redirect=${encodeURIComponent(currentPath)}`;
        router.replace(loginUrl);
        return;
      }

      try {
        // 先进行本地权限检查，减少API调用
        const hasLocalPermission = allow.includes(user.role);

        if (hasLocalPermission) {
          // 本地检查通过，直接设置权限
          console.debug('[RoleGuard] 本地权限检查通过:', user.role, 'in', allow);
          setHasPermission(true);
        } else {
          // 本地检查失败，进行服务端验证（以防用户信息过期）
          console.debug('[RoleGuard] 本地权限检查失败，进行服务端验证');
          const hasAuth = await authService.hasPermission(allow);

          if (isCancelled) return; // 检查是否已取消

          setHasPermission(hasAuth);

          if (!hasAuth) {
            // 权限不足，重定向到403页面
            router.replace("/403");
            return;
          }
        }
      } catch (error) {
        if (isCancelled) return; // 检查是否已取消

        // 权限检查失败，可能是认证过期
        console.error('Permission check failed:', error);
        setHasPermission(false);
        const currentPath = window.location.pathname;
        const loginUrl = `/login?redirect=${encodeURIComponent(currentPath)}`;
        router.replace(loginUrl);
      }
    };

    checkPermission();

    // 清理函数
    return () => {
      isCancelled = true;
    };
  }, [isLoading, user, allow, router]);

  // 如果正在加载或权限检查中，显示加载状态
  if (isLoading || hasPermission === null) {
    return fallback ?? (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center space-y-4">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto"></div>
          <p className="text-muted-foreground">正在验证权限...</p>
        </div>
      </div>
    );
  }

  // 如果用户未登录，显示重定向状态
  if (!user) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center space-y-4">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto"></div>
          <p className="text-muted-foreground">正在跳转到登录页面...</p>
        </div>
      </div>
    );
  }

  // 如果权限检查失败，显示加载状态（重定向正在进行中）
  if (!hasPermission) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center space-y-4">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto"></div>
          <p className="text-muted-foreground">权限不足，正在跳转...</p>
        </div>
      </div>
    );
  }

  return <>{children}</>;
}