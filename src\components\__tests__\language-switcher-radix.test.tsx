import { describe, test, expect, vi, beforeEach } from 'vitest'
import { render, screen, fireEvent } from '@testing-library/react'
import { renderWithProviders } from '@/__test__/test-utils'
import LanguageSwitcher from '../language-switcher'

// Mock next-intl
const mockUseLocale = vi.fn()
vi.mock('next-intl', async (importOriginal) => {
  const actual = await importOriginal()
  return {
    ...actual,
    useLocale: () => mockUseLocale()
  }
})

// Mock next/navigation
const mockRefresh = vi.fn()
const mockUseRouter = vi.fn()
vi.mock('next/navigation', () => ({
  useRouter: () => mockUseRouter()
}))

// Mock @tanstack/react-query
const mockClear = vi.fn()
const mockUseQueryClient = vi.fn()
vi.mock('@tanstack/react-query', async (importOriginal) => {
  const actual = await importOriginal()
  return {
    ...actual,
    useQueryClient: () => mockUseQueryClient()
  }
})

// Mock locale utils
vi.mock('@/lib/locale-utils', () => ({
  SUPPORTED_LOCALES: ['ja', 'en', 'zh'],
  setLocaleToCookie: vi.fn(),
  getCurrentLocale: vi.fn(() => 'en')
}))

describe('LanguageSwitcher (Radix Migration)', () => {
  beforeEach(() => {
    vi.clearAllMocks()
    
    // 设置默认的 mock 返回值
    mockUseLocale.mockReturnValue('en')
    mockUseRouter.mockReturnValue({
      refresh: mockRefresh
    })
    mockUseQueryClient.mockReturnValue({
      clear: mockClear
    })
  })

  test('renders language switcher button', () => {
    renderWithProviders(<LanguageSwitcher />)
    
    // 检查语言切换按钮
    const switcherButton = screen.getByRole('button')
    expect(switcherButton).toBeInTheDocument()
    expect(switcherButton).toHaveAttribute('aria-haspopup', 'menu')
    
    // 检查地球图标
    const globeIcon = switcherButton.querySelector('svg')
    expect(globeIcon).toBeInTheDocument()
    expect(globeIcon).toHaveClass('lucide-globe')
  })

  test('button has correct styling and hover effects', () => {
    renderWithProviders(<LanguageSwitcher />)
    
    const switcherButton = screen.getByRole('button')
    
    // 检查基本样式类
    expect(switcherButton).toHaveClass('w-10', 'h-10', 'px-0', 'transition-all')
    
    // 检查地球图标的悬停效果
    const globeIcon = switcherButton.querySelector('svg')
    expect(globeIcon).toHaveClass('transition-transform', 'hover:scale-110')
  })

  test('renders with different current locales', () => {
    // 测试日语
    mockUseLocale.mockReturnValue('ja')
    const { rerender } = renderWithProviders(<LanguageSwitcher />)
    expect(screen.getByRole('button')).toBeInTheDocument()

    // 测试中文
    mockUseLocale.mockReturnValue('zh')
    rerender(<LanguageSwitcher />)
    expect(screen.getByRole('button')).toBeInTheDocument()

    // 测试英语
    mockUseLocale.mockReturnValue('en')
    rerender(<LanguageSwitcher />)
    expect(screen.getByRole('button')).toBeInTheDocument()
  })

  test('button is clickable', () => {
    renderWithProviders(<LanguageSwitcher />)
    
    const switcherButton = screen.getByRole('button')
    expect(switcherButton).not.toBeDisabled()
    
    // 测试点击功能
    fireEvent.click(switcherButton)
    // 验证按钮状态可能会改变（但我们不测试菜单内容）
  })

  test('component renders without crashing', () => {
    const { container } = renderWithProviders(<LanguageSwitcher />)
    expect(container.firstChild).toBeInTheDocument()
  })

  test('handles missing locale gracefully', () => {
    // 测试当 locale 为 undefined 时
    mockUseLocale.mockReturnValue(undefined)
    
    const { container } = renderWithProviders(<LanguageSwitcher />)
    expect(container.firstChild).toBeInTheDocument()
    expect(screen.getByRole('button')).toBeInTheDocument()
  })

  test('button has correct accessibility attributes', () => {
    renderWithProviders(<LanguageSwitcher />)
    
    const switcherButton = screen.getByRole('button')
    
    // 检查可访问性属性
    expect(switcherButton).toHaveAttribute('aria-haspopup', 'menu')
    expect(switcherButton).toHaveAttribute('data-state', 'closed')
  })

  test('globe icon has correct size classes', () => {
    renderWithProviders(<LanguageSwitcher />)
    
    const switcherButton = screen.getByRole('button')
    const globeIcon = switcherButton.querySelector('svg')
    
    expect(globeIcon).toHaveClass('h-5', 'w-5')
  })

  test('component maintains consistent styling across renders', () => {
    const { rerender } = renderWithProviders(<LanguageSwitcher />)
    
    let switcherButton = screen.getByRole('button')
    const initialClasses = switcherButton.className
    
    // 重新渲染
    rerender(<LanguageSwitcher />)
    
    switcherButton = screen.getByRole('button')
    expect(switcherButton.className).toBe(initialClasses)
  })

  test('handles different supported locales', () => {
    // 这个测试确保组件能处理不同的支持语言列表
    renderWithProviders(<LanguageSwitcher />)
    
    const switcherButton = screen.getByRole('button')
    expect(switcherButton).toBeInTheDocument()
    
    // 验证组件不会因为语言列表而崩溃
    fireEvent.click(switcherButton)
  })
})
