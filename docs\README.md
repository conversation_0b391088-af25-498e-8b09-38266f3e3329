# Ayafeed 文档中心

## 📚 文档索引

### 🏢 Venue模块文档

#### 核心文档
- **[Venue模块完整指南](./venue-module-guide.md)** - 完整的venue模块使用指南
  - API接口文档
  - 前端组件使用
  - 管理后台操作
  - 开发指南和最佳实践

#### 技术文档
- **[Venue迁移计划](./venue-migration-plan.md)** - venues表拆分的详细计划
  - 迁移阶段划分
  - 前后端职责
  - 技术实现细节
  - 测试策略

- **[数据库Schema整合](./database-schema-integration.md)** - 数据库结构整合文档
  - Migration整合说明
  - 当前数据库结构
  - 部署指南
  - 验证清单

### 🚀 快速开始

#### 开发者快速上手
1. **了解架构** - 阅读 [Venue模块完整指南](./venue-module-guide.md#架构设计)
2. **API使用** - 查看 [API接口文档](./venue-module-guide.md#api接口)
3. **前端开发** - 参考 [前端组件文档](./venue-module-guide.md#前端组件)
4. **后台管理** - 学习 [管理后台使用](./venue-module-guide.md#管理后台)

#### 部署和运维
1. **数据库设置** - 参考 [数据库Schema整合](./database-schema-integration.md#部署指南)
2. **迁移执行** - 查看 [迁移计划](./venue-migration-plan.md#迁移阶段)
3. **验证测试** - 使用 [验证清单](./database-schema-integration.md#验证清单)

### 📋 功能特性

#### ✅ 已实现功能
- 🏢 **完整的Venue管理系统**
  - CRUD操作（创建、读取、更新、删除）
  - 多语言支持（中文、日文、英文）
  - 搜索和分页
  - 地理位置管理

- 🎨 **智能前端组件**
  - VenueSelector - 智能场馆选择器
  - VenueForm - 多语言表单组件
  - 响应式设计
  - 实时搜索和预览

- 🛠️ **管理后台**
  - 场馆列表管理
  - 新增/编辑场馆
  - 批量操作
  - 权限控制

- 🔗 **Event集成**
  - Event-Venue关联
  - 向后兼容
  - 数据迁移支持

#### 🔄 数据库优化
- 规范化设计（venues表独立）
- 多语言字段支持
- JSON扩展信息（设施、交通、停车）
- 性能索引优化

### 🛠️ 开发工具

#### 脚本工具
- `scripts/rebuild-database.ts` - 数据库重建脚本
- `scripts/cleanup-migrations.ts` - Migration清理脚本
- `scripts/migrate-venues.ts` - Venue数据迁移脚本

#### 测试工具
- 单元测试覆盖
- API集成测试
- 前端组件测试

### 📞 支持和贡献

#### 问题反馈
- 🐛 Bug报告：请提供详细的复现步骤
- 💡 功能建议：描述使用场景和预期效果
- 📖 文档改进：指出不清楚或缺失的内容

#### 开发贡献
1. Fork项目
2. 创建功能分支
3. 提交代码（遵循代码规范）
4. 创建Pull Request

### 📈 版本历史

#### v1.0.0 - Venue模块发布
- ✅ 完整的venue CRUD功能
- ✅ 多语言国际化支持
- ✅ 管理后台集成
- ✅ Event-Venue关联
- ✅ 数据库迁移完成

### 🔗 相关链接

- [项目主页](../README.md)
- [API文档](../openapi.json)
- [前端组件库](../src/components/)
- [后端API模块](../src/modules/venue/)

---

## 📝 文档维护

本文档由开发团队维护，如有问题或建议，请：
1. 提交Issue到项目仓库
2. 直接编辑文档并提交PR
3. 联系项目维护者

**最后更新：** 2025-01-30  
**文档版本：** v1.0.0
