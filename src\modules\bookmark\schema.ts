import { z } from '@hono/zod-openapi';

// 基础 Bookmark 对象
export const bookmarkSchema = z.object({
  id: z.string().openapi({ example: 'uuid-123' }),
  user_id: z.string().openapi({ example: 'user-uuid' }),
  circle_id: z.string().openapi({ example: 'circle-uuid' }),
  created_at: z.string().openapi({ example: '2025-01-01T00:00:00Z' }),
  updated_at: z.string().openapi({ example: '2025-01-01T00:00:00Z' }),
});

export type Bookmark = z.infer<typeof bookmarkSchema>;

// Toggle 请求体：仅需 circleId
export const bookmarkToggleRequest = z.object({
  circleId: z.string().openapi({ example: 'circle-uuid' }),
});

export type BookmarkToggleInput = z.infer<typeof bookmarkToggleRequest>;
