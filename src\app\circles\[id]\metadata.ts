import { Metadata } from "next";
import { fetchGetCirclesId } from "@/api/generated/ayafeedComponents";

const SITE_URL = process.env.NEXT_PUBLIC_SITE_URL ?? "";
const DEFAULT_IMAGE = "/globe.svg";

export async function generateMetadata({ params }: { params: { id: string } }): Promise<Metadata> {
  try {
    const circle = await fetchGetCirclesId({ pathParams: { id: params.id } });

    const title = `${circle.name} | Ayafeed`;
    const description = `社团 ${circle.name} 的详细信息与参展记录`;
    
    // 解析 URLs 获取 logo
    let image = DEFAULT_IMAGE;
    try {
      const urls = JSON.parse(circle.urls || "{}");
      image = urls.logo_url || DEFAULT_IMAGE;
    } catch (_) {
      // 使用默认图片
    }
    
    const url = `${SITE_URL}/circles/${params.id}`;

    return {
      title,
      description,
      alternates: { canonical: url },
      openGraph: {
        title,
        description,
        url,
        type: "article",
        images: [{ url: image, alt: circle.name }],
      },
      twitter: {
        card: "summary_large_image",
        title,
        description,
        images: [image],
      },
    };
  } catch (_) {
    // fall back default
    return {
      title: "社团详情 | Ayafeed",
      description: "同人社团信息与参展记录",
    };
  }
} 