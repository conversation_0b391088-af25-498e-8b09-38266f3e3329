"use client";

import { useState } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import VenueSelector from "@/components/admin/VenueSelector";

export default function EventVenueTestPage() {
  const [selectedVenueId, setSelectedVenueId] = useState("");
  const [testEventData, setTestEventData] = useState({
    name_en: "Test Event",
    name_ja: "テストイベント",
    name_zh: "测试活动",
    date_en: "March 15, 2025 (Sat) 10:00 - 18:00",
    date_ja: "2025年3月15日(土) 10:00 - 18:00",
    date_zh: "2025年3月15日(周六) 10:00 - 18:00",
    venue_id: "",
    image_url: "/images/events/test-event/thumb.jpg",
    url: "https://test-event.com",
  });

  const handleVenueChange = (venueId: string) => {
    setSelectedVenueId(venueId);
    setTestEventData({ ...testEventData, venue_id: venueId });
  };

  const handleSubmitTest = () => {
    console.log("Test Event Data:", testEventData);
    alert("测试数据已输出到控制台，请查看开发者工具");
  };

  return (
    <div className="space-y-6 p-6">
      <div>
        <h1 className="text-2xl font-bold">Event-Venue 集成测试</h1>
        <p className="text-muted-foreground">测试event表单中的venue选择器功能</p>
      </div>

      {/* Event表单测试 */}
      <Card>
        <CardHeader>
          <CardTitle>Event表单测试</CardTitle>
          <CardDescription>模拟event创建/编辑表单中的venue选择</CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          {/* 基本Event信息 */}
          <div className="grid grid-cols-3 gap-4">
            <div>
              <Label htmlFor="name_en">英文名称</Label>
              <Input
                id="name_en"
                value={testEventData.name_en}
                onChange={(e) => setTestEventData({ ...testEventData, name_en: e.target.value })}
              />
            </div>
            <div>
              <Label htmlFor="name_ja">日文名称</Label>
              <Input
                id="name_ja"
                value={testEventData.name_ja}
                onChange={(e) => setTestEventData({ ...testEventData, name_ja: e.target.value })}
              />
            </div>
            <div>
              <Label htmlFor="name_zh">中文名称</Label>
              <Input
                id="name_zh"
                value={testEventData.name_zh}
                onChange={(e) => setTestEventData({ ...testEventData, name_zh: e.target.value })}
              />
            </div>
          </div>

          {/* Venue选择器 */}
          <div>
            <Label>展会场馆 *</Label>
            <VenueSelector
              value={selectedVenueId}
              onValueChange={handleVenueChange}
              placeholder="选择展会场馆..."
            />
          </div>

          {/* 其他字段 */}
          <div className="grid grid-cols-2 gap-4">
            <div>
              <Label htmlFor="image_url">封面图路径</Label>
              <Input
                id="image_url"
                value={testEventData.image_url}
                onChange={(e) => setTestEventData({ ...testEventData, image_url: e.target.value })}
                placeholder="/images/events/event-name/thumb.jpg"
              />
              <p className="text-xs text-muted-foreground mt-1">
                输入相对路径，系统会自动拼接完整URL
              </p>
            </div>
            <div>
              <Label htmlFor="url">官网URL</Label>
              <Input
                id="url"
                value={testEventData.url}
                onChange={(e) => setTestEventData({ ...testEventData, url: e.target.value })}
                placeholder="https://example.com"
              />
            </div>
          </div>

          <Button onClick={handleSubmitTest} className="w-full">
            测试提交（输出到控制台）
          </Button>
        </CardContent>
      </Card>

      {/* 当前数据预览 */}
      <Card>
        <CardHeader>
          <CardTitle>当前表单数据</CardTitle>
          <CardDescription>实时显示表单数据，验证venue_id是否正确设置</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-2">
            <div className="flex items-center gap-2">
              <span className="font-medium">选中的Venue ID:</span>
              <span className="font-mono bg-muted px-2 py-1 rounded">
                {selectedVenueId || "未选择"}
              </span>
            </div>
            <div className="flex items-center gap-2">
              <span className="font-medium">表单中的venue_id:</span>
              <span className="font-mono bg-muted px-2 py-1 rounded">
                {testEventData.venue_id || "未设置"}
              </span>
            </div>
            <div className="flex items-center gap-2">
              <span className="font-medium">数据一致性:</span>
              <span className={`px-2 py-1 rounded text-sm ${
                selectedVenueId === testEventData.venue_id 
                  ? "bg-green-100 text-green-800" 
                  : "bg-red-100 text-red-800"
              }`}>
                {selectedVenueId === testEventData.venue_id ? "✅ 一致" : "❌ 不一致"}
              </span>
            </div>
          </div>

          <div className="mt-4 p-4 bg-muted rounded">
            <h4 className="font-medium mb-2">完整表单数据:</h4>
            <pre className="text-xs overflow-auto">
              {JSON.stringify(testEventData, null, 2)}
            </pre>
          </div>
        </CardContent>
      </Card>

      {/* 功能测试清单 */}
      <Card>
        <CardHeader>
          <CardTitle>功能测试清单</CardTitle>
          <CardDescription>验证venue选择器的各项功能</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-2">
            <div className="flex items-center gap-2">
              <input type="checkbox" id="test1" />
              <label htmlFor="test1" className="text-sm">
                ✅ Venue选择器正常显示
              </label>
            </div>
            <div className="flex items-center gap-2">
              <input type="checkbox" id="test2" />
              <label htmlFor="test2" className="text-sm">
                🔍 搜索功能正常工作
              </label>
            </div>
            <div className="flex items-center gap-2">
              <input type="checkbox" id="test3" />
              <label htmlFor="test3" className="text-sm">
                📋 venue列表正常加载
              </label>
            </div>
            <div className="flex items-center gap-2">
              <input type="checkbox" id="test4" />
              <label htmlFor="test4" className="text-sm">
                ✅ 选择venue后venue_id正确设置
              </label>
            </div>
            <div className="flex items-center gap-2">
              <input type="checkbox" id="test5" />
              <label htmlFor="test5" className="text-sm">
                📍 选中venue的详细信息正常显示
              </label>
            </div>
            <div className="flex items-center gap-2">
              <input type="checkbox" id="test6" />
              <label htmlFor="test6" className="text-sm">
                ➕ 创建新场馆 链接正常工作
              </label>
            </div>
            <div className="flex items-center gap-2">
              <input type="checkbox" id="test7" />
              <label htmlFor="test7" className="text-sm">
                ✏️  编辑 按钮正常工作
              </label>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
