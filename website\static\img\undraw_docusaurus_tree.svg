<svg width="200" height="200" viewBox="0 0 200 200" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <linearGradient id="tree-grad" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#228B22;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#32CD32;stop-opacity:1" />
    </linearGradient>
  </defs>
  
  <!-- 树干 -->
  <rect x="90" y="120" width="20" height="60" fill="#8B4513" />
  
  <!-- 树叶 -->
  <circle cx="100" cy="80" r="40" fill="url(#tree-grad)" />
  <circle cx="80" cy="90" r="30" fill="url(#tree-grad)" opacity="0.8" />
  <circle cx="120" cy="90" r="30" fill="url(#tree-grad)" opacity="0.8" />
  
  <!-- 地面 -->
  <ellipse cx="100" cy="180" rx="60" ry="10" fill="#8FBC8F" opacity="0.6" />
  
  <!-- 装饰 -->
  <circle cx="85" cy="75" r="3" fill="#FFD700" />
  <circle cx="115" cy="85" r="3" fill="#FFD700" />
  <circle cx="100" cy="65" r="3" fill="#FFD700" />
</svg>
