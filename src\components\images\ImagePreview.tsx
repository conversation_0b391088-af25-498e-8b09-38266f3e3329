/**
 * ImagePreview 图片预览组件
 * 全屏图片预览，支持导航、缩放和操作
 */

'use client';

import React, { useState, useEffect } from 'react';
import { X, ChevronLeft, ChevronRight, Download, Trash2, ZoomIn, ZoomOut } from 'lucide-react';
import { cn } from '@/lib/utils';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { ImageService } from '@/services/imageService';
import type { ImagePreviewProps } from '@/types/image';

export function ImagePreview({
  isOpen,
  images,
  currentIndex,
  onClose,
  onNext,
  onPrevious,
  onDelete,
  showActions = true,
}: ImagePreviewProps) {
  const [zoom, setZoom] = useState(1);
  const [imageLoaded, setImageLoaded] = useState(false);

  const currentImage = images[currentIndex];
  const hasMultiple = images.length > 1;
  const canGoNext = hasMultiple && currentIndex < images.length - 1;
  const canGoPrevious = hasMultiple && currentIndex > 0;

  // 重置状态当图片改变时
  useEffect(() => {
    setZoom(1);
    setImageLoaded(false);
  }, [currentIndex]);

  // 键盘导航
  useEffect(() => {
    if (!isOpen) return;

    const handleKeyDown = (e: KeyboardEvent) => {
      switch (e.key) {
        case 'Escape':
          onClose();
          break;
        case 'ArrowLeft':
          if (canGoPrevious) onPrevious?.();
          break;
        case 'ArrowRight':
          if (canGoNext) onNext?.();
          break;
        case '+':
        case '=':
          setZoom(prev => Math.min(prev * 1.2, 5));
          break;
        case '-':
          setZoom(prev => Math.max(prev / 1.2, 0.1));
          break;
        case '0':
          setZoom(1);
          break;
      }
    };

    document.addEventListener('keydown', handleKeyDown);
    return () => document.removeEventListener('keydown', handleKeyDown);
  }, [isOpen, canGoNext, canGoPrevious, onClose, onNext, onPrevious]);

  // 格式化文件大小
  const formatFileSize = (bytes: number): string => {
    if (bytes === 0) return '0 B';
    const k = 1024;
    const sizes = ['B', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(1)) + ' ' + sizes[i];
  };

  // 下载图片
  const handleDownload = () => {
    if (!currentImage) return;
    const url = ImageService.getUrl(currentImage.relativePath);
    const link = document.createElement('a');
    link.href = url;
    link.download = currentImage.relativePath.split('/').pop() || 'image';
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  };

  // 删除图片
  const handleDelete = () => {
    if (!currentImage) return;
    onDelete?.(currentImage);
  };

  if (!isOpen || !currentImage) {
    return null;
  }

  const imageUrl = ImageService.getUrl(currentImage.relativePath);

  // 如果URL生成失败，显示错误
  if (!imageUrl) {
    return (
      <div className="fixed inset-0 z-50 bg-black/90 flex items-center justify-center">
        <div className="text-white text-center">
          <div className="text-6xl mb-4">❌</div>
          <h3 className="text-lg font-medium mb-2">无法加载图片</h3>
          <p className="text-gray-300 mb-4">图片URL生成失败</p>
          <Button variant="outline" onClick={onClose}>
            关闭
          </Button>
        </div>
      </div>
    );
  }

  return (
    <div className="fixed inset-0 z-50 bg-black/90 flex items-center justify-center">
      {/* 背景点击关闭 */}
      <div
        className="absolute inset-0"
        onClick={onClose}
      />

      {/* 顶部工具栏 */}
      <div className="absolute top-0 left-0 right-0 z-10 p-4">
        <div className="flex items-center justify-between">
          {/* 图片信息 */}
          <div className="flex items-center gap-3 text-white">
            <div className="text-lg font-medium">
              {currentIndex + 1} of {images.length}
            </div>
            <div className="flex gap-2">
              <Badge variant="secondary">
                {currentImage.variant}
              </Badge>
              <Badge variant="outline" className="text-white border-white/20">
                {currentImage.metadata.format.toUpperCase()}
              </Badge>
            </div>
          </div>

          {/* 操作按钮 */}
          <div className="flex items-center gap-2">
            {/* 缩放控制 */}
            <div className="flex items-center gap-1 bg-black/50 rounded-lg p-1">
              <Button
                variant="ghost"
                size="sm"
                onClick={() => setZoom(prev => Math.max(prev / 1.2, 0.1))}
                className="text-white hover:bg-white/20"
              >
                <ZoomOut className="h-4 w-4" />
              </Button>
              <span className="text-white text-sm px-2 min-w-[3rem] text-center">
                {Math.round(zoom * 100)}%
              </span>
              <Button
                variant="ghost"
                size="sm"
                onClick={() => setZoom(prev => Math.min(prev * 1.2, 5))}
                className="text-white hover:bg-white/20"
              >
                <ZoomIn className="h-4 w-4" />
              </Button>
            </div>

            {showActions && (
              <>
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={handleDownload}
                  className="text-white hover:bg-white/20"
                >
                  <Download className="h-4 w-4" />
                </Button>
                {onDelete && (
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={handleDelete}
                    className="text-white hover:bg-red-500/20"
                  >
                    <Trash2 className="h-4 w-4" />
                  </Button>
                )}
              </>
            )}

            <Button
              variant="ghost"
              size="sm"
              onClick={onClose}
              className="text-white hover:bg-white/20"
            >
              <X className="h-4 w-4" />
            </Button>
          </div>
        </div>
      </div>

      {/* 导航按钮 */}
      {hasMultiple && (
        <>
          {canGoPrevious && (
            <Button
              variant="ghost"
              size="lg"
              onClick={onPrevious}
              className="absolute left-4 top-1/2 -translate-y-1/2 z-10 text-white hover:bg-white/20"
            >
              <ChevronLeft className="h-8 w-8" />
            </Button>
          )}
          {canGoNext && (
            <Button
              variant="ghost"
              size="lg"
              onClick={onNext}
              className="absolute right-4 top-1/2 -translate-y-1/2 z-10 text-white hover:bg-white/20"
            >
              <ChevronRight className="h-8 w-8" />
            </Button>
          )}
        </>
      )}

      {/* 图片容器 */}
      <div className="relative z-10 max-w-full max-h-full p-16">
        <div
          className="relative transition-transform duration-200 ease-out"
          style={{ transform: `scale(${zoom})` }}
        >
          <img
            src={imageUrl}
            alt="Preview"
            className={cn(
              'max-w-full max-h-full object-contain transition-opacity duration-200',
              imageLoaded ? 'opacity-100' : 'opacity-0'
            )}
            onLoad={() => setImageLoaded(true)}
            onDoubleClick={() => setZoom(zoom === 1 ? 2 : 1)}
          />
          
          {/* 加载状态 */}
          {!imageLoaded && (
            <div className="absolute inset-0 flex items-center justify-center">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-white"></div>
            </div>
          )}
        </div>
      </div>

      {/* 底部信息 */}
      <div className="absolute bottom-0 left-0 right-0 z-10 p-4">
        <div className="text-center text-white/80 text-sm space-y-1">
          <div>
            {currentImage.metadata.dimensions.width} × {currentImage.metadata.dimensions.height} • {formatFileSize(currentImage.metadata.size)}
          </div>
          <div className="truncate max-w-md mx-auto">
            {currentImage.relativePath}
          </div>
          <div className="text-xs text-white/60">
            Use arrow keys to navigate • Double-click to zoom • Press ESC to close
          </div>
        </div>
      </div>
    </div>
  );
}
