name: Issue Automation

on:
  issues:
    types: [opened, edited, labeled, unlabeled]

jobs:
  sync_with_project:
    runs-on: ubuntu-latest
    permissions:
      issues: write
      repository-projects: write
    steps:
      - uses: actions/checkout@v4
      
      - name: Get issue data
        id: issue_data
        uses: actions/github-script@v7
        with:
          script: |
            const issue = context.payload.issue;
            const body = issue.body;
            
            // 解析 issue body 中的字段
            const epic = body.match(/### Epic\n\n(.+)/m)?.[1];
            const priority = body.match(/### Priority\n\n(.+)/m)?.[1];
            const status = body.match(/### Status\n\n(.+)/m)?.[1];
            
            return {
              epic,
              priority,
              status,
              issue_number: issue.number
            };

      - name: Sync with project
        uses: actions/github-script@v7
        with:
          github-token: ${{ secrets.PROJECT_TOKEN }}
          script: |
            const issueData = ${{ steps.issue_data.outputs.result }};
            
            // 获取项目中的 item
            const query = `query {
              user(login: "Linear27") {
                projectV2(number: 1) {
                  items(first: 100) {
                    nodes {
                      id
                      content {
                        ... on Issue {
                          number
                        }
                      }
                    }
                  }
                }
              }
            }`;
            
            const result = await github.graphql(query);
            const items = result.user.projectV2.items.nodes;
            const item = items.find(item => item.content?.number === issueData.issue_number);
            
            if (!item) {
              console.log('Issue not found in project');
              return;
            }
            
            const itemId = item.id;
            
            // 更新项目字段
            if (issueData.epic) {
              await github.graphql(`
                mutation($itemId: ID!, $value: String!) {
                  updateProjectV2ItemFieldValue(
                    input: {
                      projectId: "PVT_lAHOAnRWIs4A-NvDzgxr5bU"
                      itemId: $itemId
                      fieldId: "PVTSSF_lAHOAnRWIs4A-NvDzgx9a-g"
                      value: { singleSelectOptionId: $value }
                    }
                  ) {
                    projectV2Item {
                      id
                    }
                  }
                }
              `, {
                itemId: itemId,
                value: getEpicOptionId(issueData.epic)
              });
            }
            
            if (issueData.priority) {
              await github.graphql(`
                mutation($itemId: ID!, $value: String!) {
                  updateProjectV2ItemFieldValue(
                    input: {
                      projectId: "PVT_lAHOAnRWIs4A-NvDzgxr5bU"
                      itemId: $itemId
                      fieldId: "PVTSSF_lAHOAnRWIs4A-NvDzgxr6i4"
                      value: { singleSelectOptionId: $value }
                    }
                  ) {
                    projectV2Item {
                      id
                    }
                  }
                }
              `, {
                itemId: itemId,
                value: getPriorityOptionId(issueData.priority)
              });
            }
            
            if (issueData.status) {
              await github.graphql(`
                mutation($itemId: ID!, $value: String!) {
                  updateProjectV2ItemFieldValue(
                    input: {
                      projectId: "PVT_lAHOAnRWIs4A-NvDzgxr5bU"
                      itemId: $itemId
                      fieldId: "PVTSSF_lAHOAnRWIs4A-NvDzgxr5bc"
                      value: { singleSelectOptionId: $value }
                    }
                  ) {
                    projectV2Item {
                      id
                    }
                  }
                }
              `, {
                itemId: itemId,
                value: getStatusOptionId(issueData.status)
              });
            }

            // 辅助函数：获取 Epic 选项 ID
            function getEpicOptionId(epicName) {
              const epicOptions = {
                'auth': 'f1c89691',
                'feed': '649db84d',
                'search': 'd6a34cb0',
                'subscription': 'e56074f8',
                'perf': '67034c7e',
                'i18n': '1495c76c',
                'tests': 'ac5722da',
                'responsive': '98da999d',
                'pwa': '6d7d0410',
                'ops': '3d125758'
              };
              return epicOptions[epicName] || null;
            }

            // 辅助函数：获取 Priority 选项 ID
            function getPriorityOptionId(priority) {
              const priorityOptions = {
                'P0': '79628723',
                'P1': '0a877460',
                'P2': 'da944a9c',
                'P3': 'e6d4c2d0'
              };
              return priorityOptions[priority] || null;
            }

            // 辅助函数：获取 Status 选项 ID
            function getStatusOptionId(status) {
              const statusOptions = {
                'Backlog': 'f75ad846',
                'Ready': '61e4505c',
                'In Progress': '47fc9ee4',
                'In Review': 'df73e18b',
                'Done': '98236657'
              };
              return statusOptions[status] || null;
            }

      - name: Add type label
        if: steps.issue_data.outputs.result.type
        uses: actions/github-script@v7
        with:
          script: |
            const issueData = ${{ steps.issue_data.outputs.result }};
            const type = issueData.type;
            
            await github.rest.issues.addLabels({
              owner: context.repo.owner,
              repo: context.repo.repo,
              issue_number: issueData.issue_number,
              labels: [`type:${type}`]
            }); 