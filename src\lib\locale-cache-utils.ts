/**
 * 多语言缓存管理工具
 * 
 * 提供智能的多语言数据缓存策略，避免语言切换时的重复请求
 */

import { QueryClient } from '@tanstack/react-query';
import type { Locale } from './locale-utils';

/**
 * 查询键模式定义
 * 定义哪些查询键包含语言相关的数据
 */
export const LOCALE_SENSITIVE_QUERY_PATTERNS = [
  // 直接包含语言代码的查询键
  'events',
  'circles', 
  'feed',
  'search',
  // 包含 locale 字段的查询键
  'locale',
  'lang',
  'i18n'
] as const;

/**
 * 检查查询键是否与语言相关
 * 现在所有API查询都会包含locale参数，所以检查更简单
 */
export function isLocaleSensitiveQuery(queryKey: unknown[]): boolean {
  if (!Array.isArray(queryKey)) return false;

  return queryKey.some(key => {
    // 检查对象类型的键中是否包含 locale 字段
    if (typeof key === 'object' && key !== null) {
      const obj = key as Record<string, unknown>;
      return 'locale' in obj;
    }

    // 向后兼容：检查字符串类型的键
    if (typeof key === 'string') {
      return LOCALE_SENSITIVE_QUERY_PATTERNS.some(pattern =>
        key.includes(pattern)
      );
    }

    return false;
  });
}

/**
 * 检查查询键是否属于特定语言
 * 现在主要检查查询参数对象中的locale字段
 */
export function isQueryForLocale(queryKey: unknown[], targetLocale: Locale): boolean {
  if (!Array.isArray(queryKey)) return false;

  return queryKey.some(key => {
    // 检查对象中的 locale 字段（主要方式）
    if (typeof key === 'object' && key !== null) {
      const obj = key as Record<string, unknown>;
      return obj.locale === targetLocale;
    }

    // 向后兼容：检查字符串中是否包含目标语言
    if (typeof key === 'string') {
      return key.includes(targetLocale);
    }

    return false;
  });
}

/**
 * 智能语言切换缓存管理
 */
export class LocaleCacheManager {
  constructor(private queryClient: QueryClient) {}

  /**
   * 语言切换时的智能缓存处理
   * @param fromLocale 切换前的语言
   * @param toLocale 切换后的语言
   */
  handleLocaleSwitch(fromLocale: Locale, toLocale: Locale) {
    console.log(`🌐 语言切换: ${fromLocale} → ${toLocale}`);

    // 由于现在查询键包含locale信息，不同语言的数据会自动隔离
    // 我们只需要确保新语言的查询会被触发
    console.log(`✅ 语言切换完成，新的API请求将自动使用 locale=${toLocale}`);

    // 可选：预取新语言的关键数据
    // this.prefetchCriticalData(toLocale);
  }

  /**
   * 失效当前语言的查询
   */
  private invalidateCurrentLocaleQueries(locale: Locale) {
    const invalidatedCount = this.queryClient.invalidateQueries({
      predicate: (query) => {
        const isLocaleSensitive = isLocaleSensitiveQuery(query.queryKey);
        const isCurrentLocale = isQueryForLocale(query.queryKey, locale);
        
        // 只失效与当前语言相关的查询
        const shouldInvalidate = isLocaleSensitive && isCurrentLocale;
        
        if (shouldInvalidate) {
          console.log(`🗑️ 失效查询:`, query.queryKey);
        }
        
        return shouldInvalidate;
      }
    });
    
    console.log(`📊 失效了 ${invalidatedCount} 个查询`);
  }

  /**
   * 预取新语言的关键数据（可选功能）
   */
  private async prefetchCriticalData(locale: Locale) {
    try {
      // 这里可以预取新语言的关键数据
      // 例如：导航菜单、常用标签等
      console.log(`🚀 预取 ${locale} 语言的关键数据`);
      
      // 示例：预取事件列表
      // await this.queryClient.prefetchQuery({
      //   queryKey: ['events', { locale }],
      //   queryFn: () => fetchEvents(locale)
      // });
      
    } catch (error) {
      console.warn('预取数据失败:', error);
    }
  }

  /**
   * 获取缓存统计信息
   */
  getCacheStats() {
    const cache = this.queryClient.getQueryCache();
    const queries = cache.getAll();
    
    const stats = {
      total: queries.length,
      byLocale: {} as Record<string, number>,
      localeSensitive: 0,
      localeAgnostic: 0
    };
    
    queries.forEach(query => {
      const isLocaleSensitive = isLocaleSensitiveQuery(query.queryKey);
      
      if (isLocaleSensitive) {
        stats.localeSensitive++;
        
        // 统计各语言的查询数量
        ['zh', 'ja', 'en'].forEach(locale => {
          if (isQueryForLocale(query.queryKey, locale as Locale)) {
            stats.byLocale[locale] = (stats.byLocale[locale] || 0) + 1;
          }
        });
      } else {
        stats.localeAgnostic++;
      }
    });
    
    return stats;
  }

  /**
   * 清理特定语言的所有缓存（谨慎使用）
   */
  clearLocaleCache(locale: Locale) {
    console.warn(`🧹 清理 ${locale} 语言的所有缓存`);
    
    this.queryClient.removeQueries({
      predicate: (query) => isQueryForLocale(query.queryKey, locale)
    });
  }

  /**
   * 清理所有语言相关的缓存（紧急情况使用）
   */
  clearAllLocaleCache() {
    console.warn('🧹 清理所有语言相关的缓存');
    
    this.queryClient.removeQueries({
      predicate: (query) => isLocaleSensitiveQuery(query.queryKey)
    });
  }
}

/**
 * 创建语言缓存管理器实例
 */
export function createLocaleCacheManager(queryClient: QueryClient) {
  return new LocaleCacheManager(queryClient);
}

/**
 * 开发环境下的缓存调试工具
 */
export function debugLocaleCache(queryClient: QueryClient) {
  if (process.env.NODE_ENV !== 'development') return;
  
  const manager = new LocaleCacheManager(queryClient);
  const stats = manager.getCacheStats();
  
  console.group('🔍 语言缓存调试信息');
  console.log('总查询数:', stats.total);
  console.log('语言相关查询:', stats.localeSensitive);
  console.log('语言无关查询:', stats.localeAgnostic);
  console.log('各语言查询分布:', stats.byLocale);
  console.groupEnd();
  
  // 将管理器暴露到全局，方便调试
  (window as any).__localeCacheManager = manager;
}
