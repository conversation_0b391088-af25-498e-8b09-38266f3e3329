"use client";

import { useState, useCallback, useEffect } from "react";
import { useGetCircles } from "@/api/generated/ayafeedComponents";

// 使用 API 生成的类型
type Circle = any; // 暂时使用 any，避免类型冲突

interface UseInfiniteCirclesOptions {
  pageSize?: number;
  searchKeyword?: string;
}

export function useInfiniteCircles({
  pageSize = 50,
  searchKeyword = ""
}: UseInfiniteCirclesOptions = {}) {
  const [allCircles, setAllCircles] = useState<Circle[]>([]);
  const [page, setPage] = useState(1);
  const [hasMore, setHasMore] = useState(true);
  const [isLoadingMore, setIsLoadingMore] = useState(false);
  const [isSearching, setIsSearching] = useState(false);
  const [debouncedSearchKeyword, setDebouncedSearchKeyword] = useState(searchKeyword);

  // 防抖处理搜索关键词
  useEffect(() => {
    const timer = setTimeout(() => {
      setDebouncedSearchKeyword(searchKeyword);
    }, 300);

    return () => clearTimeout(timer);
  }, [searchKeyword]);

  // 获取当前页数据
  const {
    data: circlesData,
    isLoading: isInitialLoading,
    error,
    refetch
  } = useGetCircles(
    {
      queryParams: {
        page: page.toString(),
        pageSize: pageSize.toString(),
        // 现在支持搜索参数了！
        ...(debouncedSearchKeyword && { search: debouncedSearchKeyword }),
      },
    },
    {
      staleTime: 5 * 60 * 1000, // 5分钟缓存
      enabled: true,
      refetchOnMount: true, // 确保组件挂载时重新获取
      refetchOnWindowFocus: false, // 避免窗口聚焦时重复请求
    }
  );



  // 当防抖后的搜索词变化时重新获取数据（但不清空当前数据）
  useEffect(() => {
    // 标记正在搜索
    setIsSearching(true);
    setPage(1);
    setHasMore(true);
    setIsLoadingMore(false);
    // 重新获取数据，但保持当前数据直到新数据到达
    refetch();
  }, [debouncedSearchKeyword, refetch]);



  // 简化的无限滚动实现

  // 当新数据到达时更新状态
  useEffect(() => {
    if (circlesData?.items) {
      const newItems = circlesData.items;

      if (page === 1) {
        // 第一页或搜索重置时，替换所有数据
        setAllCircles(newItems);
        // 搜索完成，清除搜索状态
        setIsSearching(false);
      } else {
        // 后续页面，追加数据
        setAllCircles(prev => {
          // 去重处理，避免重复数据
          const existingIds = new Set(prev.map(item => item.id));
          const uniqueNewItems = newItems.filter(item => !existingIds.has(item.id));
          return [...prev, ...uniqueNewItems];
        });
      }

      // 判断是否还有更多数据
      setHasMore(newItems.length === pageSize);
      setIsLoadingMore(false);
    }
  }, [circlesData, page, pageSize]);

  // 加载更多数据
  const loadMore = useCallback(() => {
    if (!hasMore || isLoadingMore || isInitialLoading) {
      return;
    }

    setIsLoadingMore(true);
    setPage(prev => prev + 1);
  }, [hasMore, isLoadingMore, isInitialLoading]);





  // 现在使用服务端搜索，不需要客户端筛选
  const filteredCircles = allCircles;

  return {
    circles: filteredCircles,
    allCircles,
    isLoading: isInitialLoading && page === 1 && allCircles.length === 0, // 只有初始加载且没有数据时才显示loading
    isSearching, // 新增：是否正在搜索
    isLoadingMore,
    hasMore,
    error,
    loadMore,
    total: circlesData?.total || 0,
  };
}
