/**
 * 事件详情页面导航工具函数
 */

/**
 * 切换到会场信息标签页并滚动到地图模块
 */
export const scrollToVenueMap = () => {
  const venueTab = document.querySelector('[data-value="venue"]') as HTMLElement
  if (venueTab) {
    venueTab.click()
    // 等待标签页切换完成后滚动到地图模块
    setTimeout(() => {
      const mapSection = document.getElementById('venue-map-section')
      if (mapSection) {
        // 计算滚动位置，留出顶部导航空间
        const offsetTop = mapSection.offsetTop - 120
        window.scrollTo({
          top: Math.max(0, offsetTop), // 确保不会滚动到负数位置
          behavior: 'smooth'
        })
      }
    }, 250) // 稍微增加延迟确保DOM更新完成
  }
}

/**
 * 切换到指定标签页
 */
export const switchToTab = (tabValue: string) => {
  const tab = document.querySelector(`[data-value="${tabValue}"]`) as HTMLElement
  if (tab) {
    tab.click()
  }
}

/**
 * 切换到指定标签页并滚动到指定元素
 */
export const switchToTabAndScroll = (tabValue: string, elementId: string, offset = 120) => {
  const tab = document.querySelector(`[data-value="${tabValue}"]`) as HTMLElement
  if (tab) {
    tab.click()
    setTimeout(() => {
      const element = document.getElementById(elementId)
      if (element) {
        const offsetTop = element.offsetTop - offset
        window.scrollTo({
          top: Math.max(0, offsetTop),
          behavior: 'smooth'
        })
      }
    }, 250)
  }
}
