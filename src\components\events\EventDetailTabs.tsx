'use client'

import { useState } from 'react'
import * as Tabs from "@radix-ui/react-tabs";
import { MapPin, Clock, Car, Bed, Calendar, Users, Info } from 'lucide-react'
import { cn } from "@/lib/utils"
import { RadixBadge, RadixCard, RadixCardHeader, RadixCardTitle, RadixCardDescription, RadixCardContent } from '@/components/ui/radix-components'

import CirclesGrid from './CirclesGrid'
import EnhancedFilterBar from './EnhancedFilterBar'
import EventMap from './EventMap'
import { OverviewTabSkeleton, VenueTabSkeleton, TravelTabSkeleton } from './EnhancedSkeleton'
import { createVenueFromEvent } from './types'
import { scrollToVenueMap, switchToTab } from './navigationUtils'
import type { Event } from '@/schemas/event'

interface EventDetailTabsProps {
  event: Event | null
  circles: any[]
  filteredCircles: any[]
  keyword: string
  setKeyword: (keyword: string) => void
  categories: string[]
  setCategories: (categories: string[]) => void
  toggleCategory: (category: string) => void
  categoryOptions: { id: string; label: string }[]
  isLoading: boolean
}

export default function EventDetailTabs({
  event,
  circles,
  filteredCircles,
  keyword,
  setKeyword,
  categories,
  setCategories,
  toggleCategory,
  categoryOptions,
  isLoading
}: EventDetailTabsProps) {
  const [viewMode, setViewMode] = useState<'grid' | 'list' | 'map'>('grid')
  const [sortBy, setSortBy] = useState('name')
  const venue = createVenueFromEvent(event)

  // 通用的 Tabs.Trigger 样式
  const triggerClassName = "inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-sm px-3 py-1.5 text-sm font-medium ring-offset-background transition-all focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 data-[state=active]:bg-background data-[state=active]:text-foreground data-[state=active]:shadow-sm"

  return (
    <Tabs.Root defaultValue="exhibitors" className="w-full">
      <Tabs.List className="grid w-full grid-cols-4 h-10 items-center justify-center rounded-md bg-muted p-1 text-muted-foreground">
        <Tabs.Trigger value="overview" className={triggerClassName}>
          <Info className="h-4 w-4" />
          概览
        </Tabs.Trigger>
        <Tabs.Trigger value="exhibitors" className={triggerClassName}>
          <Users className="h-4 w-4" />
          参展商 ({filteredCircles.length})
        </Tabs.Trigger>
        <Tabs.Trigger value="venue" className={triggerClassName}>
          <MapPin className="h-4 w-4" />
          会场信息
        </Tabs.Trigger>
        <Tabs.Trigger value="travel" className={triggerClassName}>
          <Car className="h-4 w-4" />
          交通住宿
        </Tabs.Trigger>
      </Tabs.List>

      <Tabs.Content value="overview" className="space-y-6 mt-2 ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2">
        {isLoading ? (
          <OverviewTabSkeleton />
        ) : (
          <OverviewTab event={event} circlesCount={circles.length} />
        )}
      </Tabs.Content>

      <Tabs.Content value="exhibitors" className="space-y-6 mt-2 ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2">
        <EnhancedFilterBar
          keyword={keyword}
          setKeyword={setKeyword}
          categories={categories}
          setCategories={setCategories}
          toggleCategory={toggleCategory}
          categoryOptions={categoryOptions}
          viewMode={viewMode}
          setViewMode={setViewMode}
          sortBy={sortBy}
          setSortBy={setSortBy}
          total={filteredCircles.length}
        />

        {isLoading ? (
          <div className="flex items-center justify-center py-12">
            <div className="text-center">
              <div className="loading-spinner rounded-full h-8 w-8 border-b-2 border-primary mx-auto mb-2"></div>
              <p className="text-muted-foreground loading-pulse">加载中...</p>
            </div>
          </div>
        ) : (
          <CirclesGrid data={filteredCircles} />
        )}
      </Tabs.Content>

      <Tabs.Content value="venue" className="space-y-6 mt-2 ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2">
        {isLoading ? (
          <VenueTabSkeleton />
        ) : (
          <VenueTab event={event} venue={venue} />
        )}
      </Tabs.Content>

      <Tabs.Content value="travel" className="space-y-6 mt-2 ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2">
        {isLoading ? (
          <TravelTabSkeleton />
        ) : (
          <TravelTab event={event} />
        )}
      </Tabs.Content>
    </Tabs.Root>
  )
}

function OverviewTab({ event, circlesCount }: { event: Event | null; circlesCount: number }) {
  if (!event) return null

  return (
    <div className="space-y-6">
      {/* 展会基本信息 */}
      <div className="grid gap-6 md:grid-cols-2">
        <RadixCard className="card-hover">
          <RadixCardHeader>
            <RadixCardTitle className="flex items-center gap-2">
              <Info className="h-5 w-5" />
              展会信息
            </RadixCardTitle>
          </RadixCardHeader>
          <RadixCardContent className="space-y-4">
            <div className="grid grid-cols-2 gap-4 text-sm">
              <div>
                <span className="text-muted-foreground">日期：</span>
                <span className="font-medium">{event.date}</span>
              </div>
              <div>
                <span className="text-muted-foreground">地点：</span>
                <span className="font-medium">{event.venue_name}</span>
              </div>
              <div>
                <span className="text-muted-foreground">参展商：</span>
                <span className="font-medium">{circlesCount}+</span>
              </div>
              <div>
                <span className="text-muted-foreground">状态：</span>
                <RadixBadge variant="secondary">即将举办</RadixBadge>
              </div>
            </div>
            {event.description && (
              <div>
                <h4 className="font-medium mb-2">展会介绍</h4>
                <p className="text-sm text-muted-foreground leading-relaxed">
                  {event.description}
                </p>
              </div>
            )}
          </RadixCardContent>
        </RadixCard>

        <RadixCard className="card-hover">
          <RadixCardHeader>
            <RadixCardTitle className="flex items-center gap-2">
              <Calendar className="h-5 w-5" />
              重要时间
            </RadixCardTitle>
          </RadixCardHeader>
          <RadixCardContent className="space-y-3">
            <div className="flex justify-between items-center">
              <span className="text-sm">开放时间</span>
              <span className="text-sm font-medium">10:00 - 18:00</span>
            </div>
            <div className="flex justify-between items-center">
              <span className="text-sm">入场截止</span>
              <span className="text-sm font-medium">17:30</span>
            </div>
            <div className="flex justify-between items-center">
              <span className="text-sm">预计参观时长</span>
              <span className="text-sm font-medium">3-5 小时</span>
            </div>
          </RadixCardContent>
        </RadixCard>
      </div>

      {/* 参观须知 */}
      <RadixCard className="card-hover">
        <RadixCardHeader>
          <RadixCardTitle className="flex items-center gap-2">
            <Info className="h-5 w-5" />
            参观须知
          </RadixCardTitle>
          <RadixCardDescription>
            为了确保您有最佳的参观体验，请仔细阅读以下注意事项
          </RadixCardDescription>
        </RadixCardHeader>
        <RadixCardContent>
          <div className="grid gap-4 md:grid-cols-2">
            <div>
              <h4 className="font-medium mb-2 text-green-600">✓ 允许携带</h4>
              <ul className="text-sm text-muted-foreground space-y-1">
                <li>• 小型背包或手提包</li>
                <li>• 相机（非商业用途）</li>
                <li>• 手机充电宝</li>
                <li>• 水杯（密封性好）</li>
              </ul>
            </div>
            <div>
              <h4 className="font-medium mb-2 text-red-600">✗ 禁止携带</h4>
              <ul className="text-sm text-muted-foreground space-y-1">
                <li>• 大型行李箱</li>
                <li>• 食物和饮料</li>
                <li>• 专业摄影设备</li>
                <li>• 宠物（导盲犬除外）</li>
              </ul>
            </div>
          </div>
          <div className="mt-4 p-3 bg-blue-50 rounded-lg border border-blue-200">
            <p className="text-sm text-blue-800">
              <strong>温馨提示：</strong>建议提前30分钟到达会场，避开入场高峰期。会场内提供免费WiFi和休息区域。
            </p>
          </div>
        </RadixCardContent>
      </RadixCard>

      {/* 快速导航 */}
      <RadixCard className="card-hover">
        <RadixCardHeader>
          <RadixCardTitle>快速导航</RadixCardTitle>
          <RadixCardDescription>
            点击下方按钮快速跳转到相关信息
          </RadixCardDescription>
        </RadixCardHeader>
        <RadixCardContent>
          <div className="grid grid-cols-2 md:grid-cols-3 gap-3">
            <button
              onClick={() => switchToTab('exhibitors')}
              className="flex items-center gap-2 p-3 rounded-lg border border-gray-200 hover:border-primary/50 hover:bg-primary/5 transition-colors text-left"
            >
              <Users className="h-4 w-4 text-primary" />
              <div>
                <div className="font-medium text-sm">参展商</div>
                <div className="text-xs text-muted-foreground">{circlesCount}+ 个展位</div>
              </div>
            </button>
            <button
              onClick={scrollToVenueMap}
              className="flex items-center gap-2 p-3 rounded-lg border border-gray-200 hover:border-primary/50 hover:bg-primary/5 transition-colors text-left"
            >
              <MapPin className="h-4 w-4 text-primary" />
              <div>
                <div className="font-medium text-sm">会场地图</div>
                <div className="text-xs text-muted-foreground">位置导航</div>
              </div>
            </button>
            <button
              onClick={() => switchToTab('travel')}
              className="flex items-center gap-2 p-3 rounded-lg border border-gray-200 hover:border-primary/50 hover:bg-primary/5 transition-colors text-left"
            >
              <Car className="h-4 w-4 text-primary" />
              <div>
                <div className="font-medium text-sm">交通住宿</div>
                <div className="text-xs text-muted-foreground">出行指南</div>
              </div>
            </button>
          </div>
        </RadixCardContent>
      </RadixCard>
    </div>
  )
}

function VenueTab({ event, venue }: { event: Event | null; venue: any }) {
  if (!event) return null

  return (
    <div className="space-y-6">
      {/* 会场基本信息 */}
      <div className="grid gap-6 md:grid-cols-2">
        <RadixCard className="card-hover">
          <RadixCardHeader>
            <RadixCardTitle className="flex items-center gap-2">
              <MapPin className="h-5 w-5" />
              会场详情
            </RadixCardTitle>
          </RadixCardHeader>
          <RadixCardContent className="space-y-4">
            <div>
              <h4 className="font-medium mb-2">会场名称</h4>
              <p className="text-sm text-muted-foreground">{event.venue_name}</p>
            </div>
            {event.venue_address && (
              <div>
                <h4 className="font-medium mb-2">详细地址</h4>
                <p className="text-sm text-muted-foreground">{event.venue_address}</p>
              </div>
            )}
            <div>
              <h4 className="font-medium mb-2">开放时间</h4>
              <p className="text-sm text-muted-foreground">10:00 - 18:00</p>
            </div>
            <div>
              <h4 className="font-medium mb-2">入场费用</h4>
              <p className="text-sm text-muted-foreground">免费入场</p>
            </div>
          </RadixCardContent>
        </RadixCard>

        <RadixCard className="card-hover">
          <RadixCardHeader>
            <RadixCardTitle className="flex items-center gap-2">
              <Clock className="h-5 w-5" />
              参观指南
            </RadixCardTitle>
          </RadixCardHeader>
          <RadixCardContent className="space-y-4">
            <div>
              <h4 className="font-medium mb-2">建议参观时长</h4>
              <p className="text-sm text-muted-foreground">3-5 小时</p>
            </div>
            <div>
              <h4 className="font-medium mb-2">最佳参观时间</h4>
              <p className="text-sm text-muted-foreground">上午 10:00-12:00（人流较少）</p>
            </div>
            <div>
              <h4 className="font-medium mb-2">注意事项</h4>
              <ul className="text-sm text-muted-foreground space-y-1">
                <li>• 请携带身份证件</li>
                <li>• 禁止携带大型行李</li>
                <li>• 会场内禁止吸烟</li>
              </ul>
            </div>
          </RadixCardContent>
        </RadixCard>
      </div>

      {/* 详细地图 */}
      <RadixCard id="venue-map-section" className="card-hover">
        <RadixCardHeader>
          <RadixCardTitle>详细位置地图</RadixCardTitle>
          <RadixCardDescription>
            点击地图可进行交互操作，支持缩放和拖拽
          </RadixCardDescription>
        </RadixCardHeader>
        <RadixCardContent>
          <EventMap
            venue={venue}
            className="h-[500px] rounded-lg overflow-hidden"
            isPreview={false}
          />
        </RadixCardContent>
      </RadixCard>
    </div>
  )
}

function TravelTab({ event }: { event: Event | null }) {
  if (!event) return null

  return (
    <div className="grid gap-6 md:grid-cols-2">
      <RadixCard className="card-hover">
        <RadixCardHeader>
          <RadixCardTitle className="flex items-center gap-2">
            <Car className="h-5 w-5" />
            交通指南
          </RadixCardTitle>
        </RadixCardHeader>
        <RadixCardContent className="space-y-4">
          <div>
            <h4 className="font-medium mb-2">公共交通</h4>
            <ul className="text-sm text-muted-foreground space-y-1">
              <li>• 地铁：最近车站步行约10分钟</li>
              <li>• 巴士：多条线路直达</li>
              <li>• 出租车：从市中心约30分钟</li>
            </ul>
          </div>
          <div>
            <h4 className="font-medium mb-2">自驾车</h4>
            <ul className="text-sm text-muted-foreground space-y-1">
              <li>• 停车场：会场提供付费停车</li>
              <li>• 费用：¥500/天</li>
              <li>• 建议：建议使用公共交通</li>
            </ul>
          </div>
        </RadixCardContent>
      </RadixCard>

      <RadixCard className="card-hover">
        <RadixCardHeader>
          <RadixCardTitle className="flex items-center gap-2">
            <Bed className="h-5 w-5" />
            住宿推荐
          </RadixCardTitle>
        </RadixCardHeader>
        <RadixCardContent className="space-y-4">
          <div>
            <h4 className="font-medium mb-2">附近酒店</h4>
            <ul className="text-sm text-muted-foreground space-y-1">
              <li>• 会场酒店：步行5分钟</li>
              <li>• 商务酒店：步行10分钟</li>
              <li>• 经济型酒店：地铁1站</li>
            </ul>
          </div>
          <div>
            <h4 className="font-medium mb-2">预订建议</h4>
            <ul className="text-sm text-muted-foreground space-y-1">
              <li>• 建议提前1个月预订</li>
              <li>• 展会期间房价可能上涨</li>
              <li>• 考虑周边城市住宿</li>
            </ul>
          </div>
        </RadixCardContent>
      </RadixCard>
    </div>
  )
}
