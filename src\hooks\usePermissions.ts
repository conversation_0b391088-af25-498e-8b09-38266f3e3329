/**
 * 权限管理Hook
 * 基于后端文档包的认证集成规范
 */

import { useAuth } from '@/contexts/user';
import type { UserRole } from '@/types/user';

export function usePermissions() {
  const { user } = useAuth();

  const permissions = {
    // 管理员权限
    canManageUsers: user?.role === 'admin',
    canManageEvents: ['admin', 'editor'].includes(user?.role || ''),
    canManageCircles: ['admin', 'editor'].includes(user?.role || ''),

    // 查看权限
    canViewStats: ['admin', 'editor', 'viewer'].includes(user?.role || ''),
    canViewLogs: user?.role === 'admin',
    canViewAdminPanel: ['admin', 'editor', 'viewer'].includes(user?.role || ''),

    // 基础功能
    canBookmark: !!user,
    canComment: !!user,
    canCreateContent: ['admin', 'editor'].includes(user?.role || ''),
    canEditOwnContent: !!user,
    canDeleteOwnContent: !!user,

    // 批量操作
    canBatchDelete: user?.role === 'admin',
    canBatchApprove: ['admin', 'editor'].includes(user?.role || ''),
  };

  return permissions;
}

// 角色权限检查工具函数
export function hasRole(userRole: UserRole | undefined, requiredRole: UserRole): boolean {
  if (!userRole) return false;

  const roleHierarchy: Record<UserRole, number> = {
    'admin': 3,
    'editor': 2,
    'viewer': 1,
  };

  return roleHierarchy[userRole] >= roleHierarchy[requiredRole];
}

// 检查是否有任一权限
export function hasAnyRole(userRole: UserRole | undefined, requiredRoles: UserRole[]): boolean {
  return requiredRoles.some(role => hasRole(userRole, role));
}
