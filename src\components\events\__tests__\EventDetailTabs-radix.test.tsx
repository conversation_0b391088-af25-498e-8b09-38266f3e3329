import { describe, test, expect, vi, beforeEach } from 'vitest'
import { render, screen, fireEvent, waitFor } from '@testing-library/react'
import { renderWithProviders } from '@/__test__/test-utils'
import EventDetailTabs from '../EventDetailTabs'

// Mock child components
vi.mock('../CirclesGrid', () => ({
  default: ({ data }: any) => <div data-testid="circles-grid">Circles Grid: {data.length} items</div>
}))

vi.mock('../EnhancedFilterBar', () => ({
  default: (props: any) => <div data-testid="enhanced-filter-bar">Enhanced Filter Bar</div>
}))

vi.mock('../EventMap', () => ({
  default: (props: any) => <div data-testid="event-map">Event Map</div>
}))

vi.mock('../EnhancedSkeleton', () => ({
  TabContentSkeleton: () => <div data-testid="tab-content-skeleton">Tab Content Skeleton</div>,
  OverviewTabSkeleton: () => <div data-testid="overview-tab-skeleton">Overview Tab Skeleton</div>,
  VenueTabSkeleton: () => <div data-testid="venue-tab-skeleton">Venue Tab Skeleton</div>,
  TravelTabSkeleton: () => <div data-testid="travel-tab-skeleton">Travel Tab Skeleton</div>
}))

vi.mock('../types', () => ({
  createVenueFromEvent: vi.fn(() => ({ name: 'Test Venue', address: 'Test Address' }))
}))

// Mock event data
const mockEvent = {
  id: '1',
  name: 'Test Event',
  date: '2024-01-15',
  venue_name: 'Test Venue',
  venue_address: 'Test Address',
  description: 'Test event description'
}

const mockCircles = [
  { id: '1', name: 'Circle 1' },
  { id: '2', name: 'Circle 2' },
  { id: '3', name: 'Circle 3' }
]

const mockProps = {
  event: mockEvent,
  circles: mockCircles,
  filteredCircles: mockCircles,
  keyword: '',
  setKeyword: vi.fn(),
  categories: [],
  setCategories: vi.fn(),
  toggleCategory: vi.fn(),
  categoryOptions: [
    { id: 'tech', label: '科技' },
    { id: 'art', label: '艺术' }
  ],
  isLoading: false
}

describe('EventDetailTabs (Radix Migration)', () => {
  beforeEach(() => {
    vi.clearAllMocks()
  })

  test('renders all tab triggers', () => {
    renderWithProviders(<EventDetailTabs {...mockProps} />)
    
    // 检查所有标签页触发器
    expect(screen.getByText('概览')).toBeInTheDocument()
    expect(screen.getByText(/参展商/)).toBeInTheDocument()
    expect(screen.getByText('会场信息')).toBeInTheDocument()
    expect(screen.getByText('交通住宿')).toBeInTheDocument()
  })

  test('shows exhibitors count in tab trigger', () => {
    renderWithProviders(<EventDetailTabs {...mockProps} />)
    
    expect(screen.getByText('参展商 (3)')).toBeInTheDocument()
  })

  test('renders overview tab trigger', () => {
    renderWithProviders(<EventDetailTabs {...mockProps} />)

    // 检查概览标签页触发器存在
    const overviewTab = screen.getByText('概览')
    expect(overviewTab).toBeInTheDocument()
    expect(overviewTab.closest('[role="tab"]')).toBeInTheDocument()
  })

  test('renders exhibitors tab content', () => {
    renderWithProviders(<EventDetailTabs {...mockProps} />)

    // 参展商标签页是默认的
    expect(screen.getByTestId('enhanced-filter-bar')).toBeInTheDocument()
    expect(screen.getByTestId('circles-grid')).toBeInTheDocument()
  })

  test('renders venue tab trigger', () => {
    renderWithProviders(<EventDetailTabs {...mockProps} />)

    const venueTab = screen.getByText('会场信息')
    expect(venueTab).toBeInTheDocument()
    expect(venueTab.closest('[role="tab"]')).toBeInTheDocument()
  })

  test('renders travel tab trigger', () => {
    renderWithProviders(<EventDetailTabs {...mockProps} />)

    const travelTab = screen.getByText('交通住宿')
    expect(travelTab).toBeInTheDocument()
    expect(travelTab.closest('[role="tab"]')).toBeInTheDocument()
  })

  test('shows loading skeletons when isLoading is true', () => {
    const loadingProps = { ...mockProps, isLoading: true }
    renderWithProviders(<EventDetailTabs {...loadingProps} />)

    // 参展商标签页应该显示加载状态
    expect(screen.getByText('加载中...')).toBeInTheDocument()
  })

  test('handles null event gracefully', () => {
    const nullEventProps = { ...mockProps, event: null }
    renderWithProviders(<EventDetailTabs {...nullEventProps} />)

    // 应该仍然渲染标签页
    expect(screen.getByText('概览')).toBeInTheDocument()
    expect(screen.getByText(/参展商/)).toBeInTheDocument()
  })

  test('component renders without crashing', () => {
    const { container } = renderWithProviders(<EventDetailTabs {...mockProps} />)
    expect(container.firstChild).toBeInTheDocument()
  })

  test('handles empty circles array', () => {
    const emptyCirclesProps = { ...mockProps, circles: [], filteredCircles: [] }
    renderWithProviders(<EventDetailTabs {...emptyCirclesProps} />)

    expect(screen.getByText('参展商 (0)')).toBeInTheDocument()
    expect(screen.getByTestId('circles-grid')).toBeInTheDocument()
  })

  test('tab triggers have correct accessibility attributes', () => {
    renderWithProviders(<EventDetailTabs {...mockProps} />)

    const overviewTab = screen.getByText('概览')
    const exhibitorsTab = screen.getByText(/参展商/)

    // 检查 tab 的 role 和属性
    expect(overviewTab.closest('[role="tab"]')).toBeInTheDocument()
    expect(exhibitorsTab.closest('[role="tab"]')).toBeInTheDocument()
  })

  test('default tab is exhibitors', () => {
    renderWithProviders(<EventDetailTabs {...mockProps} />)

    const exhibitorsTab = screen.getByText(/参展商/)
    expect(exhibitorsTab).toHaveAttribute('aria-selected', 'true')
  })

  test('tabs have correct icons', () => {
    renderWithProviders(<EventDetailTabs {...mockProps} />)

    // 检查每个标签页都有对应的图标
    const overviewTab = screen.getByText('概览').closest('button')
    const exhibitorsTab = screen.getByText(/参展商/).closest('button')
    const venueTab = screen.getByText('会场信息').closest('button')
    const travelTab = screen.getByText('交通住宿').closest('button')

    expect(overviewTab?.querySelector('svg')).toBeInTheDocument()
    expect(exhibitorsTab?.querySelector('svg')).toBeInTheDocument()
    expect(venueTab?.querySelector('svg')).toBeInTheDocument()
    expect(travelTab?.querySelector('svg')).toBeInTheDocument()
  })
})
