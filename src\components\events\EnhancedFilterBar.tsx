import * as DropdownMenu from "@radix-ui/react-dropdown-menu";
import { Search, Filter, Grid, List, Map, SortAsc, ChevronDown } from "lucide-react"
import { cn } from "@/lib/utils"

// 纯 Radix Button 组件 (复用之前的实现)
interface ButtonProps extends React.ButtonHTMLAttributes<HTMLButtonElement> {
  variant?: 'default' | 'ghost' | 'outline';
  size?: 'default' | 'sm' | 'lg' | 'icon';
  asChild?: boolean;
}

function RadixButton({
  className,
  variant = 'default',
  size = 'default',
  children,
  ...props
}: ButtonProps) {
  const baseStyles = "inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2";

  const variants = {
    default: "bg-primary text-primary-foreground shadow-sm hover:bg-primary/90",
    ghost: "hover:bg-accent hover:text-accent-foreground",
    outline: "border border-input bg-background hover:bg-accent hover:text-accent-foreground",
  };

  const sizes = {
    default: "h-10 px-4 py-2",
    sm: "h-9 rounded-md px-3",
    lg: "h-11 rounded-md px-8",
    icon: "h-10 w-10",
  };

  return (
    <button
      type="button"
      className={cn(
        baseStyles,
        variants[variant],
        sizes[size],
        className
      )}
      {...props}
    >
      {children}
    </button>
  );
}

// 纯 Radix Input 组件
interface InputProps extends React.InputHTMLAttributes<HTMLInputElement> {}

function RadixInput({ className, ...props }: InputProps) {
  return (
    <input
      className={cn(
        "flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50",
        className
      )}
      {...props}
    />
  );
}

// 纯 Radix Badge 组件
interface BadgeProps extends React.HTMLAttributes<HTMLDivElement> {
  variant?: 'default' | 'secondary' | 'destructive' | 'outline';
}

function RadixBadge({ className, variant = 'default', ...props }: BadgeProps) {
  const variants = {
    default: "bg-primary text-primary-foreground hover:bg-primary/80",
    secondary: "bg-secondary text-secondary-foreground hover:bg-secondary/80",
    destructive: "bg-destructive text-destructive-foreground hover:bg-destructive/80",
    outline: "text-foreground border border-input hover:bg-accent hover:text-accent-foreground",
  };

  return (
    <div
      className={cn(
        "inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 cursor-pointer",
        variants[variant],
        className
      )}
      {...props}
    />
  );
}

interface EnhancedFilterBarProps {
  keyword: string
  setKeyword: (keyword: string) => void
  categories: string[]
  setCategories: (categories: string[]) => void
  toggleCategory: (category: string) => void
  categoryOptions: { id: string; label: string }[]
  viewMode: 'grid' | 'list' | 'map'
  setViewMode: (mode: 'grid' | 'list' | 'map') => void
  sortBy: string
  setSortBy: (sort: string) => void
  total: number
}

export default function EnhancedFilterBar({
  keyword,
  setKeyword,
  categories,
  setCategories,
  toggleCategory,
  categoryOptions,
  viewMode,
  setViewMode,
  sortBy,
  setSortBy,
  total,
}: EnhancedFilterBarProps) {
  const sortOptions = [
    { value: 'name', label: '按名称排序' },
    { value: 'booth', label: '按摊位号排序' },
    { value: 'category', label: '按分类排序' },
  ]

  const viewModeIcons = {
    grid: Grid,
    list: List,
    map: Map,
  }

  return (
    <div className="space-y-4 p-6 bg-card rounded-lg border">
      {/* 顶部：搜索和视图控制 */}
      <div className="flex flex-col sm:flex-row gap-4 items-start sm:items-center justify-between">
        <div className="flex-1 max-w-md">
          <div className="relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
            <RadixInput
              placeholder="搜索摊位号、社团名称或作者..."
              value={keyword}
              onChange={(e) => setKeyword(e.target.value)}
              className="pl-10 search-input-enhanced"
            />
          </div>
        </div>

        <div className="flex items-center gap-2">
          {/* 排序选择 */}
          <DropdownMenu.Root>
            <DropdownMenu.Trigger asChild>
              <RadixButton variant="outline" className="w-[140px] justify-between">
                <div className="flex items-center">
                  <SortAsc className="h-4 w-4 mr-2" />
                  {sortOptions.find(opt => opt.value === sortBy)?.label || '排序'}
                </div>
                <ChevronDown className="h-4 w-4" />
              </RadixButton>
            </DropdownMenu.Trigger>
            <DropdownMenu.Portal>
              <DropdownMenu.Content
                className="min-w-[140px] bg-background border border-border rounded-md p-1 shadow-lg z-50"
                sideOffset={5}
              >
                {sortOptions.map((option) => (
                  <DropdownMenu.Item
                    key={option.value}
                    onSelect={() => setSortBy(option.value)}
                    className="flex items-center px-3 py-2 text-sm rounded-sm hover:bg-accent hover:text-accent-foreground outline-none focus:bg-accent focus:text-accent-foreground cursor-pointer"
                  >
                    {option.label}
                  </DropdownMenu.Item>
                ))}
              </DropdownMenu.Content>
            </DropdownMenu.Portal>
          </DropdownMenu.Root>

          {/* 视图模式切换 */}
          <div className="flex border rounded-md">
            {(['grid', 'list', 'map'] as const).map((mode) => {
              const Icon = viewModeIcons[mode]
              return (
                <RadixButton
                  key={mode}
                  variant={viewMode === mode ? "default" : "ghost"}
                  size="sm"
                  onClick={() => setViewMode(mode)}
                  className="rounded-none first:rounded-l-md last:rounded-r-md"
                >
                  <Icon className="h-4 w-4" />
                </RadixButton>
              )
            })}
          </div>
        </div>
      </div>

      {/* 中部：分类筛选 */}
      <div className="space-y-3">
        <div className="flex items-center justify-between">
          <h3 className="text-sm font-medium flex items-center gap-2">
            <Filter className="h-4 w-4" />
            分类筛选
          </h3>
          {categories.length > 0 && (
            <RadixButton
              variant="ghost"
              size="sm"
              onClick={() => setCategories([])}
              className="text-xs"
            >
              清除筛选
            </RadixButton>
          )}
        </div>

        <div className="flex flex-wrap gap-2">
          {categoryOptions.map((category) => {
            const isSelected = categories.includes(category.id)
            return (
              <RadixBadge
                key={category.id}
                variant={isSelected ? "default" : "outline"}
                className={cn(
                  "filter-badge",
                  isSelected && "active"
                )}
                onClick={() => toggleCategory(category.id)}
              >
                {category.label}
              </RadixBadge>
            )
          })}
        </div>
      </div>

      {/* 底部：结果统计和快速操作 */}
      <div className="flex items-center justify-between pt-2 border-t">
        <div className="text-sm text-muted-foreground">
          找到 <span className="font-medium text-foreground">{total}</span> 个参展商
          {categories.length > 0 && (
            <span className="ml-2">
              (已筛选 {categories.length} 个分类)
            </span>
          )}
        </div>

        <div className="flex items-center gap-2">
          <RadixButton
            variant="outline"
            size="sm"
            onClick={() => {
              setKeyword("")
              setCategories([])
            }}
          >
            重置所有
          </RadixButton>
        </div>
      </div>
    </div>
  )
}
