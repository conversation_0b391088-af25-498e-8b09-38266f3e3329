import type { Metadata } from "next"

const SITE_URL = process.env.NEXT_PUBLIC_SITE_URL || "https://ayafeed.com"

export const metadata: Metadata = {
  title: "社团列表 | Ayafeed - 同人展会社团信息聚合平台",
  description:
    "浏览所有参与同人展会的社团信息，发现你喜欢的创作者和作品。支持按分类筛选，快速找到感兴趣的同人社团。",
  keywords: [
    "同人社团",
    "社团列表",
    "同人展会",
    "创作者",
    "同人作品",
    "Comiket",
    "例大祭",
    "同人志",
    "同人游戏",
    "同人音乐",
    "Ayafeed",
  ],
  authors: [{ name: "Ayafeed Team" }],
  creator: "Ayafeed",
  publisher: "Ayafeed",
  formatDetection: {
    email: false,
    address: false,
    telephone: false,
  },
  alternates: {
    canonical: `${SITE_URL}/circles`,
  },
  openGraph: {
    title: "社团列表 | Ayafeed",
    description: "浏览所有参与同人展会的社团信息，发现你喜欢的创作者和作品",
    url: `${SITE_URL}/circles`,
    type: "website",
    siteName: "Ayafeed",
    locale: "zh_CN",
    images: [
      {
        url: `${SITE_URL}/og-circles.png`,
        width: 1200,
        height: 630,
        alt: "Ayafeed 社团列表 - 同人展会社团信息聚合",
      },
    ],
  },
  twitter: {
    card: "summary_large_image",
    title: "社团列表 | Ayafeed",
    description: "浏览所有参与同人展会的社团信息，发现你喜欢的创作者和作品",
    images: [`${SITE_URL}/og-circles.png`],
    creator: "@ayafeed",
  },
  robots: {
    index: true,
    follow: true,
    googleBot: {
      index: true,
      follow: true,
      "max-video-preview": -1,
      "max-image-preview": "large",
      "max-snippet": -1,
    },
  },
  verification: {
    google: process.env.GOOGLE_SITE_VERIFICATION,
  },
  category: "Entertainment",
}

export default function CirclesLayout({
  children,
}: {
  children: React.ReactNode
}) {
  return children
}
