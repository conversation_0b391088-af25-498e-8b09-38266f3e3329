/**
 * 图片处理相关的 Hook
 */

import { useState, useCallback, useRef } from 'react';
import { 
  ImageProcessor, 
  processEventImage, 
  validateImageFile,
  type ProcessingProgress,
  type ProcessedImageResult
} from '@/lib/image-processor';

export interface UseImageProcessorOptions {
  onProgress?: (variant: string, progress: ProcessingProgress) => void;
  onComplete?: (results: Array<ProcessedImageResult & { name: string }>) => void;
  onError?: (error: Error) => void;
}

export interface UseImageProcessorReturn {
  processImage: (file: File) => Promise<Array<ProcessedImageResult & { name: string }>>;
  isProcessing: boolean;
  progress: Record<string, ProcessingProgress>;
  error: string | null;
  reset: () => void;
}

/**
 * 图片处理 Hook
 */
export function useImageProcessor(options?: UseImageProcessorOptions): UseImageProcessorReturn {
  const [isProcessing, setIsProcessing] = useState(false);
  const [progress, setProgress] = useState<Record<string, ProcessingProgress>>({});
  const [error, setError] = useState<string | null>(null);
  
  const processorRef = useRef<ImageProcessor | null>(null);
  const { onProgress, onComplete, onError } = options || {};

  const processImage = useCallback(async (file: File) => {
    // 验证文件
    const validation = validateImageFile(file);
    if (!validation.valid) {
      const error = new Error(validation.error);
      setError(validation.error || '文件验证失败');
      onError?.(error);
      throw error;
    }

    setIsProcessing(true);
    setError(null);
    setProgress({});

    try {
      const results = await processEventImage(file, (variant, progressInfo) => {
        setProgress(prev => ({
          ...prev,
          [variant]: progressInfo
        }));
        onProgress?.(variant, progressInfo);
      });

      onComplete?.(results);
      return results;
    } catch (err) {
      const error = err instanceof Error ? err : new Error('图片处理失败');
      setError(error.message);
      onError?.(error);
      throw error;
    } finally {
      setIsProcessing(false);
    }
  }, [onProgress, onComplete, onError]);

  const reset = useCallback(() => {
    setIsProcessing(false);
    setProgress({});
    setError(null);
    
    // 清理处理器
    if (processorRef.current) {
      processorRef.current.dispose();
      processorRef.current = null;
    }
  }, []);

  return {
    processImage,
    isProcessing,
    progress,
    error,
    reset
  };
}

/**
 * 文件验证 Hook
 */
export function useFileValidation() {
  const validateFile = useCallback((file: File) => {
    return validateImageFile(file);
  }, []);

  const validateFiles = useCallback((files: File[]) => {
    const results = files.map(file => ({
      file,
      ...validateImageFile(file)
    }));

    const validFiles = results.filter(r => r.valid).map(r => r.file);
    const invalidFiles = results.filter(r => !r.valid);

    return {
      validFiles,
      invalidFiles: invalidFiles.map(r => ({
        file: r.file,
        error: r.error || '未知错误'
      })),
      allValid: invalidFiles.length === 0
    };
  }, []);

  return {
    validateFile,
    validateFiles
  };
}

/**
 * 图片预览 Hook
 */
export function useImagePreview() {
  const [previews, setPreviews] = useState<Record<string, string>>({});

  const createPreview = useCallback((file: File, key?: string) => {
    const previewKey = key || file.name;
    const url = URL.createObjectURL(file);
    
    setPreviews(prev => ({
      ...prev,
      [previewKey]: url
    }));

    return url;
  }, []);

  const removePreview = useCallback((key: string) => {
    setPreviews(prev => {
      const url = prev[key];
      if (url) {
        URL.revokeObjectURL(url);
      }
      
      const { [key]: removed, ...rest } = prev;
      return rest;
    });
  }, []);

  const clearPreviews = useCallback(() => {
    Object.values(previews).forEach(url => {
      URL.revokeObjectURL(url);
    });
    setPreviews({});
  }, [previews]);

  return {
    previews,
    createPreview,
    removePreview,
    clearPreviews
  };
}

/**
 * 图片尺寸检测 Hook
 */
export function useImageDimensions() {
  const getDimensions = useCallback((file: File): Promise<{ width: number; height: number }> => {
    return new Promise((resolve, reject) => {
      const img = new Image();
      
      img.onload = () => {
        resolve({
          width: img.naturalWidth,
          height: img.naturalHeight
        });
        URL.revokeObjectURL(img.src);
      };
      
      img.onerror = () => {
        reject(new Error('无法读取图片尺寸'));
        URL.revokeObjectURL(img.src);
      };
      
      img.src = URL.createObjectURL(file);
    });
  }, []);

  return { getDimensions };
}

/**
 * 图片格式转换 Hook
 */
export function useImageConverter() {
  const convertFormat = useCallback(async (
    file: File, 
    targetFormat: 'jpeg' | 'png' | 'webp',
    quality: number = 0.9
  ): Promise<Blob> => {
    const processor = new ImageProcessor();
    
    try {
      const result = await processor.processImage(file, {
        width: 0, // 保持原始尺寸
        height: 0,
        quality: quality * 100,
        format: targetFormat
      });
      
      return result.blob;
    } finally {
      processor.dispose();
    }
  }, []);

  return { convertFormat };
}
