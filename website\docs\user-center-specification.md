# 用户中心功能规划文档

> 📋 本文档详细规划了 Ayafeed 平台用户中心功能的设计与实现方案

## 🎯 功能概览

### 设计目标

用户中心旨在为 Ayafeed 平台用户提供完整的个人账户管理体验，包括个人资料管理、偏好设置、社交功能、隐私控制等核心功能。

### 核心价值

- **个性化体验**: 根据用户偏好定制内容和界面
- **社交互动**: 支持关注、收藏、分享等社交功能
- **数据安全**: 完善的隐私保护和数据管理
- **便捷管理**: 统一的账户和设置管理入口

## 🏗️ 架构设计

### 模块结构

```text
src/modules/user-center/
├── profile/              # 个人资料管理
│   ├── controller.ts
│   ├── service.ts
│   ├── schema.ts
│   ├── routes.ts
│   └── repository.ts
├── settings/             # 用户设置
├── social/               # 社交功能（关注/收藏）
├── notifications/        # 通知管理
├── security/             # 安全设置
├── activities/           # 活动记录
└── privacy/              # 隐私与数据管理
```

### 数据流设计

```mermaid
graph TD
    A[用户请求] --> B[认证中间件]
    B --> C[权限验证]
    C --> D[业务逻辑层]
    D --> E[数据访问层]
    E --> F[D1 数据库]
    D --> G[缓存层]
    G --> H[Cloudflare KV]
```

## 📋 核心功能模块

### 1. 个人资料管理

**功能描述**: 用户基本信息和扩展资料管理

**主要功能**:
- 基本信息编辑（昵称、头像、简介、联系方式）
- 社交媒体链接管理
- 个人标签和兴趣设置
- 地区和语言偏好

**API 端点**:
```typescript
GET    /user/profile           # 获取个人资料
PUT    /user/profile           # 更新个人资料
POST   /user/profile/avatar    # 上传头像
DELETE /user/profile/avatar    # 删除头像
```

### 2. 账户安全设置

**功能描述**: 账户安全相关的设置和管理

**主要功能**:
- 密码修改
- 邮箱绑定和验证
- 两步验证设置
- 登录设备管理
- 安全日志查看

**API 端点**:
```typescript
PUT    /user/security/password     # 修改密码
POST   /user/security/email        # 绑定邮箱
POST   /user/security/2fa          # 启用两步验证
GET    /user/security/devices      # 获取登录设备
DELETE /user/security/devices/:id  # 移除设备
GET    /user/security/logs         # 获取安全日志
```

### 3. 关注与收藏

**功能描述**: 社交功能，支持关注社团、作者，收藏展会等

**主要功能**:
- 关注/取消关注社团
- 关注/取消关注作者
- 收藏/取消收藏展会
- 个人收藏夹管理
- 关注列表管理

**API 端点**:
```typescript
POST   /user/follows/circles/:id    # 关注社团
DELETE /user/follows/circles/:id    # 取消关注社团
POST   /user/follows/artists/:id    # 关注作者
DELETE /user/follows/artists/:id    # 取消关注作者
POST   /user/favorites/events/:id   # 收藏展会
DELETE /user/favorites/events/:id   # 取消收藏展会
GET    /user/follows                # 获取关注列表
GET    /user/favorites              # 获取收藏列表
```

### 4. 通知管理

**功能描述**: 用户通知的接收、管理和设置

**主要功能**:
- 通知列表查看
- 通知状态管理（已读/未读）
- 通知偏好设置
- 推送设置管理

**API 端点**:
```typescript
GET    /user/notifications          # 获取通知列表
PUT    /user/notifications/:id      # 标记通知状态
POST   /user/notifications/read-all # 全部标记已读
GET    /user/notifications/settings # 获取通知设置
PUT    /user/notifications/settings # 更新通知设置
```

### 5. 个人活动记录

**功能描述**: 用户在平台上的活动历史记录

**主要功能**:
- 参与展会记录
- 评价和评论历史
- 分享记录
- 搜索历史

**API 端点**:
```typescript
GET /user/activities              # 获取活动记录
GET /user/activities/events       # 获取展会参与记录
GET /user/activities/reviews      # 获取评价记录
GET /user/activities/shares       # 获取分享记录
```

### 6. 隐私与数据管理

**功能描述**: 用户隐私设置和数据管理

**主要功能**:
- 隐私设置管理
- 数据导出功能
- 账户删除申请
- 第三方授权管理

**API 端点**:
```typescript
GET    /user/privacy/settings      # 获取隐私设置
PUT    /user/privacy/settings      # 更新隐私设置
POST   /user/data/export           # 申请数据导出
POST   /user/account/delete        # 申请账户删除
GET    /user/oauth/connections     # 获取第三方授权
DELETE /user/oauth/connections/:id # 取消第三方授权
```

## 🗄️ 数据库设计

### 新增数据表

#### user_profiles 表
```sql
CREATE TABLE user_profiles (
  user_id TEXT PRIMARY KEY REFERENCES auth_user(id),
  display_name TEXT,
  avatar_url TEXT,
  bio TEXT,
  location TEXT,
  website TEXT,
  birth_date TEXT,
  gender TEXT CHECK (gender IN ('male', 'female', 'other', 'prefer_not_to_say')),
  created_at TEXT NOT NULL DEFAULT (strftime('%Y-%m-%dT%H:%M:%fZ', 'now')),
  updated_at TEXT NOT NULL DEFAULT (strftime('%Y-%m-%dT%H:%M:%fZ', 'now'))
);
```

#### user_follows 表
```sql
CREATE TABLE user_follows (
  id TEXT PRIMARY KEY,
  user_id TEXT NOT NULL REFERENCES auth_user(id),
  target_type TEXT NOT NULL CHECK (target_type IN ('circle', 'artist')),
  target_id TEXT NOT NULL,
  created_at TEXT NOT NULL DEFAULT (strftime('%Y-%m-%dT%H:%M:%fZ', 'now')),
  UNIQUE(user_id, target_type, target_id)
);
```

#### user_favorites 表
```sql
CREATE TABLE user_favorites (
  id TEXT PRIMARY KEY,
  user_id TEXT NOT NULL REFERENCES auth_user(id),
  target_type TEXT NOT NULL CHECK (target_type IN ('event', 'circle', 'artist')),
  target_id TEXT NOT NULL,
  folder_name TEXT DEFAULT 'default',
  created_at TEXT NOT NULL DEFAULT (strftime('%Y-%m-%dT%H:%M:%fZ', 'now')),
  UNIQUE(user_id, target_type, target_id)
);
```

#### user_notifications 表
```sql
CREATE TABLE user_notifications (
  id TEXT PRIMARY KEY,
  user_id TEXT NOT NULL REFERENCES auth_user(id),
  type TEXT NOT NULL,
  title TEXT NOT NULL,
  content TEXT NOT NULL,
  data TEXT CHECK (json_valid(data)),
  is_read BOOLEAN DEFAULT FALSE,
  created_at TEXT NOT NULL DEFAULT (strftime('%Y-%m-%dT%H:%M:%fZ', 'now'))
);
```

#### user_settings 表
```sql
CREATE TABLE user_settings (
  user_id TEXT PRIMARY KEY REFERENCES auth_user(id),
  theme TEXT DEFAULT 'auto' CHECK (theme IN ('light', 'dark', 'auto')),
  language TEXT DEFAULT 'auto' CHECK (language IN ('en', 'ja', 'zh', 'auto')),
  timezone TEXT DEFAULT 'UTC',
  email_notifications BOOLEAN DEFAULT TRUE,
  push_notifications BOOLEAN DEFAULT TRUE,
  privacy_level TEXT DEFAULT 'public' CHECK (privacy_level IN ('public', 'friends', 'private')),
  show_activity BOOLEAN DEFAULT TRUE,
  show_favorites BOOLEAN DEFAULT TRUE,
  created_at TEXT NOT NULL DEFAULT (strftime('%Y-%m-%dT%H:%M:%fZ', 'now')),
  updated_at TEXT NOT NULL DEFAULT (strftime('%Y-%m-%dT%H:%M:%fZ', 'now'))
);
```

#### user_activities 表
```sql
CREATE TABLE user_activities (
  id TEXT PRIMARY KEY,
  user_id TEXT NOT NULL REFERENCES auth_user(id),
  action_type TEXT NOT NULL,
  target_type TEXT,
  target_id TEXT,
  metadata TEXT CHECK (json_valid(metadata)),
  created_at TEXT NOT NULL DEFAULT (strftime('%Y-%m-%dT%H:%M:%fZ', 'now'))
);
```

#### user_social_links 表
```sql
CREATE TABLE user_social_links (
  id TEXT PRIMARY KEY,
  user_id TEXT NOT NULL REFERENCES auth_user(id),
  platform TEXT NOT NULL,
  url TEXT NOT NULL,
  display_name TEXT,
  created_at TEXT NOT NULL DEFAULT (strftime('%Y-%m-%dT%H:%M:%fZ', 'now')),
  UNIQUE(user_id, platform)
);
```

## 🔐 安全与隐私考虑

### 数据保护

1. **敏感信息加密**: 个人信息使用 AES-256 加密存储
2. **访问控制**: 实现细粒度的权限控制
3. **审计日志**: 记录所有敏感操作的审计日志
4. **数据最小化**: 只收集必要的用户信息

### API 安全

1. **认证验证**: 所有 API 都需要有效的会话认证
2. **权限检查**: 确保用户只能访问自己的数据
3. **频率限制**: 实现 API 调用频率限制
4. **输入验证**: 严格的输入数据验证和清理

### GDPR 合规

1. **数据导出**: 支持用户数据的完整导出
2. **数据删除**: 支持用户账户和数据的完全删除
3. **同意管理**: 明确的数据使用同意机制
4. **透明度**: 清晰的隐私政策和数据使用说明

## 📅 实施计划

### 第一阶段：基础功能 (2-3 周)
- [ ] 个人资料管理
- [ ] 基础设置功能
- [ ] 密码修改功能

### 第二阶段：社交功能 (2-3 周)
- [ ] 关注/收藏功能
- [ ] 活动记录
- [ ] 通知系统基础

### 第三阶段：高级功能 (3-4 周)
- [ ] 完整通知系统
- [ ] 隐私设置
- [ ] 数据导出/删除
- [ ] 安全日志

### 第四阶段：优化完善 (1-2 周)
- [ ] 性能优化
- [ ] 用户体验优化
- [ ] 测试完善
- [ ] 文档更新

## 🧪 测试策略

### 单元测试
- 所有 service 层函数的单元测试
- 数据验证逻辑测试
- 权限控制逻辑测试

### 集成测试
- API 端点的完整流程测试
- 数据库操作的集成测试
- 认证和授权的集成测试

### 端到端测试
- 用户完整操作流程测试
- 跨模块功能的端到端测试
- 性能和负载测试

---

> 📝 **注意**: 本文档将随着开发进展持续更新，请确保团队成员使用最新版本。
