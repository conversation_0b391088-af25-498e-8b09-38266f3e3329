import { request } from "./http";

// ayafeed 代码生成器自定义 fetcher 适配层
// 通过复用 request() 统一处理 Toast / 401 / 403 等逻辑
// 泛型签名需与 generateReactQueryComponents 期望保持一致
type Primitive = string | number | boolean | null | undefined;

 
export async function ayafeedFetch<
  TData = unknown,
  // TError 保留仅为向后兼容，内部不会使用
  TError = unknown,
  // Body 可以是任意可序列化对象或 FormData
  TBody extends Record<string, unknown> | FormData | null | undefined = Record<string, unknown>,
  THeaders extends Record<string, string> = Record<string, string>,
  TQueryParams extends Record<string, Primitive> = Record<string, Primitive>,
  TPathParams extends Record<string, Primitive> = Record<string, Primitive>,
>(options: {
  url: string;
  method: string;
  body?: TBody;
  headers?: THeaders;
  queryParams?: TQueryParams;
  pathParams?: TPathParams;
  signal?: AbortSignal;
}): Promise<TData> {
  // 构建最终请求路径（相对于 API_BASE）
  const buildPath = () => {
    const { url, queryParams = {}, pathParams = {} } = options;

    // 替换路径参数 /events/{id} → /events/123
    let path = url.replace(/\{\w+\}/g, (key) => {
      const k = key.slice(1, -1); // 去除花括号
      const v = (pathParams as Record<string, Primitive>)[k];
      return encodeURIComponent(String(v ?? ""));
    });

    // 拼接查询字符串
    const query = new URLSearchParams(
      Object.entries(queryParams as Record<string, Primitive>)
        .filter(([, v]) => v != null)
        .reduce<Record<string, string>>((acc, [k, v]) => {
          acc[k] = String(v);
          return acc;
        }, {})
    ).toString();

    if (query) path += `?${query}`;

    return path;
  };

  // FormData 直接透传，其余对象按 JSON 字符串化
  const buildBody = (): BodyInit | undefined => {
    if (options.body == null) return undefined;
    if (options.body instanceof FormData) return options.body;
    return JSON.stringify(options.body as unknown);
  };

  return request<TData>(buildPath(), {
    method: options.method.toUpperCase(),
    body: buildBody(),
    headers: options.headers as HeadersInit,
    signal: options.signal,
  });
} 