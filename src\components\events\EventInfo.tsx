'use client'

import { useTranslations } from 'next-intl'
import { ExternalLink } from 'lucide-react'

import { cn } from '@/lib/utils'
import type { EventInfoProps } from './types'

/**
 * 事件信息组件
 * 
 * 功能：
 * - 显示事件基本信息（名称、日期、场馆、描述）
 * - 显示官方网站链接
 * - 处理数据缺失的情况
 * - 支持国际化
 */
export default function EventInfo({ event, className }: EventInfoProps) {
  const t = useTranslations('EventHeader')

  if (!event) {
    return (
      <div className={cn("flex flex-col gap-6", className)}>
        {/* 骨架屏 */}
        <div className="space-y-4">
          <div className="h-8 bg-gray-200 rounded animate-pulse" />
          <div className="h-6 bg-gray-200 rounded animate-pulse w-3/4" />
        </div>
        
        <div className="space-y-3">
          <div className="grid grid-cols-[auto_1fr] gap-4">
            <div className="h-5 w-12 bg-gray-200 rounded animate-pulse" />
            <div className="h-5 bg-gray-200 rounded animate-pulse" />
          </div>
          <div className="grid grid-cols-[auto_1fr] gap-4">
            <div className="h-5 w-12 bg-gray-200 rounded animate-pulse" />
            <div className="h-5 bg-gray-200 rounded animate-pulse" />
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className={cn("flex flex-col gap-6", className)}>
      {/* 事件标题 */}
      <h1 className="text-3xl md:text-4xl font-bold leading-tight">
        {event.name}
      </h1>

      {/* 事件详情 */}
      <dl className="grid grid-cols-[auto_1fr] gap-y-3 text-base md:text-lg font-medium max-w-xl">
        {/* 日期 */}
        <dt className="text-muted-foreground pr-4">{t('date')}</dt>
        <dd className="border-b border-border pb-2">
          {event.date || '未定'}
        </dd>

        {/* 场馆名称 */}
        <dt className="text-muted-foreground pr-4 mt-2">{t('venue')}</dt>
        <dd className="border-b border-border pb-2">
          {event.venue_name || t('defaultVenueName')}
        </dd>

        {/* 场馆地址（可选） */}
        {event.venue_address && (
          <>
            <dt className="text-muted-foreground pr-4 mt-2">{t('address')}</dt>
            <dd className="border-b border-border pb-2 text-foreground/80">
              {event.venue_address}
            </dd>
          </>
        )}

        {/* 描述（可选） */}
        {event.description && (
          <>
            <dt className="text-muted-foreground pr-4 mt-2">{t('description')}</dt>
            <dd className="border-b border-border pb-2 leading-6 text-foreground/80">
              {event.description}
            </dd>
          </>
        )}
      </dl>

      {/* 官方网站链接（可选） */}
      {event.url && (
        <a
          href={event.url}
          target="_blank"
          rel="noopener noreferrer"
          className="inline-flex items-center gap-2 text-primary hover:text-primary/80 underline text-sm w-fit transition-colors"
        >
          <span>{t('officialWebsite')}</span>
          <ExternalLink className="h-4 w-4" />
        </a>
      )}
    </div>
  )
}
