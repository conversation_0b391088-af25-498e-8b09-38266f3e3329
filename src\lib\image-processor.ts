/**
 * 图片处理工具
 * 使用 Canvas API 进行图片缩放、裁剪和压缩
 */

export interface ImageProcessorOptions {
  width: number;
  height: number;
  quality: number;
  format?: 'jpeg' | 'png' | 'webp';
  cropStrategy?: 'center' | 'top' | 'bottom';
}

export interface ProcessedImageResult {
  blob: Blob;
  width: number;
  height: number;
  size: number;
  format: string;
}

export interface ProcessingProgress {
  stage: 'loading' | 'processing' | 'compressing' | 'complete';
  progress: number;
}

/**
 * 图片处理器类
 */
export class ImageProcessor {
  private canvas: HTMLCanvasElement;
  private ctx: CanvasRenderingContext2D;

  constructor() {
    this.canvas = document.createElement('canvas');
    const ctx = this.canvas.getContext('2d');
    if (!ctx) {
      throw new Error('Canvas 2D context not supported');
    }
    this.ctx = ctx;
  }

  /**
   * 处理图片文件
   * @param file 原始图片文件
   * @param options 处理选项
   * @param onProgress 进度回调
   * @returns 处理后的图片结果
   */
  async processImage(
    file: File,
    options: ImageProcessorOptions,
    onProgress?: (progress: ProcessingProgress) => void
  ): Promise<ProcessedImageResult> {
    onProgress?.({ stage: 'loading', progress: 0 });

    // 加载图片
    const img = await this.loadImage(file);
    onProgress?.({ stage: 'loading', progress: 50 });

    // 计算目标尺寸
    const { targetWidth, targetHeight, sourceX, sourceY, sourceWidth, sourceHeight } = 
      this.calculateDimensions(img, options);

    onProgress?.({ stage: 'processing', progress: 0 });

    // 设置画布尺寸
    this.canvas.width = targetWidth;
    this.canvas.height = targetHeight;

    // 清空画布
    this.ctx.clearRect(0, 0, targetWidth, targetHeight);

    // 绘制图片
    this.ctx.drawImage(
      img,
      sourceX, sourceY, sourceWidth, sourceHeight,
      0, 0, targetWidth, targetHeight
    );

    onProgress?.({ stage: 'processing', progress: 100 });
    onProgress?.({ stage: 'compressing', progress: 0 });

    // 转换为 Blob
    const blob = await this.canvasToBlob(options);
    
    onProgress?.({ stage: 'compressing', progress: 100 });
    onProgress?.({ stage: 'complete', progress: 100 });

    return {
      blob,
      width: targetWidth,
      height: targetHeight,
      size: blob.size,
      format: options.format || 'jpeg'
    };
  }

  /**
   * 批量处理图片（生成多个变体）
   * @param file 原始图片文件
   * @param variants 变体配置
   * @param onProgress 进度回调
   * @returns 处理后的图片结果数组
   */
  async processImageVariants(
    file: File,
    variants: Array<ImageProcessorOptions & { name: string }>,
    onProgress?: (variant: string, progress: ProcessingProgress) => void
  ): Promise<Array<ProcessedImageResult & { name: string }>> {
    const results: Array<ProcessedImageResult & { name: string }> = [];

    for (let i = 0; i < variants.length; i++) {
      const variant = variants[i];
      const result = await this.processImage(
        file,
        variant,
        (progress) => onProgress?.(variant.name, progress)
      );
      results.push({ ...result, name: variant.name });
    }

    return results;
  }

  /**
   * 加载图片文件
   */
  private loadImage(file: File): Promise<HTMLImageElement> {
    return new Promise((resolve, reject) => {
      const img = new Image();
      img.onload = () => resolve(img);
      img.onerror = () => reject(new Error('Failed to load image'));
      img.src = URL.createObjectURL(file);
    });
  }

  /**
   * 计算目标尺寸和裁剪区域
   */
  private calculateDimensions(
    img: HTMLImageElement,
    options: ImageProcessorOptions
  ) {
    const { width: targetWidth, height: targetHeight, cropStrategy = 'center' } = options;
    const sourceWidth = img.width;
    const sourceHeight = img.height;

    // 计算缩放比例
    const scaleX = targetWidth / sourceWidth;
    const scaleY = targetHeight / sourceHeight;
    const scale = Math.max(scaleX, scaleY);

    // 计算裁剪区域
    const cropWidth = targetWidth / scale;
    const cropHeight = targetHeight / scale;

    let sourceX = 0;
    let sourceY = 0;

    switch (cropStrategy) {
      case 'center':
        sourceX = (sourceWidth - cropWidth) / 2;
        sourceY = (sourceHeight - cropHeight) / 2;
        break;
      case 'top':
        sourceX = (sourceWidth - cropWidth) / 2;
        sourceY = 0;
        break;
      case 'bottom':
        sourceX = (sourceWidth - cropWidth) / 2;
        sourceY = sourceHeight - cropHeight;
        break;
    }

    return {
      targetWidth,
      targetHeight,
      sourceX: Math.max(0, sourceX),
      sourceY: Math.max(0, sourceY),
      sourceWidth: Math.min(cropWidth, sourceWidth),
      sourceHeight: Math.min(cropHeight, sourceHeight)
    };
  }

  /**
   * 将画布转换为 Blob
   */
  private canvasToBlob(options: ImageProcessorOptions): Promise<Blob> {
    return new Promise((resolve, reject) => {
      const format = options.format || 'jpeg';
      const mimeType = `image/${format}`;
      const quality = format === 'jpeg' ? options.quality / 100 : undefined;

      this.canvas.toBlob(
        (blob) => {
          if (blob) {
            resolve(blob);
          } else {
            reject(new Error('Failed to convert canvas to blob'));
          }
        },
        mimeType,
        quality
      );
    });
  }

  /**
   * 清理资源
   */
  dispose() {
    // Canvas 会被垃圾回收，这里主要是为了明确清理
    this.canvas.width = 0;
    this.canvas.height = 0;
  }
}

/**
 * 预定义的变体配置
 */
export const EVENT_IMAGE_VARIANTS = {
  original: {
    name: 'original',
    width: 0, // 保持原始尺寸
    height: 0,
    quality: 95,
    format: 'jpeg' as const
  },
  large: {
    name: 'large',
    width: 1200,
    height: 900,
    quality: 85,
    format: 'jpeg' as const
  },
  medium: {
    name: 'medium',
    width: 400,
    height: 300,
    quality: 80,
    format: 'jpeg' as const
  }
} as const;

/**
 * 便捷函数：处理事件图片的所有变体
 */
export async function processEventImage(
  file: File,
  onProgress?: (variant: string, progress: ProcessingProgress) => void
): Promise<Array<ProcessedImageResult & { name: string }>> {
  const processor = new ImageProcessor();
  
  try {
    // 对于 original 变体，如果尺寸为 0，则保持原始尺寸
    const variants = Object.values(EVENT_IMAGE_VARIANTS).map(variant => {
      if (variant.name === 'original') {
        // 需要先获取原始图片尺寸
        return { ...variant, width: 0, height: 0 }; // 稍后处理
      }
      return variant;
    });

    // 特殊处理 original 变体
    const img = await new Promise<HTMLImageElement>((resolve, reject) => {
      const image = new Image();
      image.onload = () => resolve(image);
      image.onerror = () => reject(new Error('Failed to load image'));
      image.src = URL.createObjectURL(file);
    });

    const processVariants = variants.map(variant => {
      if (variant.name === 'original') {
        return {
          ...variant,
          width: img.width,
          height: img.height
        };
      }
      return variant;
    });

    const results = await processor.processImageVariants(file, processVariants, onProgress);
    return results;
  } finally {
    processor.dispose();
  }
}

/**
 * 验证图片文件
 */
export function validateImageFile(file: File): { valid: boolean; error?: string } {
  // 检查文件类型
  if (!file.type.startsWith('image/')) {
    return { valid: false, error: '请选择图片文件' };
  }

  // 检查支持的格式
  const supportedFormats = ['image/jpeg', 'image/png', 'image/webp', 'image/gif'];
  if (!supportedFormats.includes(file.type)) {
    return { valid: false, error: '不支持的图片格式，请选择 JPEG、PNG、WebP 或 GIF 格式' };
  }

  // 检查文件大小 (10MB)
  const maxSize = 10 * 1024 * 1024;
  if (file.size > maxSize) {
    return { valid: false, error: '图片文件过大，请选择小于 10MB 的文件' };
  }

  // 检查最小文件大小 (100 bytes)
  if (file.size < 100) {
    return { valid: false, error: '图片文件过小' };
  }

  return { valid: true };
}
