/**
 * 错误状态管理
 * 基于后端文档包的错误处理规范
 */

import { create } from 'zustand';
import { ErrorHandlerService, type ProcessedError } from '@/services/errorHandler';

interface ErrorState {
  errors: ProcessedError[];
  isShowingError: boolean;
  
  // Actions
  addError: (error: any, context?: any) => void;
  removeError: (index: number) => void;
  clearErrors: () => void;
  handleApiError: (error: any, context?: any) => ProcessedError;
}

export const useErrorStore = create<ErrorState>((set, get) => ({
  errors: [],
  isShowingError: false,
  
  addError: (error: any, context?: any) => {
    const processedError = ErrorHandlerService.processApiError(error);
    ErrorHandlerService.logError(processedError, context);
    
    set(state => ({
      errors: [...state.errors, processedError],
      isShowingError: true,
    }));
    
    // 自动处理重定向
    if (processedError.shouldRedirect) {
      setTimeout(() => {
        window.location.href = processedError.shouldRedirect!;
      }, 2000);
    }
  },
  
  removeError: (index: number) => {
    set(state => ({
      errors: state.errors.filter((_, i) => i !== index),
      isShowingError: state.errors.length > 1,
    }));
  },
  
  clearErrors: () => {
    set({ errors: [], isShowingError: false });
  },
  
  handleApiError: (error: any, context?: any) => {
    const processedError = ErrorHandlerService.processApiError(error);
    get().addError(error, context);
    return processedError;
  },
}));
