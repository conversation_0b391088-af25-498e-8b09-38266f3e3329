/* eslint-disable @next/next/no-img-element */
// 允许在测试环境中返回 <img> 以简化 next/image mock
import { expect, test, vi } from "vitest"

import CircleCard from "@/components/circle-card"
import { renderWithProviders, screen } from "@test/test-utils"

// Mock Next.js specific components
vi.mock("next/image", () => ({
  default: (props: any) => {
    // eslint-disable-next-line jsx-a11y/alt-text
    return <img {...props} />
  },
}))
vi.mock("next/link", () => ({
  default: (props: any) => {
    const { href, children, ...rest } = props
    return (
      <a href={href} {...rest}>
        {children}
      </a>
    )
  },
}))

test("renders circle card with image, name and link", () => {
  renderWithProviders(
    <CircleCard logo="/logo.png" name="天空之城" id="abc123" />
  )

  // 图片 alt 文本
  expect(screen.getByAltText("天空之城")).toBeInTheDocument()
  // 名称文字
  expect(screen.getByText("天空之城")).toBeInTheDocument()
  // 链接 href
  expect(screen.getByRole("link")).toHaveAttribute("href", "/circles/abc123")
}) 