import { dirname } from "path"
import { fileURLToPath } from "url"
import { FlatCompat } from "@eslint/eslintrc"
import js from "@eslint/js"

const __filename = fileURLToPath(import.meta.url)
const __dirname = dirname(__filename)

const compat = new FlatCompat({
  baseDirectory: __dirname,
  recommendedConfig: js.configs.recommended,
  extends: [
    "plugin:@typescript-eslint/strict-type-checked",
    "plugin:@typescript-eslint/stylistic-type-checked",
    "plugin:react-hooks/recommended",
    "next/core-web-vitals",
  ],
  rules: {
    // 按字母顺序自动排序 import (还可结合 eslint-plugin-simple-import-sort)
    "import/order": [
      "warn",
      {
        alphabetize: { order: "asc", caseInsensitive: true },
        "newlines-between": "always",
      },
    ],
    "react-hooks/exhaustive-deps": "warn",
  },
  // 忽略自动生成的 OpenAPI 类型文件以及其他生成产物/构建输出
  ignores: [
    "src/types/api-types.d.ts", // OpenAPI 生成的类型定义
    ".next/**/*", // Next.js 构建输出
    "coverage/**/*", // vitest 覆盖率报告
    "*.config.mjs", // 构建/工具配置文件
  ],
  // 启用 TypeScript Typed Linting 支持
  languageOptions: {
    parserOptions: {
      tsconfigRootDir: __dirname,
      projectService: true,
    },
  },
})

const eslintConfig = [
  ...compat.config({
    extends: ["eslint:recommended", "next"],
  }),
  {
    files: [
      "**/__test__/**/*",
      "**/__tests__/**/*",
      "**/*.test.*",
      "**/*.spec.*",
    ],
    // 测试代码允许使用更宽松的规则
    rules: {
      "@typescript-eslint/ban-ts-comment": "off",
      "@typescript-eslint/no-unsafe-assignment": "off",
      "@typescript-eslint/no-unsafe-call": "off",
      "@typescript-eslint/no-unsafe-member-access": "off",
      "no-unused-vars": "off",
      "no-undef": "off",
      "@typescript-eslint/no-unsafe-return": "off",
      "@typescript-eslint/no-explicit-any": "off",
      "@typescript-eslint/no-empty-function": "off",
      "@typescript-eslint/require-await": "off",
      "@typescript-eslint/await-thenable": "off",
      "@typescript-eslint/no-floating-promises": "off",
      "@typescript-eslint/no-misused-promises": "off",
      "@typescript-eslint/no-array-delete": "off",
      "react/display-name": "off",
      "no-constant-binary-expression": "off",
    },
  },
  {
    files: ["src/api/generated/**/*"],
    rules: {
      "@typescript-eslint/await-thenable": "off",
      "@typescript-eslint/no-floating-promises": "off",
      "@typescript-eslint/no-misused-promises": "off",
      "@typescript-eslint/no-array-delete": "off",
      "@typescript-eslint/require-await": "off",
      "@typescript-eslint/no-unused-vars": "off",
      "no-unused-vars": "off",
    },
  },
  {
    files: ["**/*.{ts,tsx}"],
    // 针对 TypeScript 源码的通用规则调整
    rules: {
      // TS 编译器负责未定义检查，此处关闭避免误报
      "no-undef": "off",
      // 允许未使用的占位符变量（以下划线开头），其余按 warn 处理
      "no-unused-vars": "off",
      "@typescript-eslint/no-unused-vars": "off",
      // 空代码块通常在 TS 中用于占位，这里降级为 warn
      "no-empty": "warn",
      "react/display-name": "warn",
      "no-constant-binary-expression": "warn",
    },
  },
]

export default eslintConfig
