# Venues表拆分迁移计划

## 🎯 目标
将venues数据从events表中拆分出来，实现数据规范化和更好的国际化支持。

## 📋 前后端边界划分

### 后端职责
- **数据库设计**：创建venues表，修改events表结构
- **API设计**：提供venues CRUD接口，调整events API
- **数据本地化**：根据locale参数返回对应语言的数据
- **数据迁移**：平滑迁移现有数据

### 前端职责
- **数据消费**：接收并展示本地化后的数据
- **组件适配**：更新现有组件以支持新的数据结构
- **用户交互**：提供venue选择、搜索等功能
- **错误处理**：处理API调用的各种状态

## 🚀 迁移阶段

### 阶段1：后端基础设施 (Week 1-2)
1. **数据库迁移**
   - 创建venues表
   - 迁移现有venue数据
   - 修改events表结构
   - 创建必要的索引

2. **API开发**
   ```typescript
   // 新增venues API
   GET /api/venues
   GET /api/venues/{id}
   POST /api/venues
   PUT /api/venues/{id}
   DELETE /api/venues/{id}
   
   // 调整events API
   GET /api/events/{id} // 返回包含venue信息
   ```

3. **数据本地化逻辑**
   ```typescript
   // 后端工具函数
   function localizeVenue(venue: Venue, locale: string): LocalizedVenue {
     return {
       id: venue.id,
       name: venue[`name_${locale}`],
       address: venue[`address_${locale}`],
       description: venue[`description_${locale}`],
       // ... 其他字段
     }
   }
   ```

### 阶段2：前端适配 (Week 3)
1. **类型定义更新**
   - 更新Event类型以包含venue关联
   - 创建FrontendVenue类型
   - 更新相关组件Props

2. **组件兼容性**
   ```typescript
   // 更新createVenueFromEvent函数
   // 支持新旧两种数据结构
   function createVenueFromEvent(event: Event): Venue {
     // 优先使用新结构
     if (event.venue) return event.venue
     // 兼容旧结构
     if (event.venue_lat && event.venue_lng) {
       return { /* 旧结构转换 */ }
     }
     return DEFAULT_VENUE
   }
   ```

3. **API调用更新**
   - 更新useGetEventsId hook
   - 确保locale参数正确传递
   - 处理新的数据结构

### 阶段3：功能增强 (Week 4)
1. **管理后台**
   - 新增venue管理页面
   - 更新event编辑页面的venue选择
   - 添加venue搜索和筛选功能

2. **用户界面优化**
   - 增强venue信息展示
   - 添加venue详情页面
   - 优化地图组件功能

## 🔧 技术实现细节

### 数据库迁移脚本
```sql
-- 见 db/migrations/004_create_venues_table.sql
-- 包含完整的迁移逻辑和数据转换
```

### API接口设计
```typescript
// venues API响应格式
interface VenueResponse {
  id: string
  name: string        // 已本地化
  address?: string    // 已本地化
  description?: string // 已本地化
  lat: number
  lng: number
  // ... 其他字段
}

// events API响应格式
interface EventResponse {
  id: string
  name: string        // 已本地化
  date: string        // 已本地化
  venue_id: string
  venue?: VenueResponse // 关联的venue信息
  // ... 其他字段
}
```

### 前端组件更新
```typescript
// EventMap组件适配
const EventMap = ({ venue }: { venue: FrontendVenue }) => {
  // 直接使用已本地化的venue数据
  return <VenueLocationMap venue={venue} />
}

// Event详情页面
const EventDetailPage = () => {
  const { data: event } = useGetEventsId(id)
  const venue = event?.venue || createVenueFromEvent(event)
  
  return (
    <div>
      <EventInfo event={event} />
      <EventMap venue={venue} />
    </div>
  )
}
```

## ⚠️ 风险控制

### 数据一致性
- 在迁移过程中保持数据备份
- 使用事务确保迁移的原子性
- 提供回滚方案

### API兼容性
- 保持现有API的向后兼容
- 使用版本控制管理API变更
- 提供迁移期间的双重支持

### 前端稳定性
- 渐进式更新组件
- 保持旧数据结构的兼容性
- 充分的测试覆盖

## 📊 成功指标

### 技术指标
- [ ] 数据迁移成功率 100%
- [ ] API响应时间 < 200ms
- [ ] 前端组件无报错
- [ ] 测试覆盖率 > 90%

### 业务指标
- [ ] 用户体验无明显变化
- [ ] 管理后台功能正常
- [ ] 多语言切换正常
- [ ] 地图功能正常

## 📅 时间计划

| 阶段 | 时间 | 主要任务 | 负责人 |
|------|------|----------|--------|
| 阶段1 | Week 1-2 | 后端基础设施开发 | 后端团队 |
| 阶段2 | Week 3 | 前端适配和测试 | 前端团队 |
| 阶段3 | Week 4 | 功能增强和优化 | 全团队 |
| 验收 | Week 5 | 全面测试和上线 | 全团队 |
