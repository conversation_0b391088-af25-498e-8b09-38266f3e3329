"use client";

// mock sonner toast 要在导入之前
vi.mock('sonner', () => ({
  toast: {
    error: vi.fn(),
    success: vi.fn(),
  },
}));

import { useRouter } from 'next/navigation';
import React from 'react';
import type { Mock } from 'vitest';

import AdminEventsPage from '../page';

import { useAdminEvents } from '@/hooks/admin/useAdminEvents';
import { useDeleteEvent } from '@/hooks/admin/useDeleteEvent';
import { renderWithProviders, screen, fireEvent, waitFor } from '@test/test-utils';

// Mock next/navigation
vi.mock('next/navigation', () => ({
  useRouter: vi.fn(),
}));

// Mock the old API hooks
vi.mock('@/api/generated/ayafeedComponents', () => ({
  useGetAdminEvents: vi.fn(),
  useDeleteAdminEventsId: vi.fn(),
}));

// Mock the useAdminEvents hook directly
vi.mock('@/hooks/admin/useAdminEvents', () => ({
  useAdminEvents: vi.fn(),
}));

// Mock the useDeleteEvent hook
vi.mock('@/hooks/admin/useDeleteEvent', () => ({
  useDeleteEvent: vi.fn(),
}));

// Mock next-intl
vi.mock('next-intl', async (importOriginal) => {
  const actual = await importOriginal() as Record<string, any>;
  return {
    ...actual,
    useLocale: vi.fn(() => 'ja'),
    NextIntlClientProvider: ({ children }: { children: React.ReactNode }) => children,
  };
});

// 新增 mock show-error
vi.mock('@/lib/show-error', () => ({
  showApiError: vi.fn(),
}));

const mockPush = vi.fn();
const mockEvents = [
  { id: '1', name: 'Event 1', date: '2023-01-01', venue_name: 'Venue 1', venue_address: 'Address 1' },
  { id: '2', name: 'Event 2', date: '2023-01-02', venue_name: 'Venue 2', venue_address: 'Address 2' },
];

const mockDeleteMutation = {
  mutate: vi.fn(),
  isPending: false,
};

describe('AdminEventsPage', () => {
  beforeEach(() => {
    vi.resetAllMocks();
    (useRouter as Mock).mockReturnValue({ push: mockPush });

    // Mock the hooks
    (useAdminEvents as Mock).mockReturnValue({
      data: mockEvents,
      isLoading: false,
      error: null,
      pagination: {
        total: 2,
        page: 1,
        pageSize: 20,
      }
    });

    (useDeleteEvent as Mock).mockReturnValue(mockDeleteMutation);
  });

  test('should show loading state and then display events', async () => {
    // First show loading state
    (useAdminEvents as Mock).mockReturnValue({
      data: [],
      isLoading: true,
      error: null,
      pagination: undefined
    });

    const { rerender } = renderWithProviders(<AdminEventsPage />);

    expect(screen.getByText('加载中...')).toBeInTheDocument();

    // Then show data
    (useAdminEvents as Mock).mockReturnValue({
      data: mockEvents,
      isLoading: false,
      error: null,
      pagination: {
        total: 2,
        page: 1,
        pageSize: 20,
      }
    });

    rerender(<AdminEventsPage />);

    await waitFor(() => {
      expect(screen.getByText('Event 1')).toBeInTheDocument();
      expect(screen.getByText('Event 2')).toBeInTheDocument();
    });
  });

  test('should navigate to new event page on button click', async () => {
    renderWithProviders(<AdminEventsPage />);

    await waitFor(() => {
      fireEvent.click(screen.getByText('新增展会'));
    });

    expect(mockPush).toHaveBeenCalledWith('/admin/events/new');
  });

  test('should call delete handler and refetch events', async () => {
    renderWithProviders(<AdminEventsPage />);

    await waitFor(() => {
      expect(screen.getByText('Event 1')).toBeInTheDocument();
    });

    // Mock delete confirmation
    window.confirm = vi.fn(() => true);

    const deleteButton = screen.getAllByText('删除')[0];
    fireEvent.click(deleteButton);

    expect(window.confirm).toHaveBeenCalledWith('确定删除该展会吗？');
    expect(mockDeleteMutation.mutate).toHaveBeenCalledWith('1', expect.any(Object));
  });

  test('should show toast on delete failure', async () => {
    renderWithProviders(<AdminEventsPage />);

    await waitFor(() => {
      expect(screen.getByText('Event 1')).toBeInTheDocument();
    });

    // Mock delete confirmation
    window.confirm = vi.fn(() => true);

    const deleteButton = screen.getAllByText('删除')[0];
    fireEvent.click(deleteButton);

    expect(window.confirm).toHaveBeenCalledWith('确定删除该展会吗？');
    expect(mockDeleteMutation.mutate).toHaveBeenCalled();
  });
}); 