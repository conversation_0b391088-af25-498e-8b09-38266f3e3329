import type {SidebarsConfig} from '@docusaurus/plugin-content-docs';

/**
 * 创建侧边栏配置，定义文档的导航结构
 */
const sidebars: SidebarsConfig = {
  // 主要的文档侧边栏
  tutorialSidebar: [
    {
      type: 'category',
      label: '介绍',
      items: [
        'introduction/overview',
        'introduction/roadmap',
      ],
    },
    {
      type: 'category',
      label: '快速开始',
      items: [
        'getting-started/prerequisites',
        'getting-started/installation',
        'getting-started/faq',
      ],
    },
    {
      type: 'category',
      label: '前端开发',
      items: [
        'frontend/quick-start',
        'frontend/client-generation',
        'frontend/authentication',
        'frontend/i18n-integration',
        'frontend/error-handling',
        'frontend/business-flows',
        'frontend/common-examples',
        'frontend/faq',
      ],
    },
    {
      type: 'category',
      label: 'API 文档',
      items: [
        'api/README',
        'api-specification',
      ],
    },
    {
      type: 'category',
      label: '架构设计',
      items: [
        'architecture/system-design',
        'architecture/frontend',
        'architecture/backend',
        'architecture/ci-cd',
        'architecture/shadcn-to-radix-migration',
      ],
    },
    {
      type: 'category',
      label: '开发指南',
      items: [
        'guides/contribution',
        'guides/i18n',
        'guides/performance',
        'guides/testing',
      ],
    },
    {
      type: 'category',
      label: '测试',
      items: [
        'testing/enhanced-components-testing-summary',
      ],
    },
    {
      type: 'category',
      label: '设计改进',
      items: [
        'design-improvements/event-detail-page-redesign',
        'design-improvements/map-optimization',
        'design-improvements/map-optimization-demo',
        'design-improvements/visual-enhancements-summary',
      ],
    },
    {
      type: 'category',
      label: 'ADR (架构决策记录)',
      items: [
        'adr/README',
        'adr/init',
        'adr/cookie-based-auth',
        'adr/i18n-events',
      ],
    },
    {
      type: 'category',
      label: '变更日志',
      items: [
        'changelog/CHANGELOG',
      ],
    },
    {
      type: 'category',
      label: '许可证',
      items: [
        'licenses/third-party',
      ],
    },
    // 单独的文档页面
    'API_INTEGRATION',
    'backend-implementation-examples',
    'multilingual-caching-best-practices',
    'next-project',
    'TODO',
    'admin-events-update-summary',
  ],
};

export default sidebars;
