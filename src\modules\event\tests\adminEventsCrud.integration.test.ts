import { describe, it, expect } from 'vitest';

import app from '@/app';

// @ts-ignore
const Request = globalThis.Request;

function createMockDB(role: 'admin' | 'viewer' = 'admin') {
  return {
    prepare: (query: string) => {
      const upper = query.toUpperCase();

      const buildResponse = () => ({
        all: async () => {
          if (upper.includes('FROM AUTH_SESSION')) {
            return {
              results: role ? [{ id: 'u1', username: 'tester', role }] : [],
            };
          }
          return { results: [] };
        },
        first: async () => {
          if (
            upper.includes('FROM EVENTS') ||
            upper.includes('FROM AUTH_SESSION')
          ) {
            if (upper.includes('FROM AUTH_SESSION')) {
              return role ? { id: 'u1', username: 'tester', role } : null;
            }
            // Mock event with venue data for JOIN query
            return {
              id: 'e1',
              name_en: 'Event1 EN',
              name_ja: 'イベント1 JA',
              name_zh: 'イベント1 ZH',
              date_en: 'May 1, 2025',
              date_ja: '2025年5月1日',
              date_zh: '2025年5月1日',
              date_sort: 20250501,
              image_url: null,
              venue_id: 'test-venue-id',
              url: null,
              created_at: '2024-01-01T00:00:00Z',
              updated_at: '2024-01-01T00:00:00Z',
              // Venue data from JOIN
              venue_name_en: 'Test Venue EN',
              venue_name_ja: 'テスト会場',
              venue_name_zh: '测试场馆',
              venue_address_en: 'Test Address EN',
              venue_address_ja: 'テスト住所',
              venue_address_zh: '测试地址',
              venue_lat: 35.0,
              venue_lng: 139.0,
              venue_capacity: 1000,
              venue_website_url: 'https://test-venue.com',
              venue_phone: '+81-3-1234-5678',
              venue_description_en: 'Test venue description',
              venue_description_ja: 'テスト会場の説明',
              venue_description_zh: '测试场馆描述',
              venue_facilities: '{"wifi": true, "parking": true}',
              venue_transportation: '{"train": "Test Line"}',
              venue_parking_info: '{"spaces": 100, "fee": "¥500/day"}',
            };
          }
          return null;
        },
        run: async () => ({ success: true }),
      });

      return {
        ...buildResponse(),
        bind: (..._args: any[]) => buildResponse(),
      };
    },
  };
}

function fetchWithEnv(req: Request, env: any) {
  return app.fetch(req, env);
}

describe('/admin/events CRUD', () => {
  const baseHeaders = {
    'Content-Type': 'application/json',
    Cookie: 'auth_session=admin_session',
  } as const;

  it('should create event successfully', async () => {
    const body = {
      name_en: 'Event1 EN',
      name_ja: 'イベント1 JA',
      name_zh: 'イベント1 ZH',
      date_en: 'May 1, 2025',
      date_ja: '2025年5月1日',
      date_zh: '2025年5月1日',
      date_sort: 20250501,
      venue_id: 'test-venue-id',
    } as const;

    const req = new Request('http://localhost/admin/events', {
      method: 'POST',
      headers: baseHeaders,
      body: JSON.stringify(body),
    });

    const res = await fetchWithEnv(req, { DB: createMockDB() });

    // Debug: print response if not 201
    if (res.status !== 201) {
      const text = await res.text();
      console.log('Response status:', res.status);
      console.log('Response body:', text);
    }

    expect(res.status).toBe(201);
    expect(res.headers.get('X-Success-Message')).toBe(
      encodeURIComponent('展会创建成功')
    );
    const json = (await res.json()) as any;
    expect(json.message).toBe('展会创建成功');
  });

  it('should update event successfully', async () => {
    const body = { name_en: 'Updated EN' };
    const req = new Request('http://localhost/admin/events/e1', {
      method: 'PATCH',
      headers: baseHeaders,
      body: JSON.stringify(body),
    });

    const res = await fetchWithEnv(req, { DB: createMockDB() });
    expect(res.status).toBe(200);
    expect(res.headers.get('X-Success-Message')).toBe(
      encodeURIComponent('展会已保存')
    );
    const json = (await res.json()) as any;
    expect(json.message).toBe('展会已保存');
  });

  it('should delete event successfully', async () => {
    const req = new Request('http://localhost/admin/events/e1', {
      method: 'DELETE',
      headers: baseHeaders,
    });

    const res = await fetchWithEnv(req, { DB: createMockDB() });
    expect(res.status).toBe(204);
    expect(res.headers.get('X-Success-Message')).toBe(
      encodeURIComponent('展会已删除')
    );
  });
});
