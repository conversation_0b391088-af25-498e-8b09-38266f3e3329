"use client";

import { useRouter } from "next/navigation";

import { Button } from "@/components/ui/button";
import { useZodForm } from "@/hooks";
import { useCreateCircle } from "@/hooks/admin/useCreateCircle";
import { showApiError } from "@/lib/show-error";
import { CircleInputSchema, CircleInput } from "@/schemas/circle";

export default function NewCirclePage() {
  const router = useRouter();

  const { register, handleSubmit, formState: { errors }, reset } = useZodForm(CircleInputSchema, {
    defaultValues: {
      name: "",
      author: "",
    },
    mode: "onBlur",
  })

  const createCircle = useCreateCircle();

  async function onSubmit(values: CircleInput) {
    createCircle.mutate(values, {
      onSuccess: (data: any) => {
        reset();
        // 若后端返回新建圈子 id，则跳转至前台详情页；否则回到列表
        if (data && typeof data === "object" && "id" in data) {
          router.push(`/circles/${(data as any).id}`);
        } else {
          router.push("/admin/circles");
        }
      },
      onError: (err) => { showApiError(err); },
    });
  }

  return (
    <div className="max-w-xl space-y-4">
      <h1 className="text-2xl font-bold">新增社团</h1>
      <form onSubmit={handleSubmit(onSubmit)} className="space-y-4">
        <div>
          <label htmlFor="circle-name" className="block text-sm mb-1">名称 *</label>
          <input
            id="circle-name"
            type="text"
            {...register("name")}
            className="w-full border rounded px-3 py-2"
          />
          {errors.name && <p className="text-destructive text-xs mt-1">{errors.name.message}</p>}
        </div>
        <div>
          <label className="block text-sm mb-1">作者</label>
          <input
            type="text"
            {...register("author")}
            className="w-full border rounded px-3 py-2"
          />
        </div>
        <Button type="submit" disabled={createCircle.isPending}>{createCircle.isPending ? "保存中..." : "保存"}</Button>
      </form>
    </div>
  )
} 