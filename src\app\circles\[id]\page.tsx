"use client"

import { skipToken } from "@tanstack/react-query";
import { useParams } from "next/navigation";

import {
  useGetCirclesId,
  useGetAppearances,
} from "@/api/generated/ayafeedComponents";
import CircleHeader from "@/components/circles/CircleHeader";
import AppearancesGrid from "@/components/circles/AppearancesGrid";

export default function CircleDetailPage() {
  const { id } = useParams<{ id: string }>();

  // 基本社团信息
  const { data: circle } = useGetCirclesId(
    id ? { pathParams: { id } } : skipToken,
    {
      // 后端部分字段可能为 null，需要转换类型
      select: (data) =>
        data
          ? {
              ...data,
              urls: data.urls ?? "{}",
            }
          : null,
    },
  );

  // 参展记录（分页上限 500 足够）
  const { data: appearancesRes } = useGetAppearances(
    id
      ? { queryParams: { circle_id: id, page: "1", pageSize: "500" } }
      : skipToken,
    {
      select: (data) => data?.items ?? [],
      placeholderData: (prev) => prev,
      enabled: !!id,
      staleTime: 5 * 60 * 1000,
    },
  );

  return (
    <div className="min-h-screen bg-background text-foreground">
      {/* 页头：社团信息 */}
      <CircleHeader circle={circle ?? null} />

      <main className="max-w-7xl mx-auto flex flex-col gap-10 px-4 py-10">
        <section className="flex flex-col gap-6">
          <h2 className="text-2xl font-semibold">参展记录</h2>
          <AppearancesGrid data={(appearancesRes ?? []) as any} />
        </section>
      </main>
    </div>
  );
} 