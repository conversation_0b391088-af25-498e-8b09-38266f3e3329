#!/usr/bin/env tsx
/**
 * 代码生成后处理脚本
 * 
 * 在 openapi-codegen 生成代码后自动应用语言支持的修改
 * 确保重新生成代码时不会丢失语言切换功能
 */

import fs from 'fs';
import path from 'path';

const CONTEXT_FILE = path.join(process.cwd(), 'src/api/generated/ayafeedContext.ts');

/**
 * 检查文件是否存在
 */
function fileExists(filePath: string): boolean {
  try {
    return fs.statSync(filePath).isFile();
  } catch {
    return false;
  }
}

/**
 * 为 ayafeedContext.ts 添加语言支持
 */
function patchContextFile(): boolean {
  if (!fileExists(CONTEXT_FILE)) {
    console.error(`❌ Context file not found: ${CONTEXT_FILE}`);
    return false;
  }

  let content = fs.readFileSync(CONTEXT_FILE, 'utf8');
  let modified = false;

  // 1. 添加 getCurrentLocale 导入
  if (!content.includes('getCurrentLocale')) {
    const importRegex = /import { QueryOperation } from "\.\/ayafeedComponents";/;
    if (importRegex.test(content)) {
      content = content.replace(
        importRegex,
        `import { QueryOperation } from "./ayafeedComponents";
import { getCurrentLocale } from "@/lib/locale-utils";`
      );
      modified = true;
      console.log('✅ Added getCurrentLocale import');
    }
  }

  // 2. 修改 useAyafeedContext 函数
  const originalReturnPattern = /return\s*\{\s*fetcherOptions:\s*\{\},\s*queryOptions:\s*\{\},?\s*\};/s;
  
  if (originalReturnPattern.test(content) && !content.includes('locale: currentLocale')) {
    const newReturnStatement = `// 自动添加当前语言到查询参数，确保不同语言的数据有独立的缓存
  const currentLocale = getCurrentLocale();
  
  return {
    fetcherOptions: {
      queryParams: {
        locale: currentLocale,
      },
    },
    queryOptions: {},
  };`;

    content = content.replace(originalReturnPattern, newReturnStatement);
    modified = true;
    console.log('✅ Updated useAyafeedContext function');
  }

  // 3. 写回文件
  if (modified) {
    fs.writeFileSync(CONTEXT_FILE, content, 'utf8');
    console.log('✅ Context file patched successfully');
    return true;
  } else {
    console.log('ℹ️ Context file already contains locale support');
    return true;
  }
}

/**
 * 验证修改是否成功
 */
function verifyPatch(): boolean {
  if (!fileExists(CONTEXT_FILE)) {
    return false;
  }

  const content = fs.readFileSync(CONTEXT_FILE, 'utf8');
  
  const hasImport = content.includes('getCurrentLocale');
  const hasLocaleLogic = content.includes('locale: currentLocale');
  
  if (hasImport && hasLocaleLogic) {
    console.log('✅ Verification passed: locale support is present');
    return true;
  } else {
    console.error('❌ Verification failed:');
    if (!hasImport) console.error('  - Missing getCurrentLocale import');
    if (!hasLocaleLogic) console.error('  - Missing locale logic in useAyafeedContext');
    return false;
  }
}

/**
 * 主函数
 */
function main() {
  console.log('🔧 Starting post-codegen processing...');
  
  try {
    const success = patchContextFile();
    
    if (success) {
      const verified = verifyPatch();
      if (verified) {
        console.log('🎉 Post-codegen processing completed successfully!');
        process.exit(0);
      } else {
        console.error('❌ Post-codegen processing failed verification');
        process.exit(1);
      }
    } else {
      console.error('❌ Post-codegen processing failed');
      process.exit(1);
    }
  } catch (error) {
    console.error('❌ Post-codegen processing error:', error);
    process.exit(1);
  }
}

// 运行主函数
if (require.main === module) {
  main();
}

export { patchContextFile, verifyPatch };
