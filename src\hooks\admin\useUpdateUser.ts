import { useQueryClient } from "@tanstack/react-query";

import { queryKeys } from "@/constants/queryKeys";
import { useAdminSuccessToast } from "@/hooks/useAdminSuccessToast";
import { request } from "@/lib/http";

type UserInput = Record<string, any>;

export function useUpdateUser(id: string) {
  const qc = useQueryClient();
  return useAdminSuccessToast(
    (payload: UserInput) =>
      request(`/admin/users/${id}`, {
        method: "PUT",
        body: JSON.stringify(payload),
      }),
    {
      onSuccess: () => {
        qc.invalidateQueries({ queryKey: queryKeys.adminUsers() });
        qc.invalidateQueries({ queryKey: queryKeys.adminUserDetail(id) });
      },
    },
    "修改成功"
  );
} 