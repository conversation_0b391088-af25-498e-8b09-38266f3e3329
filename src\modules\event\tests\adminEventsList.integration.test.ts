import { describe, it, expect } from 'vitest';

import app from '@/app';

// @ts-ignore
const Request = globalThis.Request;

function createMockDB(role: 'admin' | 'viewer' = 'admin') {
  return {
    prepare: (query: string) => {
      const upper = query.toUpperCase();

      const buildResponse = () => ({
        all: async () => {
          if (upper.includes('FROM AUTH_SESSION')) {
            return {
              results: role ? [{ id: 'u1', username: 'tester', role }] : [],
            };
          }
          if (upper.includes('FROM EVENTS') && !upper.includes('COUNT(*)')) {
            return {
              results: [
                { id: 'e11', name: 'Event 11', date_sort: 20250501 },
                { id: 'e12', name: 'Event 12', date_sort: 20250502 },
              ],
            };
          }
          return { results: [] };
        },
        first: async () => {
          if (upper.includes('COUNT(*)')) {
            return { total: 2 };
          }
          if (upper.includes('FROM AUTH_SESSION')) {
            return { id: 'u1', username: 'tester', role };
          }
          return null;
        },
        run: async () => ({ success: true }),
        bind: (..._args: any[]) => buildResponse(),
      });

      return buildResponse();
    },
  };
}

describe('/admin/events list', () => {
  const baseHeaders = {
    Cookie: 'auth_session=admin_session',
  } as const;

  it('should return paginated list', async () => {
    const req = new Request(
      'http://localhost/admin/events?page=1&pageSize=2&keyword=test',
      {
        headers: baseHeaders,
      }
    );

    const res = await app.fetch(req, { DB: createMockDB() });
    expect(res.status).toBe(200);
    const json = (await res.json()) as any;
    expect(json.page).toBe(1);
    expect(json.pageSize).toBe(2);
    expect(json.total).toBe(2);
    expect(json.items.length).toBe(2);
  });
});
