import type { D1Database } from '@cloudflare/workers-types';
import bcrypt from 'bcryptjs';
import { v4 as uuidv4 } from 'uuid';

import { createUserRepository } from './repository';
import type { CreateUserData, UpdateUserData } from './repository';
import { User, UserCreateInput, UserUpdateInput } from './schema';
import type { Cache, Logger } from '@/infrastructure';

/**
 * 列出所有用户，带缓存
 */
export async function listUsers(
  db: D1Database,
  cache?: Cache,
  logger?: Logger
): Promise<User[]> {
  const cacheKey = 'users_all';
  if (cache) {
    const cached = await cache.get<User[]>(cacheKey);
    if (cached) {
      logger?.debug?.('listUsers: hit cache');
      return cached;
    }
  }

  const repo = createUserRepository(db);
  const users = await repo.list();
  if (cache) {
    await cache.set(cacheKey, users, 300);
  }
  return users;
}

/**
 * 获取单个用户
 */
export async function getUser(
  db: D1Database,
  id: string
): Promise<User | null> {
  const repo = createUserRepository(db);
  return repo.findById(id);
}

/**
 * 创建用户
 */
export async function createUser(
  db: D1Database,
  input: UserCreateInput
): Promise<User> {
  const hashedPassword = await bcrypt.hash(input.password, 12);
  const data: CreateUserData = {
    id: uuidv4(),
    username: input.username,
    role: input.role ?? 'viewer',
    hashedPassword,
  };
  const repo = createUserRepository(db);
  return repo.create(data);
}

/**
 * 更新用户
 */
export async function updateUser(
  db: D1Database,
  id: string,
  input: UserUpdateInput
): Promise<User | null> {
  const repo = createUserRepository(db);
  const changes: UpdateUserData = {
    username: input.username,
    role: input.role,
  };
  const updated = await repo.update(id, changes);
  if (input.password !== undefined) {
    const hashed = await bcrypt.hash(input.password, 12);
    await repo.updatePassword(id, hashed);
  }
  return updated;
}

/**
 * 删除用户
 */
export async function deleteUser(db: D1Database, id: string): Promise<void> {
  const repo = createUserRepository(db);
  await repo.delete(id);
}
