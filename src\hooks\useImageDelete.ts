/**
 * 图片删除 Hook
 * 基于 React Query 的图片删除操作
 */

import { useMutation, useQueryClient } from '@tanstack/react-query';
import { toast } from 'sonner';
import { ImageService } from '@/services/imageService';
import type { ImageInfo } from '@/types/image';

interface UseImageDeleteOptions {
  onSuccess?: () => void;
  onError?: (error: Error) => void;
  showToast?: boolean;
}

export function useImageDelete(options: UseImageDeleteOptions = {}) {
  const { onSuccess, onError, showToast = true } = options;
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (relativePaths: string[]) => ImageService.delete(relativePaths),
    onSuccess: () => {
      // 刷新所有图片查询
      queryClient.invalidateQueries({
        queryKey: ['images'],
      });

      if (showToast) {
        toast.success('Images deleted successfully');
      }

      onSuccess?.();
    },
    onError: (error: Error) => {
      if (showToast) {
        toast.error(`Delete failed: ${error.message}`);
      }

      onError?.(error);
    },
  });
}

interface UseBatchImageDeleteOptions {
  onSuccess?: (deletedCount: number) => void;
  onError?: (error: Error) => void;
  showToast?: boolean;
}

export function useBatchImageDelete(options: UseBatchImageDeleteOptions = {}) {
  const { onSuccess, onError, showToast = true } = options;
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (images: ImageInfo[]) => {
      const relativePaths = images.map(img => img.relativePath);
      return ImageService.delete(relativePaths);
    },
    onSuccess: (data, variables) => {
      // 刷新所有图片查询
      queryClient.invalidateQueries({
        queryKey: ['images'],
      });

      const deletedCount = variables.length;
      if (showToast) {
        toast.success(`${deletedCount} images deleted successfully`);
      }

      onSuccess?.(deletedCount);
    },
    onError: (error: Error) => {
      if (showToast) {
        toast.error(`Batch delete failed: ${error.message}`);
      }

      onError?.(error);
    },
  });
}
