#!/usr/bin/env tsx
/**
 * Venues表迁移执行脚本
 *
 * 用法：
 *   pnpm tsx scripts/migrate-venues.ts --env=development
 *   pnpm tsx scripts/migrate-venues.ts --env=production --confirm
 *
 * 功能：
 *   1. 创建venues表
 *   2. 从events表迁移venue数据
 *   3. 修改events表结构
 *   4. 验证迁移结果
 */

import { readFileSync } from 'fs';
import path from 'path';
import minimist from 'minimist';

// 解析命令行参数
const argv = minimist(process.argv.slice(2), {
  string: ['env'],
  boolean: ['confirm', 'dry-run', 'rollback'],
  default: {
    env: 'development',
    confirm: false,
    'dry-run': false,
    rollback: false,
  },
});

const { env, confirm, 'dry-run': dryRun, rollback } = argv;

// 环境配置
const configs = {
  development: {
    databaseId: process.env.CLOUDFLARE_D1_DATABASE_ID_DEV,
    accountId: process.env.CLOUDFLARE_ACCOUNT_ID,
    apiToken: process.env.CLOUDFLARE_API_TOKEN,
  },
  production: {
    databaseId: process.env.CLOUDFLARE_D1_DATABASE_ID_PROD,
    accountId: process.env.CLOUDFLARE_ACCOUNT_ID,
    apiToken: process.env.CLOUDFLARE_API_TOKEN,
  },
};

const config = configs[env as keyof typeof configs];

if (!config) {
  console.error(`❌ 不支持的环境: ${env}`);
  process.exit(1);
}

if (!config.databaseId || !config.accountId || !config.apiToken) {
  console.error('❌ 缺少必要的环境变量:');
  console.error('   CLOUDFLARE_D1_DATABASE_ID_DEV/PROD');
  console.error('   CLOUDFLARE_ACCOUNT_ID');
  console.error('   CLOUDFLARE_API_TOKEN');
  process.exit(1);
}

/**
 * 执行D1数据库查询
 */
async function executeD1Query(sql: string, params: any[] = []) {
  const url = `https://api.cloudflare.com/client/v4/accounts/${config.accountId}/d1/database/${config.databaseId}/query`;

  const response = await fetch(url, {
    method: 'POST',
    headers: {
      Authorization: `Bearer ${config.apiToken}`,
      'Content-Type': 'application/json',
    },
    body: JSON.stringify({
      sql,
      params,
    }),
  });

  if (!response.ok) {
    const error = await response.text();
    throw new Error(`D1 查询失败: ${response.status} ${error}`);
  }

  const result = await response.json();
  if (!result.success) {
    throw new Error(`D1 查询错误: ${JSON.stringify(result.errors)}`);
  }

  return result.result[0];
}

/**
 * 检查表是否存在
 */
async function tableExists(tableName: string): Promise<boolean> {
  try {
    const result = await executeD1Query(
      "SELECT name FROM sqlite_master WHERE type='table' AND name=?",
      [tableName]
    );
    return result.results.length > 0;
  } catch (error) {
    console.error(`检查表 ${tableName} 时出错:`, error);
    return false;
  }
}

/**
 * 获取表的行数
 */
async function getTableCount(tableName: string): Promise<number> {
  try {
    const result = await executeD1Query(
      `SELECT COUNT(*) as count FROM ${tableName}`
    );
    return result.results[0].count;
  } catch (error) {
    console.error(`获取表 ${tableName} 行数时出错:`, error);
    return 0;
  }
}

/**
 * 执行迁移
 */
async function runMigration() {
  console.log(`🚀 开始执行venues表迁移 (环境: ${env})`);

  if (dryRun) {
    console.log('🔍 DRY RUN 模式 - 不会实际执行迁移');
  }

  // 1. 检查当前状态
  console.log('\n📊 检查当前数据库状态...');
  const venuesExists = await tableExists('venues');
  const eventsCount = await getTableCount('events');

  console.log(`   - venues表存在: ${venuesExists ? '✅' : '❌'}`);
  console.log(`   - events表记录数: ${eventsCount}`);

  if (venuesExists && !rollback) {
    console.log('⚠️  venues表已存在，迁移可能已经执行过');
    if (!confirm) {
      console.log('使用 --confirm 参数强制执行');
      return;
    }
  }

  // 2. 读取迁移脚本
  const migrationPath = path.resolve(
    __dirname,
    '../db/migrations/004_create_venues_table.sql'
  );
  let migrationSQL: string;

  try {
    migrationSQL = readFileSync(migrationPath, 'utf-8');
  } catch (_error) {
    console.error(`❌ 无法读取迁移脚本: ${migrationPath}`);
    process.exit(1);
  }

  // 3. 执行迁移
  if (!dryRun) {
    console.log('\n🔄 执行迁移脚本...');

    try {
      // 分割SQL语句并逐个执行
      const statements = migrationSQL
        .split(';')
        .map((stmt) => stmt.trim())
        .filter((stmt) => stmt.length > 0 && !stmt.startsWith('--'));

      for (const statement of statements) {
        if (
          statement.toLowerCase().includes('begin') ||
          statement.toLowerCase().includes('commit')
        ) {
          continue; // D1不支持显式事务
        }

        console.log(`   执行: ${statement.substring(0, 50)}...`);
        await executeD1Query(statement);
      }

      console.log('✅ 迁移脚本执行完成');
    } catch (error) {
      console.error('❌ 迁移执行失败:', error);
      process.exit(1);
    }
  }

  // 4. 验证迁移结果
  console.log('\n🔍 验证迁移结果...');

  if (!dryRun) {
    const newVenuesExists = await tableExists('venues');
    const venuesCount = await getTableCount('venues');
    const newEventsCount = await getTableCount('events');

    console.log(`   - venues表创建: ${newVenuesExists ? '✅' : '❌'}`);
    console.log(`   - venues表记录数: ${venuesCount}`);
    console.log(`   - events表记录数: ${newEventsCount}`);

    if (newVenuesExists && venuesCount > 0) {
      console.log('✅ 迁移验证成功');
    } else {
      console.log('⚠️  迁移验证失败，请检查数据');
    }
  }

  console.log('\n🎉 venues表迁移完成！');
}

/**
 * 回滚迁移
 */
async function rollbackMigration() {
  console.log(`🔄 开始回滚venues表迁移 (环境: ${env})`);

  if (!confirm) {
    console.log('⚠️  回滚操作需要确认，请使用 --confirm 参数');
    return;
  }

  if (dryRun) {
    console.log('🔍 DRY RUN 模式 - 不会实际执行回滚');
    return;
  }

  try {
    // 检查venues表是否被events使用
    const result = await executeD1Query(
      'SELECT COUNT(*) as count FROM events WHERE venue_id IS NOT NULL'
    );
    const eventsWithVenues = result.results[0].count;

    if (eventsWithVenues > 0) {
      console.log(
        `⚠️  有 ${eventsWithVenues} 个events记录使用了venue_id，无法安全回滚`
      );
      return;
    }

    // 删除venues表
    await executeD1Query('DROP TABLE IF EXISTS venues');
    console.log('✅ venues表已删除');

    console.log('🎉 回滚完成！');
  } catch (error) {
    console.error('❌ 回滚失败:', error);
    process.exit(1);
  }
}

/**
 * 主函数
 */
async function main() {
  try {
    if (rollback) {
      await rollbackMigration();
    } else {
      await runMigration();
    }
  } catch (error) {
    console.error('❌ 脚本执行失败:', error);
    process.exit(1);
  }
}

// 显示帮助信息
if (argv.help || argv.h) {
  console.log(`
Venues表迁移脚本

用法:
  pnpm tsx scripts/migrate-venues.ts [选项]

选项:
  --env=<env>        环境 (development|production, 默认: development)
  --confirm          确认执行 (生产环境必需)
  --dry-run          预演模式，不实际执行
  --rollback         回滚迁移
  --help, -h         显示帮助信息

示例:
  pnpm tsx scripts/migrate-venues.ts --env=development
  pnpm tsx scripts/migrate-venues.ts --env=production --confirm
  pnpm tsx scripts/migrate-venues.ts --dry-run
  pnpm tsx scripts/migrate-venues.ts --rollback --confirm
`);
  process.exit(0);
}

// 生产环境安全检查
if (env === 'production' && !confirm) {
  console.log('⚠️  生产环境迁移需要确认，请使用 --confirm 参数');
  process.exit(1);
}

main();
