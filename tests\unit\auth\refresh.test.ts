import { describe, it, expect, vi, beforeEach } from 'vitest';
import type { Context } from 'hono';
import { jsonError } from '@/utils/errorResponse';

// Refresh endpoint handler function (extracted from routes.ts)
const refreshHandler = async (c: Context) => {
  const authContext = c.get('auth');

  if (!authContext || !authContext.user || !authContext.session) {
    return jsonError(c, 20001, '未登录', 401);
  }

  const user = authContext.user;

  // 延长会话过期时间（可选）
  const db = c.env.DB;
  const sessionExpiresIn = 1000 * 60 * 60 * 24 * 30; // 30 天
  const newExpiresAt = new Date(Date.now() + sessionExpiresIn);

  try {
    await db
      .prepare('UPDATE auth_session SET expires_at = ? WHERE id = ?')
      .bind(newExpiresAt.toISOString(), authContext.session.id)
      .run();
  } catch (error) {
    // 如果更新失败，仍然返回用户信息，因为会话仍然有效
    console.warn('Failed to extend session:', error);
  }

  return c.json({
    code: 0,
    message: '会话刷新成功',
    data: user,
  });
};

// Mock database
const createMockDB = (shouldFailUpdate = false) => {
  const mockPreparedStatement = {
    bind: vi.fn().mockReturnThis(),
    run: vi.fn().mockResolvedValue({ success: true }),
  };

  if (shouldFailUpdate) {
    mockPreparedStatement.run.mockRejectedValue(
      new Error('Database update failed')
    );
  }

  return {
    prepare: vi.fn().mockReturnValue(mockPreparedStatement),
    _mockPreparedStatement: mockPreparedStatement,
  };
};

// Mock context
const createMockContext = (
  user: any = null,
  session: any = null,
  db: any = null
) => {
  const mockResponse = {
    status: 200,
    json: async () => ({}),
    headers: new Headers(),
  };

  return {
    get: vi.fn((key: string) => {
      if (key === 'auth') {
        return { user, session };
      }
      return null;
    }),
    set: vi.fn(),
    env: { DB: db },
    json: vi.fn().mockReturnValue(mockResponse),
  };
};

describe('auth/refresh endpoint handler', () => {
  let mockDB: any;

  beforeEach(() => {
    mockDB = createMockDB();
    vi.clearAllMocks();
  });

  it('should refresh session successfully', async () => {
    const mockUser = { id: 'u1', username: 'testuser', role: 'viewer' };
    const mockSession = { id: 'session123' };
    const mockContext = createMockContext(mockUser, mockSession, mockDB);

    const result = await refreshHandler(mockContext as any);

    expect(mockContext.json).toHaveBeenCalledWith({
      code: 0,
      message: '会话刷新成功',
      data: mockUser,
    });

    // Verify database update was called
    expect(mockDB.prepare).toHaveBeenCalledWith(
      'UPDATE auth_session SET expires_at = ? WHERE id = ?'
    );
    expect(mockDB._mockPreparedStatement.bind).toHaveBeenCalledWith(
      expect.any(String), // ISO date string
      'session123'
    );
  });

  it('should reject refresh when user is not authenticated', async () => {
    const mockContext = createMockContext(null, null, mockDB);

    const result = await refreshHandler(mockContext as any);

    // Should call jsonError which returns a response
    expect(result).toBeDefined();
    expect(mockDB.prepare).not.toHaveBeenCalled();
  });

  it('should reject refresh when session is missing', async () => {
    const mockUser = { id: 'u1', username: 'testuser', role: 'viewer' };
    const mockContext = createMockContext(mockUser, null, mockDB);

    const result = await refreshHandler(mockContext as any);

    // Should call jsonError which returns a response
    expect(result).toBeDefined();
    expect(mockDB.prepare).not.toHaveBeenCalled();
  });

  it('should handle database update failure gracefully', async () => {
    const mockUser = { id: 'u1', username: 'testuser', role: 'viewer' };
    const mockSession = { id: 'session123' };
    const mockDBWithFailure = createMockDB(true); // Will fail on update
    const mockContext = createMockContext(
      mockUser,
      mockSession,
      mockDBWithFailure
    );

    // Mock console.warn to avoid noise in test output
    const consoleSpy = vi.spyOn(console, 'warn').mockImplementation(() => {});

    const result = await refreshHandler(mockContext as any);

    // Should still return success even if session extension fails
    expect(mockContext.json).toHaveBeenCalledWith({
      code: 0,
      message: '会话刷新成功',
      data: mockUser,
    });

    expect(consoleSpy).toHaveBeenCalledWith(
      'Failed to extend session:',
      expect.any(Error)
    );

    consoleSpy.mockRestore();
  });

  it('should generate correct expiration date', async () => {
    const mockUser = { id: 'u1', username: 'testuser', role: 'viewer' };
    const mockSession = { id: 'session123' };
    const now = new Date('2024-01-01T00:00:00Z');

    // Mock Date.now to return a fixed timestamp
    const dateSpy = vi.spyOn(Date, 'now').mockReturnValue(now.getTime());

    const mockContext = createMockContext(mockUser, mockSession, mockDB);

    await refreshHandler(mockContext as any);

    // Verify the expiration date is 30 days from now
    const expectedExpiresAt = new Date(
      now.getTime() + 1000 * 60 * 60 * 24 * 30
    );
    expect(mockDB._mockPreparedStatement.bind).toHaveBeenCalledWith(
      expectedExpiresAt.toISOString(),
      'session123'
    );

    dateSpy.mockRestore();
  });

  it('should handle different user roles', async () => {
    const testCases = [
      { role: 'admin' },
      { role: 'editor' },
      { role: 'viewer' },
    ];

    for (const testCase of testCases) {
      const mockUser = { id: 'u1', username: 'testuser', role: testCase.role };
      const mockSession = { id: 'session123' };
      const mockContext = createMockContext(mockUser, mockSession, mockDB);

      await refreshHandler(mockContext as any);

      expect(mockContext.json).toHaveBeenCalledWith({
        code: 0,
        message: '会话刷新成功',
        data: mockUser,
      });

      vi.clearAllMocks();
    }
  });
});
