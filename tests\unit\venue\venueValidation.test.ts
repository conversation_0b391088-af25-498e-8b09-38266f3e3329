import { describe, it, expect } from 'vitest';
import { venueCreateRequest } from '../../../src/modules/venue/schema';

describe('Venue Validation', () => {
  it('should validate valid venue data', () => {
    const validData = {
      name_en: "Test Venue",
      name_ja: "テスト会場",
      name_zh: "测试场馆",
      lat: 35.6298,
      lng: 139.793,
      capacity: 1000,
      website_url: "https://example.com",
      phone: "+81-3-1234-5678",
      description_en: "A test venue",
      description_ja: "テスト会場です",
      description_zh: "这是一个测试场馆",
      facilities: JSON.stringify({
        wifi: true,
        parking: true,
        restaurant: false
      }),
      transportation: JSON.stringify({
        nearest_stations: [{
          name: "Test Station",
          line: "Test Line",
          walking_minutes: 5,
          distance_meters: 400
        }]
      }),
      parking_info: JSON.stringify({
        available: true,
        capacity: 100,
        hourly_rate: 500,
        daily_rate: 2000,
        free_hours: 1
      })
    };

    expect(() => venueCreateRequest.parse(validData)).not.toThrow();
  });

  it('should reject invalid JSON in facilities', () => {
    const invalidData = {
      name_en: "Test Venue",
      name_ja: "テスト会場",
      name_zh: "测试场馆",
      lat: 35.6298,
      lng: 139.793,
      facilities: "invalid json {",
    };

    expect(() => venueCreateRequest.parse(invalidData)).toThrow();
  });

  it('should accept empty string for JSON fields', () => {
    const dataWithEmptyJson = {
      name_en: "Test Venue",
      name_ja: "テスト会場",
      name_zh: "测试场馆",
      lat: 35.6298,
      lng: 139.793,
      facilities: "",
      transportation: "",
      parking_info: "",
    };

    expect(() => venueCreateRequest.parse(dataWithEmptyJson)).not.toThrow();
  });

  it('should accept null for JSON fields', () => {
    const dataWithNullJson = {
      name_en: "Test Venue",
      name_ja: "テスト会场",
      name_zh: "测试场馆",
      lat: 35.6298,
      lng: 139.793,
      facilities: null,
      transportation: null,
      parking_info: null,
    };

    expect(() => venueCreateRequest.parse(dataWithNullJson)).not.toThrow();
  });

  it('should require mandatory fields', () => {
    const incompleteData = {
      name_en: "Test Venue",
      // Missing name_ja, name_zh, lat, lng
    };

    expect(() => venueCreateRequest.parse(incompleteData)).toThrow();
  });
});
