---
id: README
title: API 代码生成指南
sidebar_label: API 代码生成
sidebar_position: 1
description: 基于 OpenAPI 的前端代码自动生成指南
keywords: [api, openapi, 代码生成, typescript]
---

# API 代码生成指南

本项目采用 **OpenAPI** 描述文件配合 *@openapi-codegen* 与 *openapi-typescript* 自动生成前端网络层代码。

## 生成产物位置

| 路径 | 描述 |
| ---- | ---- |
| `src/api/generated/*` | *@openapi-codegen* 输出的 **TypeScript Fetcher** + **React-Query Hooks** |
| `src/types/api-types.d.ts` | *openapi-typescript* 输出的全局接口类型声明 |

> ⚠️ **请勿手动修改以上自动生成文件**，任何本地改动都会在下一次生成时被覆盖。

## 本地生成命令

```bash
# 基于 openapi.json 生成所有产物
pnpm sync:api       # 等同于 pnpm gen:api && pnpm gen:rq
```

脚本说明：

| 命令 | 作用 |
| ----- | ---- |
| `pnpm gen:api` | 调用 *openapi-typescript* 生成接口类型文件 |
| `pnpm gen:rq`  | 调用 *@openapi-codegen/cli* 生成 Fetcher + Hooks |
| `pnpm sync:api` | 组合执行以上两步，并在 CI 中校验产物是否已提交 |

生成配置位于根目录 `openapi-codegen.config.ts`；如需调整输出目录、文件名前缀等，请修改该文件。

## Git 策略

- 当前仓库 **提交** 生成产物，方便 IDE 补全与轻量 CI。
- 若未来希望减小仓库体积，可在 `.gitignore` 排除 `src/api/generated/*` 并在 CI / 本地脚本中执行 `pnpm sync:api` 后再运行测试。

## 常见问题

| 问题 | 解决方案 |
| --- | --- |
| 生成失败或类型错误 | 检查 `openapi.json` 中是否存在重复或缺失的 `operationId` / Schema 引用 |
| CI 报告未同步产物 | 本地执行 `pnpm sync:api`，提交变更后重试 | 

## 统一错误处理机制

前端通过 `src/lib/http.ts` 和 `src/lib/show-error.ts` 实现「网络层 → 业务层 → UI」的完整错误链路：

| 层级 | 位置 | 作用 |
| ---- | ---- | ---- |
| 网络层 | `http.ts` / `openapi-fetcher.ts` | 1) 拼接 `API_BASE` 2) 解析 401 / 403 并自动跳转 3) 读取 `X-Success-Message` Header → `toast.success` 4) 统一抛出 `ApiError` 或 `{ code, message }` 业务错误对象 |
| 业务层 | `show-error.ts` | 接收任意错误对象，优先匹配 **业务错误码** → 中文提示，回退到 HTTP `statusText` 或默认文案；最终调用 `toast.error` |
| UI 层 | `ReactQueryProvider` | 在 `QueryCache.onError` 中集中调用 `showApiError`，覆盖所有 `useQuery` / `useMutation` 错误 |
| 页面级 | `components/ErrorFallback.tsx` | 作为 Next.js `error.tsx` 通用回退 UI，显示错误文案并提供「重试」按钮 |

### 新增业务错误码
1. 后端在 OpenAPI `ErrorCodes` 中添加枚举值、`x-enum-varnames` 与 `x-enum-descriptions`。
2. 运行 `pnpm sync:api` 重新生成 `ayafeedSchemas.ts`。
3. 更新 `src/constants/error-codes.ts`，保持码值 → 中文描述映射。

### 本地调试
- 通过 **MSW** 在测试中模拟 500 / 400 等状态，验证 Toast 与回退 UI 是否正确触发（示例见 `src/__test__/error-handling.integration.test.tsx`）。

--- 