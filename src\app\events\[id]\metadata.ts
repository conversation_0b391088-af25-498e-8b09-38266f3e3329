import { Metadata } from "next";
import { fetchGetEventsId } from "@/api/generated/ayafeedComponents";

const SITE_URL = process.env.NEXT_PUBLIC_SITE_URL ?? "";
const DEFAULT_IMAGE = "/next.svg";

export async function generateMetadata({ params }: { params: { id: string } }): Promise<Metadata> {
  try {
    const evt = await fetchGetEventsId({ pathParams: { id: params.id } });

    const title = `${evt.name_zh || evt.name_ja || evt.name_en} | Ayafeed`;
    const description = "同人活动情报平台 Ayafeed";
    const image = evt.image_url || DEFAULT_IMAGE;
    const url = `${SITE_URL}/events/${params.id}`;

    return {
      title,
      description,
      alternates: { canonical: url },
      openGraph: {
        title,
        description,
        url,
        type: "article",
        images: [{ url: image, alt: evt.name_zh || evt.name_ja || evt.name_en }],
      },
      twitter: {
        card: "summary_large_image",
        title,
        description,
        images: [image],
      },
    };
  } catch (_) {
    // fall back default
    return {
      title: "展会详情 | Ayafeed",
      description: "同人活动情报平台 Ayafeed",
    };
  }
} 