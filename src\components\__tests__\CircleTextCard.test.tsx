import userEvent from "@testing-library/user-event"
import { expect, test } from "vitest"

import CircleTextCard from "@/components/circle-text-card"
import { renderWithProviders, screen } from "@test/test-utils"

const sample = {
  id: "1",
  circle_name: "幻想郷文楽団",
  booth_id: "あ25a",
  artist_name: "<PERSON><PERSON>",
  circle_urls: JSON.stringify({ twitter: "https://x.com/xyz", logo_url: "/logo.png" }),
  category: "music",
  created_at: "2024-01-01T00:00:00Z",
  updated_at: "2024-01-02T00:00:00Z",
}

test("renders basic info and opens dialog on click", async () => {
  const user = userEvent.setup()
  renderWithProviders(<CircleTextCard data={sample as any} />)

  expect(screen.getByText("幻想郷文楽団")).toBeInTheDocument()
  expect(screen.getByText("あ25a")).toBeInTheDocument()
  expect(screen.getByText("ZUN")).toBeInTheDocument()
  expect(screen.getByText("音乐")).toBeInTheDocument()

  await user.click(screen.getByText("幻想郷文楽団"))

  expect(await screen.findByText("社团相关链接")).toBeInTheDocument()
  expect(screen.getByRole("link", { name: "twitter" })).toHaveAttribute(
    "href",
    "https://x.com/xyz"
  )

  // 查看详情按钮存在且 href 正确
  expect(screen.getByRole("link", { name: "查看详情" })).toHaveAttribute(
    "href",
    "/circles/1"
  )
}) 