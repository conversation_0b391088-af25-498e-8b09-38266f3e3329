# Contribution Guide

感谢你对 Ayafeed 的贡献！请在提交代码前阅读本指南，确保流程一致。

## 分支策略

- `master`：稳定分支，用于生产部署。
- `feat/*`：功能开发分支。
- `fix/*`：缺陷修复分支。
- `docs/*`：文档修改。

## Commit 规范

```
<type>(<scope>): <subject>
```

- type: feat | fix | docs | style | refactor | test | chore
- scope: 可选，模块或文件夹名称
- subject: 简洁描述（≤ 72 字符）

示例：
```
feat(events): 支持展会筛选标签
```

## PR 流程

1. 保证 `pnpm lint` 与 `pnpm test` 通过。
2. 更新对应文档与测试。
3. 描述变化动机与影响，关联 Issue。
4. 至少 1 名 Reviewer 通过后合并。

## 文档贡献

- 文档位于 `website/docs/`，请遵循目录规划。
- 更新文档后运行 `markdownlint`。
- 使用 `pnpm docs:dev` 预览文档效果。

## API 代码生成

项目使用 OpenAPI Codegen 自动生成前端请求层代码：

```bash
pnpm sync:api   # 等同于 pnpm gen:api && pnpm gen:rq
```

若 `src/api/generated/*` 与 `src/types/api-types.d.ts` 出现差异，**必须** 在提交前执行上述命令并一并提交。

## ADR 流程

对于影响架构的决策，请在 `docs/adr/` 目录新增条目并提交 PR。 