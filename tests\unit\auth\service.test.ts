import { describe, it, expect, vi, beforeEach } from 'vitest';
import type { D1Database } from '@cloudflare/workers-types';
import * as authService from '@/modules/auth/service';
import bcrypt from 'bcryptjs';

// Mock bcrypt
vi.mock('bcryptjs', () => ({
  default: {
    hash: vi.fn(),
    compare: vi.fn(),
  },
}));

// Mock crypto for Node.js environment
vi.mock('node:crypto', () => ({
  randomBytes: vi.fn(() => Buffer.from('mock-random-bytes')),
}));

// Mock the auth repository
const mockRepo = {
  createUser: vi.fn(),
  createSession: vi.fn(),
  validateSession: vi.fn(),
  findUserByUsername: vi.fn(),
  cleanupExpiredSessions: vi.fn(),
};

vi.mock('@/modules/auth/repository', () => ({
  createAuthRepository: vi.fn(() => mockRepo),
}));

describe('auth/service', () => {
  let mockDB: D1Database;

  beforeEach(() => {
    mockDB = {} as D1Database;
    vi.clearAllMocks();
    (bcrypt.hash as any).mockResolvedValue('hashed-password');
    (bcrypt.compare as any).mockResolvedValue(true);
  });

  describe('createUser', () => {
    it('should create user successfully', async () => {
      mockRepo.createUser.mockResolvedValue(undefined);

      const result = await authService.createUser(
        mockDB,
        'testuser',
        'password123'
      );

      expect(result).toEqual({
        id: expect.any(String),
        username: 'testuser',
        role: 'viewer',
      });
      expect(bcrypt.hash).toHaveBeenCalledWith('password123', 12);
      expect(mockRepo.createUser).toHaveBeenCalledWith(
        expect.any(String),
        'testuser',
        'viewer',
        'hashed-password'
      );
    });

    it('should throw error when username already exists (UNIQUE constraint)', async () => {
      const uniqueError = new Error('UNIQUE constraint failed: users.username');
      mockRepo.createUser.mockRejectedValue(uniqueError);

      await expect(
        authService.createUser(mockDB, 'existinguser', 'password123')
      ).rejects.toThrow('Username already taken.');
    });

    it('should re-throw non-UNIQUE database errors', async () => {
      const otherError = new Error('Database connection failed');
      mockRepo.createUser.mockRejectedValue(otherError);

      await expect(
        authService.createUser(mockDB, 'testuser', 'password123')
      ).rejects.toThrow('Database connection failed');
    });

    it('should handle non-Error exceptions', async () => {
      const stringError = 'String error';
      mockRepo.createUser.mockRejectedValue(stringError);

      await expect(
        authService.createUser(mockDB, 'testuser', 'password123')
      ).rejects.toBe('String error');
    });

    it('should handle Error without UNIQUE in message', async () => {
      const errorWithoutUnique = new Error('Some other constraint failed');
      mockRepo.createUser.mockRejectedValue(errorWithoutUnique);

      await expect(
        authService.createUser(mockDB, 'testuser', 'password123')
      ).rejects.toThrow('Some other constraint failed');
    });
  });

  describe('createSession', () => {
    it('should create session successfully', async () => {
      mockRepo.createSession.mockResolvedValue(undefined);

      const sessionId = await authService.createSession(mockDB, 'user-123');

      expect(typeof sessionId).toBe('string');
      expect(sessionId.length).toBeGreaterThan(0);
      expect(mockRepo.createSession).toHaveBeenCalledWith(
        expect.any(String),
        'user-123',
        expect.any(Date)
      );
    });

    it('should handle database insertion errors', async () => {
      const dbError = new Error('Database insertion failed');
      mockRepo.createSession.mockRejectedValue(dbError);

      await expect(
        authService.createSession(mockDB, 'user-123')
      ).rejects.toThrow('Database insertion failed');
    });
  });

  describe('validateSession', () => {
    it('should validate session successfully', async () => {
      const mockUser = {
        id: 'user-123',
        username: 'testuser',
        role: 'viewer',
      };

      mockRepo.validateSession.mockResolvedValue(mockUser);

      const result = await authService.validateSession(mockDB, 'session-123');

      expect(result).toEqual(mockUser);
      expect(mockRepo.validateSession).toHaveBeenCalledWith('session-123');
    });

    it('should return null for invalid session', async () => {
      mockRepo.validateSession.mockResolvedValue(null);

      const result = await authService.validateSession(
        mockDB,
        'invalid-session'
      );

      expect(result).toBeNull();
    });

    it('should handle repository errors', async () => {
      const repoError = new Error('Repository error');
      mockRepo.validateSession.mockRejectedValue(repoError);

      await expect(
        authService.validateSession(mockDB, 'session-123')
      ).rejects.toThrow('Repository error');
    });
  });

  describe('hashPassword', () => {
    it('should hash password successfully', async () => {
      (bcrypt.hash as any).mockResolvedValue('hashed-result');

      const result = await authService.hashPassword('password123');

      expect(result).toBe('hashed-result');
      expect(bcrypt.hash).toHaveBeenCalledWith('password123', 12);
    });

    it('should handle bcrypt hashing errors', async () => {
      const hashError = new Error('Hashing failed');
      (bcrypt.hash as any).mockRejectedValue(hashError);

      await expect(authService.hashPassword('password123')).rejects.toThrow(
        'Hashing failed'
      );
    });
  });

  describe('verifyPassword', () => {
    it('should verify password successfully', async () => {
      (bcrypt.compare as any).mockResolvedValue(true);

      const result = await authService.verifyPassword(
        'hashed-password',
        'password123'
      );

      expect(result).toBe(true);
      expect(bcrypt.compare).toHaveBeenCalledWith(
        'password123',
        'hashed-password'
      );
    });

    it('should return false for incorrect password', async () => {
      (bcrypt.compare as any).mockResolvedValue(false);

      const result = await authService.verifyPassword(
        'hashed-password',
        'wrongpassword'
      );

      expect(result).toBe(false);
    });

    it('should handle bcrypt comparison errors', async () => {
      const compareError = new Error('Comparison failed');
      (bcrypt.compare as any).mockRejectedValue(compareError);

      await expect(
        authService.verifyPassword('hashed-password', 'password123')
      ).rejects.toThrow('Comparison failed');
    });
  });
});
