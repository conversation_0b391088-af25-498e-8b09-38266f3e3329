import { renderHook, waitFor } from '@testing-library/react';
import { vi } from 'vitest';

import { useUpdateEvent } from '../useUpdateEvent';
import type { MultilingualEventInput } from '@/schemas/event';

// Mock the generated API hook
const mockMutate = vi.fn();

vi.mock('@/api/generated/ayafeedComponents', () => ({
  usePatchAdminEventsId: vi.fn(() => ({
    mutate: mockMutate,
    isPending: false,
    error: null,
  })),
}));

// Mock query client
const mockInvalidateQueries = vi.fn();
vi.mock('@tanstack/react-query', () => ({
  useQueryClient: () => ({
    invalidateQueries: mockInvalidateQueries,
  }),
}));

describe('useUpdateEvent', () => {
  const eventId = 'test-event-id';

  beforeEach(() => {
    vi.clearAllMocks();
  });

  test('should call usePatchAdminEventsId with correct configuration', () => {
    renderHook(() => useUpdateEvent(eventId));

    // 验证 hook 能正常创建
    expect(true).toBe(true);
  });

  test('should transform partial multilingual input to API format', () => {
    const { result } = renderHook(() => useUpdateEvent(eventId));

    const partialInput: Partial<MultilingualEventInput> = {
      name_zh: '更新的中文名称',
      venue_id: 'updated-venue',
      image_url: '/images/events/updated/thumb.jpg',
    };

    result.current.mutate(partialInput);

    expect(mockMutate).toHaveBeenCalledWith(
      {
        pathParams: { id: eventId },
        body: {
          name_zh: '更新的中文名称',
          venue_id: 'updated-venue',
          image_url: '/images/events/updated/thumb.jpg',
        },
      },
      expect.any(Object)
    );
  });

  test('should handle empty string values as null', () => {
    const { result } = renderHook(() => useUpdateEvent(eventId));

    const inputWithEmptyStrings: Partial<MultilingualEventInput> = {
      image_url: '',
      url: '',
      venue_id: '',
    };

    result.current.mutate(inputWithEmptyStrings);

    expect(mockMutate).toHaveBeenCalledWith(
      {
        pathParams: { id: eventId },
        body: {
          image_url: null,
          url: null,
          venue_id: '',
        },
      },
      expect.any(Object)
    );
  });

  test('should only include defined fields in update payload', () => {
    const { result } = renderHook(() => useUpdateEvent(eventId));

    const sparseInput: Partial<MultilingualEventInput> = {
      name_en: 'Updated Name',
      // Other fields are undefined and should not be included
    };

    result.current.mutate(sparseInput);

    expect(mockMutate).toHaveBeenCalledWith(
      {
        pathParams: { id: eventId },
        body: {
          name_en: 'Updated Name',
          // Should not include other fields
        },
      },
      expect.any(Object)
    );
  });

  test('should handle mutation call', () => {
    const { result } = renderHook(() => useUpdateEvent(eventId));

    // 验证 hook 返回了 mutate 函数
    expect(typeof result.current.mutate).toBe('function');

    // 调用 mutate 不应该抛出错误
    expect(() => {
      result.current.mutate({ name_en: 'Updated Name' });
    }).not.toThrow();
  });

  test('should handle all multilingual fields correctly', () => {
    const { result } = renderHook(() => useUpdateEvent(eventId));

    const fullInput: MultilingualEventInput = {
      id: eventId,
      name_en: 'Updated Event',
      name_ja: '更新されたイベント',
      name_zh: '更新的活动',
      date_en: 'September 1, 2025',
      date_ja: '2025年9月1日',
      date_zh: '2025年9月1日',
      date_sort: 20250901,
      venue_id: 'updated-venue',
      image_url: '/images/events/updated/thumb.jpg',
      url: 'https://example.com/updated',
    };

    result.current.mutate(fullInput);

    expect(mockMutate).toHaveBeenCalledWith(
      {
        pathParams: { id: eventId },
        body: {
          name_en: 'Updated Event',
          name_ja: '更新されたイベント',
          name_zh: '更新的活动',
          date_en: 'September 1, 2025',
          date_ja: '2025年9月1日',
          date_zh: '2025年9月1日',
          date_sort: 20250901,
          venue_id: 'updated-venue',
          image_url: '/images/events/updated/thumb.jpg',
          url: 'https://example.com/updated',
        },
      },
      expect.any(Object)
    );
  });
});
