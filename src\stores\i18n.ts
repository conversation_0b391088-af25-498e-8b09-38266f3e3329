/**
 * 多语言状态管理
 * 基于后端文档包的多语言集成规范
 */

import { create } from 'zustand';
import { i18nService, type Locale } from '@/services/i18n';

interface I18nState {
  locale: Locale;
  isLoading: boolean;
  
  // Actions
  setLocale: (locale: Locale) => void;
  getLocaleConfig: () => ReturnType<typeof i18nService.getLocaleConfig>;
}

export const useI18nStore = create<I18nState>((set, get) => ({
  locale: i18nService.getCurrentLocale(),
  isLoading: false,
  
  setLocale: (locale: Locale) => {
    set({ isLoading: true });
    
    try {
      i18nService.setLocale(locale);
      set({ locale, isLoading: false });
    } catch (error) {
      console.error('Failed to set locale:', error);
      set({ isLoading: false });
    }
  },
  
  getLocaleConfig: () => {
    return i18nService.getLocaleConfig(get().locale);
  },
}));
