/**
 * Token自动刷新工具
 * 基于后端文档包的认证集成规范
 */

import { useAuthStore } from '@/stores/auth';

let refreshPromise: Promise<boolean> | null = null;

export async function refreshTokenIfNeeded(): Promise<boolean> {
  // 避免并发刷新
  if (refreshPromise) {
    return refreshPromise;
  }

  const { refreshTokenIfNeeded } = useAuthStore.getState();
  
  refreshPromise = refreshTokenIfNeeded()
    .finally(() => {
      refreshPromise = null;
    });

  return refreshPromise;
}

// 设置定时刷新
export function setupTokenRefreshInterval() {
  // 每5分钟检查一次token是否需要刷新
  setInterval(() => {
    refreshTokenIfNeeded().catch(console.error);
  }, 5 * 60 * 1000);
}

// 在页面可见性变化时检查token
export function setupVisibilityChangeRefresh() {
  document.addEventListener('visibilitychange', () => {
    if (document.visibilityState === 'visible') {
      refreshTokenIfNeeded().catch(console.error);
    }
  });
}
