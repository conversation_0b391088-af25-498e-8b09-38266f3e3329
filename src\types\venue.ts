/**
 * 前端venue相关类型定义
 * 这些类型专门用于前端组件，与后端API返回的数据格式对应
 */

import type { LocalizedVenue, VenueFacilities, VenueTransportation, VenueParkingInfo, Venue } from '@/schemas/venue'

/**
 * 前端使用的Venue类型（已本地化）
 * 从API接收到的数据已经是根据当前locale处理过的
 */
export type FrontendVenue = LocalizedVenue

/**
 * 前端venue组件的Props接口
 */
export interface VenueDisplayProps {
  venue: FrontendVenue
  isLoading?: boolean
  error?: string | null
  className?: string
}

/**
 * 地图组件专用的venue数据
 */
export interface MapVenue {
  id: string
  name: string
  lat: number
  lng: number
  address?: string
}

/**
 * 从FrontendVenue转换为MapVenue的工具函数
 */
export const toMapVenue = (venue: FrontendVenue): MapVenue => ({
  id: venue.id,
  name: venue.name,
  lat: venue.lat,
  lng: venue.lng,
  address: venue.address,
})

/**
 * 解析venue扩展信息的前端工具函数
 */
export const parseVenueExtensions = (venue: FrontendVenue) => {
  const result: {
    facilities?: VenueFacilities
    transportation?: VenueTransportation
    parking_info?: VenueParkingInfo
  } = {}

  try {
    if (venue.facilities) {
      result.facilities = JSON.parse(venue.facilities)
    }
    if (venue.transportation) {
      result.transportation = JSON.parse(venue.transportation)
    }
    if (venue.parking_info) {
      result.parking_info = JSON.parse(venue.parking_info)
    }
  } catch (error) {
    console.warn('Failed to parse venue extensions:', error)
  }

  return result
}

/**
 * venue选择器组件的选项类型
 */
export interface VenueOption {
  value: string
  label: string
  address?: string
  coordinates?: {
    lat: number
    lng: number
  }
}

/**
 * 将FrontendVenue转换为选择器选项
 */
export const toVenueOption = (venue: FrontendVenue): VenueOption => ({
  value: venue.id,
  label: venue.name,
  address: venue.address,
  coordinates: {
    lat: venue.lat,
    lng: venue.lng,
  },
})

/**
 * venue搜索和筛选的参数类型
 */
export interface VenueSearchParams {
  keyword?: string
  city?: string
  capacity_min?: number
  capacity_max?: number
  has_parking?: boolean
  has_wifi?: boolean
}

/**
 * venue列表组件的Props
 */
export interface VenueListProps {
  venues: FrontendVenue[]
  searchParams?: VenueSearchParams
  onVenueSelect?: (venue: FrontendVenue) => void
  onSearchChange?: (params: VenueSearchParams) => void
  isLoading?: boolean
  error?: string | null
  className?: string
}

// ==================== Admin相关类型 ====================

/**
 * Admin专用的venue类型（包含完整多语言数据）
 * 用于后台管理系统，包含所有语言版本的字段
 */
export interface AdminVenue {
  id: string;
  name_en: string;
  name_ja: string;
  name_zh: string;
  address_en?: string;
  address_ja?: string;
  address_zh?: string;
  lat: number;
  lng: number;
  capacity?: number;
  website_url?: string;
  phone?: string;
  description_en?: string;
  description_ja?: string;
  description_zh?: string;
  facilities?: string; // JSON string
  transportation?: string; // JSON string
  parking_info?: string; // JSON string
  created_at?: string;
  updated_at?: string;
}

/**
 * Admin venues列表响应类型
 */
export interface AdminVenuesResponse {
  items: AdminVenue[];
  total: number;
  page: number;
  pageSize: number;
}

/**
 * 创建venue的输入类型
 */
export interface CreateVenueInput {
  name_en: string;
  name_ja: string;
  name_zh: string;
  address_en?: string;
  address_ja?: string;
  address_zh?: string;
  lat: number;
  lng: number;
  capacity?: number;
  website_url?: string;
  phone?: string;
  description_en?: string;
  description_ja?: string;
  description_zh?: string;
  facilities?: string;
  transportation?: string;
  parking_info?: string;
}

/**
 * 更新venue的输入类型（所有字段都是可选的）
 */
export type UpdateVenueInput = Partial<CreateVenueInput>;

/**
 * Admin venues列表查询参数
 */
export interface AdminVenuesParams {
  page?: string;
  pageSize?: string;
  keyword?: string;
  city?: string;
  capacity_min?: string;
  capacity_max?: string;
  has_parking?: string;
  has_wifi?: string;
}
