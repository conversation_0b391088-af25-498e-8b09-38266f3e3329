import { useQuery } from "@tanstack/react-query";

import { queryKeys } from "@/constants/queryKeys";
import { request } from "@/lib/http";

export interface AdminCirclesParams {
  page?: number;
  pageSize?: number;
  keyword?: string;
}

export interface Circle {
  id: string;
  name: string;
  author?: string;
}

export function useAdminCircles(params?: AdminCirclesParams) {
  return useQuery({
    queryKey: queryKeys.adminCircles(params),
    placeholderData: (prev) => prev, // v5 语义等价于 keepPreviousData
    queryFn: () => {
      const qs = new URLSearchParams();
      if (params?.page) qs.append("page", String(params.page));
      if (params?.keyword) qs.append("keyword", params.keyword);
      if (params?.pageSize) qs.append("pageSize", String(params.pageSize));
      const url = "/admin/circles" + (qs.size ? `?${qs.toString()}` : "");
      return request<Circle[]>(url);
    },
    staleTime: 1000 * 60 * 5, // 5 分钟
  });
} 