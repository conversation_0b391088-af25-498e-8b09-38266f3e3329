import {themes as prismThemes} from 'prism-react-renderer';
import type {Config} from '@docusaurus/types';
import type * as Preset from '@docusaurus/preset-classic';

const config: Config = {
  title: 'Ayafeed 文档',
  tagline: '同人展会信息聚合平台 - 开发者文档',
  favicon: 'img/favicon.ico',

  // 设置生产环境的URL
  url: 'https://your-docusaurus-site.example.com',
  // 设置服务的基础路径
  baseUrl: '/',

  // GitHub pages部署配置
  organizationName: 'your-org', // 通常是你的GitHub用户名
  projectName: 'ayafeed', // 通常是你的仓库名

  onBrokenLinks: 'warn', // 暂时设为警告，避免构建失败
  onBrokenMarkdownLinks: 'warn',

  // 国际化配置
  i18n: {
    defaultLocale: 'zh-Hans',
    locales: ['zh-Hans', 'en', 'ja'],
    localeConfigs: {
      'zh-<PERSON>': {
        label: '简体中文',
        direction: 'ltr',
      },
      en: {
        label: 'English',
        direction: 'ltr',
      },
      ja: {
        label: '日本語',
        direction: 'ltr',
      },
    },
  },

  presets: [
    [
      'classic',
      {
        docs: {
          sidebarPath: './sidebars.ts',
          // 编辑此页面的链接
          editUrl: 'https://github.com/your-org/ayafeed/tree/main/',
        },
        blog: {
          showReadingTime: true,
          feedOptions: {
            type: ['rss', 'atom'],
            xslt: true,
          },
          // 编辑此页面的链接
          editUrl: 'https://github.com/your-org/ayafeed/tree/main/',
          // 博客侧边栏配置
          blogSidebarCount: 'ALL',
          blogSidebarTitle: '所有博客文章',
        },
        theme: {
          customCss: './src/css/custom.css',
        },
      } satisfies Preset.Options,
    ],
  ],

  themeConfig: {
    // 替换为你的项目的社交卡片
    image: 'img/docusaurus-social-card.jpg',
    navbar: {
      title: 'Ayafeed',
      logo: {
        alt: 'Ayafeed Logo',
        src: 'img/logo.svg',
      },
      items: [
        {
          type: 'docSidebar',
          sidebarId: 'tutorialSidebar',
          position: 'left',
          label: '文档',
        },
        {to: '/blog', label: '博客', position: 'left'},

        {
          type: 'localeDropdown',
          position: 'right',
        },
        {
          href: 'https://github.com/your-org/ayafeed',
          label: 'GitHub',
          position: 'right',
        },
      ],
    },
    footer: {
      style: 'dark',
      links: [
        {
          title: '文档',
          items: [
            {
              label: '快速开始',
              to: '/docs/getting-started/installation',
            },
            {
              label: 'API 文档',
              to: '/docs/api',
            },
            {
              label: '架构设计',
              to: '/docs/architecture/system-design',
            },
          ],
        },
        {
          title: '社区',
          items: [
            {
              label: 'GitHub Issues',
              href: 'https://github.com/your-org/ayafeed/issues',
            },
            {
              label: 'GitHub Discussions',
              href: 'https://github.com/your-org/ayafeed/discussions',
            },
          ],
        },
        {
          title: '更多',
          items: [
            {
              label: '博客',
              to: '/blog',
            },
            {
              label: 'GitHub',
              href: 'https://github.com/your-org/ayafeed',
            },
          ],
        },
      ],
      copyright: `Copyright © ${new Date().getFullYear()} Ayafeed. Built with Docusaurus.`,
    },
    prism: {
      theme: prismThemes.github,
      darkTheme: prismThemes.dracula,
      additionalLanguages: ['bash', 'json', 'typescript', 'javascript'],
    },
    // 搜索配置
    algolia: {
      // 如果你使用Algolia DocSearch，在这里配置
      appId: 'YOUR_APP_ID',
      apiKey: 'YOUR_SEARCH_API_KEY',
      indexName: 'YOUR_INDEX_NAME',
      contextualSearch: true,
    },
  } satisfies Preset.ThemeConfig,

  // 插件配置
  plugins: [
    // 可以在这里添加额外的插件
  ],

  // Markdown配置
  markdown: {
    mermaid: true,
  },
  themes: ['@docusaurus/theme-mermaid'],
};

export default config;
