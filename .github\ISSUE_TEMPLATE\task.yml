name: Task Template
description: 标准任务模板
title: "[Epic] Task Description"
labels: []
body:
  - type: dropdown
    id: epic
    attributes:
      label: Epic
      description: 选择任务所属的 Epic
      options:
        - auth
        - feed
        - search
        - subscription
        - perf
        - i18n
        - tests
        - responsive
        - pwa
        - ops
    validations:
      required: true

  - type: dropdown
    id: type
    attributes:
      label: Type
      description: 选择任务类型
      options:
        - feature
        - bug
        - refactor
        - style
        - perf
        - docs
    validations:
      required: true

  - type: dropdown
    id: priority
    attributes:
      label: Priority
      description: 选择任务优先级
      options:
        - P0
        - P1
        - P2
        - P3
    validations:
      required: true

  - type: dropdown
    id: status
    attributes:
      label: Status
      description: 选择任务状态
      options:
        - Backlog
        - Todo
        - In Progress
        - In Review
        - Done
    validations:
      required: true

  - type: textarea
    id: description
    attributes:
      label: Description
      description: 详细描述任务内容、目标和验收标准
      placeholder: |
        ## 任务描述
        
        ## 目标
        
        ## 验收标准
        
        ## 注意事项
    validations:
      required: true

  - type: textarea
    id: dependencies
    attributes:
      label: Dependencies
      description: 列出依赖的其他任务（如果有）
      placeholder: |
        - Depends on #123
        - Blocks #456
    validations:
      required: false 