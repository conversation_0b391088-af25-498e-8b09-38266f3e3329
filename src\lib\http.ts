/**
 * 统一 fetch 包装：
 * - 自动拼接 API_BASE
 * - 处理错误码
 * - 输出 JSON 并抛出业务错误
 * - 集成统一错误处理机制
 */

// 默认使用本地开发 API 地址，支持通过 NEXT_PUBLIC_API_URL 覆盖

import { toast } from "sonner";
import { z } from "zod";
import { getCurrentLocale } from "./locale-utils";
import { ErrorHandlerService } from "@/services/errorHandler";
// ⚡️ 类型推断增强所需类型
// 若需要 OpenAPI 路径推断，可在后续迭代引入

// Base URL 可通过环境变量覆盖，默认同源
const API_BASE_ENV = process.env.NEXT_PUBLIC_API_URL;
if (!API_BASE_ENV) {
  // 若未设置环境变量，抛出明确错误，避免请求指向空地址
  throw new Error(
    "Environment variable NEXT_PUBLIC_API_URL is not defined. Please set it in your .env file."
  );
}

export const API_BASE = API_BASE_ENV;

// 若需要旧接口保留，可在此处加别名：const BASE_URL = API_BASE

export class ApiError extends Error {
  constructor(public code: number, message: string, public data?: any) {
    super(message)
  }
}

// ---------------------------------------------------------------------------
// http 函数重载：
// 1) 若传入 schema，则返回值 = zod 推断类型
// 2) 否则返回值 = 泛型 T（默认 any）
// ---------------------------------------------------------------------------

type Primitive = string | number | boolean | null | undefined;
type BaseOpts = RequestInit & {
  params?: Record<string, Primitive>;
};

// ① schema 推断优先
export function http<S extends z.ZodSchema>(
  url: string,
  options: { schema: S } & BaseOpts
): Promise<z.infer<S>>

// ③ 兜底：显式传入泛型或 any
export function http<T = unknown>(
  url: string,
  options?: BaseOpts & { schema?: z.ZodSchema<T> }
): Promise<T>



// 具体实现体 —— 维持原有逻辑
export async function http<TReturn = unknown>(
  url: string,
  options?: BaseOpts & { schema?: z.ZodSchema }
): Promise<TReturn> {
  const { params, ...rest } = options ?? {}

  const queryString =
    params && Object.keys(params).length
      ? "?" +
        new URLSearchParams(
          Object.entries(params).reduce<Record<string, string>>((acc, [k, v]) => {
            if (v !== undefined && v !== null) {
              acc[k] = String(v);
            }
            return acc;
          }, {})
        ).toString()
      : "";

  // 构建请求头，自动添加语言信息
  const headers: HeadersInit = {
    "Content-Type": "application/json",
    "Accept-Language": getCurrentLocale(),
    ...(rest.headers || {})
  }

  const res = await fetch(API_BASE + url + queryString, {
    headers,
    ...rest,
  })

  if (!res.ok) {
    let errorData;
    try {
      errorData = await res.json();
    } catch {
      errorData = { message: res.statusText };
    }
    throw new ApiError(res.status, res.statusText, errorData);
  }

  const data: unknown = await res.json()

  if (options?.schema) {
    return options.schema.parse(data)
  }

  return data as TReturn
}

/**
 * 轻量包装：直接传递 fetch 其他参数，自动 JSON 解析 / 204 处理
 * 等价于旧版 apiRequest，供业务层调用
 */
export async function request<T = unknown>(
  path: string,
  options: (RequestInit & { skipAuth?: boolean; silent?: boolean }) = {}
): Promise<T> {
  const { skipAuth, silent, ...rest } = options

  // 构建请求头，包含语言信息
  const defaultHeaders: Record<string, string> = {
    "Content-Type": "application/json",
    "Accept-Language": getCurrentLocale(),
  }

  // 合并用户提供的头部
  const mergedHeaders = {
    ...defaultHeaders,
    ...(typeof rest.headers === "object" && !Array.isArray(rest.headers) ? rest.headers : {}),
  }

  const res = await fetch(API_BASE + path, {
    credentials: "include", // 携带 Cookie
    headers: mergedHeaders,
    ...rest,
  })

  // 处理 204 No Content
  if (res.status === 204) {
    return undefined as T
  }

  if (!res.ok) {
    let errorData;
    try {
      const errorText = await res.text();
      errorData = errorText ? JSON.parse(errorText) : { message: res.statusText };
    } catch {
      errorData = { message: res.statusText };
    }

    // 处理认证失败
    if (res.status === 401 && !skipAuth) {
      // 清除本地用户信息
      if (typeof window !== 'undefined') {
        localStorage.removeItem('auth_user');

        // 只在管理页面才自动重定向到登录页
        const currentPath = window.location.pathname;
        if (currentPath.startsWith('/admin')) {
          const loginUrl = `/login?redirect=${encodeURIComponent(currentPath)}`;
          window.location.href = loginUrl;
        }
        // 公共页面不重定向，让组件自己处理
      }
    }

    // 在静默模式下，不在控制台显示401错误（这是预期的）
    if (silent && res.status === 401) {
      throw new ApiError(res.status, errorData.message || res.statusText, errorData);
    }

    throw new ApiError(res.status, errorData.message || res.statusText, errorData);
  }

  return await res.json()
}
