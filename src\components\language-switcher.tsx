'use client'

import * as DropdownMenu from "@radix-ui/react-dropdown-menu";
import { Globe } from 'lucide-react';
import { cn } from "@/lib/utils";
import { useLocale } from 'next-intl';
import { useRouter } from 'next/navigation';
import { useQueryClient } from '@tanstack/react-query';
import { SUPPORTED_LOCALES, type Locale, setLocaleToCookie } from '@/lib/locale-utils';
import { createLocaleCacheManager } from '@/lib/locale-cache-utils';
import { RadixButton } from "@/components/ui/radix-components";

// 语言显示名称映射
const LANGUAGE_NAMES: Record<Locale, string> = {
  ja: '日本語',
  en: 'English',
  zh: '中文'
};

// 语言选项组件
interface LanguageOptionProps {
  locale: Locale;
  currentLocale: Locale;
  onSelect: (locale: Locale) => void;
}

function LanguageOption({ locale, currentLocale, onSelect }: LanguageOptionProps) {
  const isActive = locale === currentLocale;

  return (
    <DropdownMenu.Item
      disabled={isActive}
      onSelect={() => onSelect(locale)}
      className={cn(
        "flex items-center justify-between px-3 py-2 text-sm rounded-sm outline-none cursor-pointer",
        isActive
          ? "bg-accent/50 text-accent-foreground cursor-default"
          : "hover:bg-accent hover:text-accent-foreground focus:bg-accent focus:text-accent-foreground"
      )}
    >
      <span>{LANGUAGE_NAMES[locale]}</span>
      {isActive && (
        <Globe className="h-4 w-4 text-muted-foreground" />
      )}
    </DropdownMenu.Item>
  );
}

// 语言切换器触发按钮组件
function LanguageTrigger() {
  return (
    <DropdownMenu.Trigger asChild>
      <RadixButton
        variant="ghost"
        size="sm"
        className="w-10 h-10 px-0 transition-all text-white/80 hover:text-white hover:bg-white/10 rounded-lg"
        aria-label="切换语言"
      >
        <Globe className="h-5 w-5 transition-transform hover:scale-110" />
      </RadixButton>
    </DropdownMenu.Trigger>
  );
}

export default function LanguageSwitcher() {
  const locale = useLocale() as Locale;
  const router = useRouter();
  const queryClient = useQueryClient();

  const handleLocaleChange = (nextLocale: Locale) => {
    if (nextLocale === locale) return;

    console.log(`🌐 语言切换开始: ${locale} → ${nextLocale}`);

    // 设置 Cookie
    setLocaleToCookie(nextLocale);

    // 使用智能缓存管理器
    const cacheManager = createLocaleCacheManager(queryClient);
    cacheManager.handleLocaleSwitch(locale, nextLocale);

    // 在开发环境下显示缓存调试信息
    if (process.env.NODE_ENV === 'development') {
      const stats = cacheManager.getCacheStats();
      console.log('📊 缓存统计:', stats);
    }

    // 刷新页面让 next-intl 重新加载
    router.refresh();
  };

  return (
    <DropdownMenu.Root>
      <LanguageTrigger />
      <DropdownMenu.Portal>
        <DropdownMenu.Content
          align="end"
          className="min-w-[144px] bg-background border border-border rounded-md p-1 shadow-lg z-50"
          sideOffset={5}
        >
          {SUPPORTED_LOCALES.map((supportedLocale) => (
            <LanguageOption
              key={supportedLocale}
              locale={supportedLocale}
              currentLocale={locale}
              onSelect={handleLocaleChange}
            />
          ))}
        </DropdownMenu.Content>
      </DropdownMenu.Portal>
    </DropdownMenu.Root>
  );
}
