<svg width="200" height="200" viewBox="0 0 200 200" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <linearGradient id="mountain-grad" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#2e8555;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#25c2a0;stop-opacity:1" />
    </linearGradient>
  </defs>
  
  <!-- 山峰 -->
  <polygon points="50,150 100,50 150,150" fill="url(#mountain-grad)" />
  <polygon points="120,150 160,80 200,150" fill="url(#mountain-grad)" opacity="0.8" />
  <polygon points="0,150 40,100 80,150" fill="url(#mountain-grad)" opacity="0.6" />
  
  <!-- 地面 -->
  <rect x="0" y="150" width="200" height="50" fill="#f0f0f0" />
  
  <!-- 装饰元素 -->
  <circle cx="170" cy="30" r="15" fill="#ffd700" opacity="0.8" />
  <polygon points="30,140 35,130 40,140" fill="#228B22" />
  <polygon points="160,140 165,130 170,140" fill="#228B22" />
</svg>
