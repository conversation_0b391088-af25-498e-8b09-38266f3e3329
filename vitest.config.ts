import react from "@vitejs/plugin-react"
import tsconfigPaths from "vite-tsconfig-paths"
import { defineConfig } from "vitest/config"

export default defineConfig({
  plugins: [tsconfigPaths(), react()],
  test: {
    globals: true,
    environment: "jsdom",
    setupFiles: ["src/__test__/setupTests.ts"],
    // 覆盖率阈值配置
    coverage: {
      provider: "v8",
      reporter: ["text", "lcov"],
      exclude: [
        "**/.next/**",
        "**/node_modules/**",
        "scripts/**",
        "src/app/**",
        "src/components/ui/**",
        "src/types/**",
        "middleware.ts",
        "next.config.ts",
        "postcss.config.mjs",
        "eslint.config.mjs",
        "next-env.d.ts",
        "openapi-codegen.config.ts",
        "src/api/generated/**",
        "vitest.config.ts",
      ],
      thresholds: {
        lines: 60,
        functions: 60,
        branches: 60,
        statements: 60,
      },
    },
  },
})
