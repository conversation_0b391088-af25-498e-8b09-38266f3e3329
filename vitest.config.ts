// eslint-disable-next-line eslint-comments/disable-enable-pair
/* eslint-disable */
import { defineConfig } from 'vitest/config';
import path from 'path';

export default defineConfig({
  test: {
    // 仅匹配项目源码与顶级 tests 目录下的测试文件
    include: [
      'src/**/*.test.{ts,tsx}',
      'src/**/*.integration.test.{ts,tsx}',
      'tests/**/*.test.ts',
    ],
    // 排除构建产物及无关目录，避免扫描大量无用文件
    exclude: [
      'node_modules',
      'dist',
      'coverage',
      '.wrangler',
      '.docusaurus',
      'docs-site/build',
      'docs-site/.docusaurus',
      'docs-site/node_modules',
      'tests/index.ts',
    ],
    environment: 'node',
    // 覆盖率配置，后续可按需调整阈值
    coverage: {
      provider: 'v8',
      reporter: ['text', 'html', 'lcov'],
      all: true,
      thresholds: {
        lines: 95,
        functions: 80,
        statements: 95,
        branches: 70,
      },
      // 从覆盖率中排除测试代码与类型声明
      exclude: [
        'tests/**',
        'src/**/*.test.{ts,tsx}',
        'src/**/*.integration.test.{ts,tsx}',
        '**/*.d.ts',
        'docs-site/**',
        'scripts/**',
        // 顶层配置文件 & 工具脚本
        '*.config.{js,cjs,mjs,ts}',
        '.dependency-cruiser.js',
        '**/node_modules/**', // 一刀切排除所有依赖
        '.wrangler/**', // Worker 打包输出
      ],
    },
    alias: {
      '@': path.resolve(__dirname, 'src'),
    },
  },
});
