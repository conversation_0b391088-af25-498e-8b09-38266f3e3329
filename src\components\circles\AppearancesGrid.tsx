import { VirtuosoGrid } from "react-virtuoso"
import { Card, CardContent, CardHeader } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import Link from "next/link"

interface Appearance {
  id: string
  name?: string
  date?: string
  venue_name?: string | null
  booth_id: string
  event_id: string
}

interface AppearancesGridProps {
  data: (Appearance | null)[]
}

export default function AppearancesGrid({ data }: AppearancesGridProps) {
  if (!data.length) {
    return (
      <div className="text-center text-muted-foreground py-8">
        暂无参展记录
      </div>
    );
  }

  return (
    <VirtuosoGrid
      data={data}
      style={{ height: "50vh" }}
      listClassName="w-full grid grid-cols-[repeat(auto-fill,minmax(280px,1fr))] gap-4"
      itemClassName="flex"
      overscan={400}
      itemContent={(index, appearance) => {
        if (!appearance) return null;
        return (
          <Link key={`${appearance.event_id}-${appearance.booth_id}`} href={`/events/${appearance.event_id}`}>
            <Card className="w-full cursor-pointer transition-shadow hover:shadow-md">
              <CardHeader className="pb-2">
                <div className="flex justify-between items-start">
                  <h3 className="font-semibold truncate">
                    {appearance.name || "展会"}
                  </h3>
                  <Badge variant="outline">{appearance.booth_id}</Badge>
                </div>
              </CardHeader>
              <CardContent className="pt-0">
                <p className="text-sm text-muted-foreground">
                  {appearance.date}
                  {appearance.venue_name ? ` ・ ${appearance.venue_name}` : ""}
                </p>
              </CardContent>
            </Card>
          </Link>
        );
      }}
    />
  )
} 