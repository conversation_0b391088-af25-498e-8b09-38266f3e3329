import { describe, it, expect, vi, beforeEach } from 'vitest';
import type { Context } from 'hono';
import {
  getAppearance,
  createAppearance,
  deleteAppearance,
} from '@/modules/appearance/controller';
import { getDB } from '@/infrastructure';
import { jsonError } from '@/utils/errorResponse';

// Mock dependencies
vi.mock('@/infrastructure');
vi.mock('@/utils/errorResponse');

// Helper function to create mock context
const createMockContext = (overrides: any = {}) => {
  const mockDB = {
    prepare: vi.fn().mockReturnValue({
      bind: vi.fn().mockReturnThis(),
      first: vi.fn(),
      run: vi.fn(),
    }),
  };

  return {
    req: {
      param: vi.fn(),
      json: vi.fn(),
      ...overrides.req,
    },
    json: vi.fn((data) => ({
      status: 200,
      json: async () => data,
    })),
    env: { DB: mockDB },
    _mockDB: mockDB,
    ...overrides,
  };
};

describe('appearance/controller', () => {
  beforeEach(() => {
    vi.clearAllMocks();
    (getDB as any).mockImplementation((c) => c.env.DB);
    (jsonError as any).mockReturnValue({
      status: 404,
      json: async () => ({ code: 50001, message: 'Not Found' }),
    });
  });

  describe('getAppearance', () => {
    it('should return appearance when found', async () => {
      const mockAppearance = {
        id: '1',
        circle_id: 'circle-1',
        event_id: 'event-1',
        artist_id: 'artist-1',
        booth_id: 'A01a',
        path: '/path/to/image.jpg',
        created_at: '2024-01-01T00:00:00Z',
        updated_at: '2024-01-01T00:00:00Z',
      };

      const mockContext = createMockContext({
        req: { param: vi.fn(() => '1') },
      });

      mockContext._mockDB.prepare().first.mockResolvedValue(mockAppearance);

      await getAppearance(mockContext as any);

      expect(mockContext._mockDB.prepare).toHaveBeenCalledWith(
        'SELECT * FROM appearances WHERE id = ?'
      );
      expect(mockContext._mockDB.prepare().bind).toHaveBeenCalledWith('1');
      expect(mockContext.json).toHaveBeenCalledWith(mockAppearance);
    });

    it('should return 404 when appearance not found', async () => {
      const mockContext = createMockContext({
        req: { param: vi.fn(() => 'nonexistent') },
      });

      mockContext._mockDB.prepare().first.mockResolvedValue(null);

      const result = await getAppearance(mockContext as any);

      expect(result.status).toBe(404);
      expect(jsonError).toHaveBeenCalledWith(
        mockContext,
        50001,
        'Not Found',
        404
      );
    });

    it('should handle different appearance IDs', async () => {
      const mockAppearance = {
        id: 'special-id-123',
        circle_id: 'circle-2',
        event_id: 'event-2',
        booth_id: 'B02b',
      };

      const mockContext = createMockContext({
        req: { param: vi.fn(() => 'special-id-123') },
      });

      mockContext._mockDB.prepare().first.mockResolvedValue(mockAppearance);

      await getAppearance(mockContext as any);

      expect(mockContext._mockDB.prepare().bind).toHaveBeenCalledWith(
        'special-id-123'
      );
      expect(mockContext.json).toHaveBeenCalledWith(mockAppearance);
    });

    it('should handle database errors gracefully', async () => {
      const mockContext = createMockContext({
        req: { param: vi.fn(() => '1') },
      });

      const dbError = new Error('Database connection failed');
      mockContext._mockDB.prepare().first.mockRejectedValue(dbError);

      await expect(getAppearance(mockContext as any)).rejects.toThrow(
        'Database connection failed'
      );
    });
  });

  describe('createAppearance', () => {
    it('should create appearance successfully with all fields', async () => {
      const mockBody = {
        id: 'new-appearance-1',
        circle_id: 'circle-1',
        event_id: 'event-1',
        artist_id: 'artist-1',
        booth_id: 'A01a',
        path: '/path/to/image.jpg',
      };

      const mockContext = createMockContext({
        req: { json: vi.fn().mockResolvedValue(mockBody) },
      });

      mockContext._mockDB.prepare().run.mockResolvedValue({ success: true });

      await createAppearance(mockContext as any);

      expect(mockContext._mockDB.prepare).toHaveBeenCalledWith(
        'INSERT INTO appearances (id, circle_id, event_id, artist_id, booth_id, path) VALUES (?, ?, ?, ?, ?, ?)'
      );
      expect(mockContext._mockDB.prepare().bind).toHaveBeenCalledWith(
        'new-appearance-1',
        'circle-1',
        'event-1',
        'artist-1',
        'A01a',
        '/path/to/image.jpg'
      );
      expect(mockContext.json).toHaveBeenCalledWith({ success: true });
    });

    it('should create appearance with minimal required fields', async () => {
      const mockBody = {
        id: 'minimal-appearance',
        booth_id: 'B01b',
      };

      const mockContext = createMockContext({
        req: { json: vi.fn().mockResolvedValue(mockBody) },
      });

      mockContext._mockDB.prepare().run.mockResolvedValue({ success: true });

      await createAppearance(mockContext as any);

      expect(mockContext._mockDB.prepare().bind).toHaveBeenCalledWith(
        'minimal-appearance',
        undefined, // circle_id
        undefined, // event_id
        undefined, // artist_id
        'B01b',
        undefined // path
      );
      expect(mockContext.json).toHaveBeenCalledWith({ success: true });
    });

    it('should return 400 when booth_id is missing', async () => {
      const mockBody = {
        id: 'invalid-appearance',
        circle_id: 'circle-1',
        event_id: 'event-1',
        // booth_id is missing
      };

      const mockContext = createMockContext({
        req: { json: vi.fn().mockResolvedValue(mockBody) },
      });

      (jsonError as any).mockReturnValue({
        status: 400,
        json: async () => ({ code: 50002, message: 'booth_id is required' }),
      });

      const result = await createAppearance(mockContext as any);

      expect(result.status).toBe(400);
      expect(jsonError).toHaveBeenCalledWith(
        mockContext,
        50002,
        'booth_id is required',
        400
      );
      expect(mockContext._mockDB.prepare().run).not.toHaveBeenCalled();
    });

    it('should return 400 when booth_id is empty string', async () => {
      const mockBody = {
        id: 'invalid-appearance',
        booth_id: '', // empty string
      };

      const mockContext = createMockContext({
        req: { json: vi.fn().mockResolvedValue(mockBody) },
      });

      (jsonError as any).mockReturnValue({
        status: 400,
        json: async () => ({ code: 50002, message: 'booth_id is required' }),
      });

      const result = await createAppearance(mockContext as any);

      expect(result.status).toBe(400);
      expect(jsonError).toHaveBeenCalledWith(
        mockContext,
        50002,
        'booth_id is required',
        400
      );
    });

    it('should return 400 when booth_id is null', async () => {
      const mockBody = {
        id: 'invalid-appearance',
        booth_id: null,
      };

      const mockContext = createMockContext({
        req: { json: vi.fn().mockResolvedValue(mockBody) },
      });

      (jsonError as any).mockReturnValue({
        status: 400,
        json: async () => ({ code: 50002, message: 'booth_id is required' }),
      });

      const result = await createAppearance(mockContext as any);

      expect(result.status).toBe(400);
      expect(jsonError).toHaveBeenCalledWith(
        mockContext,
        50002,
        'booth_id is required',
        400
      );
    });

    it('should handle database insertion errors', async () => {
      const mockBody = {
        id: 'error-appearance',
        booth_id: 'A01a',
      };

      const mockContext = createMockContext({
        req: { json: vi.fn().mockResolvedValue(mockBody) },
      });

      const dbError = new Error('Database insertion failed');
      mockContext._mockDB.prepare().run.mockRejectedValue(dbError);

      await expect(createAppearance(mockContext as any)).rejects.toThrow(
        'Database insertion failed'
      );
    });

    it('should handle JSON parsing errors', async () => {
      const mockContext = createMockContext({
        req: { json: vi.fn().mockRejectedValue(new Error('Invalid JSON')) },
      });

      await expect(createAppearance(mockContext as any)).rejects.toThrow(
        'Invalid JSON'
      );
    });
  });

  describe('deleteAppearance', () => {
    it('should delete appearance successfully', async () => {
      const mockContext = createMockContext({
        req: { param: vi.fn(() => '1') },
      });

      mockContext._mockDB.prepare().run.mockResolvedValue({ success: true });

      await deleteAppearance(mockContext as any);

      expect(mockContext._mockDB.prepare).toHaveBeenCalledWith(
        'DELETE FROM appearances WHERE id = ?'
      );
      expect(mockContext._mockDB.prepare().bind).toHaveBeenCalledWith('1');
      expect(mockContext.json).toHaveBeenCalledWith({ success: true });
    });

    it('should handle deletion of non-existent appearance', async () => {
      const mockContext = createMockContext({
        req: { param: vi.fn(() => 'nonexistent') },
      });

      mockContext._mockDB.prepare().run.mockResolvedValue({ success: true });

      await deleteAppearance(mockContext as any);

      // Should still return success even if record doesn't exist
      expect(mockContext.json).toHaveBeenCalledWith({ success: true });
      expect(mockContext._mockDB.prepare().bind).toHaveBeenCalledWith(
        'nonexistent'
      );
    });

    it('should handle different appearance IDs', async () => {
      const mockContext = createMockContext({
        req: { param: vi.fn(() => 'special-delete-id') },
      });

      mockContext._mockDB.prepare().run.mockResolvedValue({ success: true });

      await deleteAppearance(mockContext as any);

      expect(mockContext._mockDB.prepare().bind).toHaveBeenCalledWith(
        'special-delete-id'
      );
      expect(mockContext.json).toHaveBeenCalledWith({ success: true });
    });

    it('should handle database deletion errors', async () => {
      const mockContext = createMockContext({
        req: { param: vi.fn(() => '1') },
      });

      const dbError = new Error('Database deletion failed');
      mockContext._mockDB.prepare().run.mockRejectedValue(dbError);

      await expect(deleteAppearance(mockContext as any)).rejects.toThrow(
        'Database deletion failed'
      );
    });
  });
});
