# Event 图片管理前端实施方案

## 📋 概述

本文档详细说明在 admin/events/[id]/edit 页面添加图片上传功能，以及在 events/[id] 和 events/ 页面显示图片的前端实施方案。

## 🎯 技术选择

### 图片处理
- **处理库**: Canvas API
- **变体规格**:
  - Original: 保持原图尺寸
  - Large: 1200x900px, 质量85% (详情页使用)
  - Medium: 400x300px, 质量80% (列表页使用)
- **裁剪策略**: 居中裁剪，保持4:3宽高比

### 性能优化
- **查询方式**: 通过图片API单独查询
- **缓存策略**: React Query缓存30分钟
- **懒加载**: 使用 Intersection Observer API
- **批量查询**: 列表页批量获取图片

## 🔧 核心组件设计

### 1. EventImageUpload 组件

**功能职责**:
- 图片文件选择和验证
- Canvas API 图片处理
- 三个变体的生成和预览
- 上传进度显示
- 错误处理和重试

**组件结构**:
```
EventImageUpload
├── FileDropZone (文件拖拽区域)
├── ImageProcessor (图片处理逻辑)
├── VariantPreview (三变体预览)
├── UploadProgress (上传进度)
└── ErrorDisplay (错误显示)
```

**状态管理**:
- `selectedFile`: 用户选择的原始文件
- `processedImages`: 处理后的三个变体
- `uploadProgress`: 上传进度状态
- `processingProgress`: 图片处理进度
- `error`: 错误信息

### 2. EventImageDisplay 组件

**功能职责**:
- 根据变体类型显示图片
- 加载状态处理
- 错误回退机制
- 懒加载支持

**Props 接口**:
```typescript
interface EventImageDisplayProps {
  eventId: string;
  variant: 'large' | 'medium';
  className?: string;
  lazy?: boolean;
  fallbackSrc?: string;
}
```

### 3. LazyImage 组件

**功能职责**:
- Intersection Observer 懒加载
- 骨架屏显示
- 渐进式加载效果

## 🎣 Hook 设计

### 1. useEventImage

**用途**: 获取单个事件的图片
```typescript
function useEventImage(eventId: string, variant?: string) {
  // 返回: { imageUrl, isLoading, error, refetch }
}
```

### 2. useEventImagesBatch

**用途**: 批量获取多个事件的图片
```typescript
function useEventImagesBatch(eventIds: string[], variant: string) {
  // 返回: { imagesMap, isLoading, error }
}
```

### 3. useEventImageUpload

**用途**: 处理图片上传流程
```typescript
function useEventImageUpload(eventId: string) {
  // 返回: { uploadImages, isUploading, progress, error, reset }
}
```

### 4. useImageProcessor

**用途**: Canvas API 图片处理
```typescript
function useImageProcessor() {
  // 返回: { processImage, isProcessing, progress }
}
```

## 🔄 实施步骤

### 阶段1: 基础上传功能 (Week 1-2)

#### 1.1 图片处理工具开发
- [ ] 创建 `lib/image-processor.ts`
- [ ] 实现 Canvas API 图片缩放函数
- [ ] 实现质量压缩功能
- [ ] 添加进度回调支持

#### 1.2 上传组件开发
- [ ] 创建 `EventImageUpload` 组件
- [ ] 实现文件选择和验证
- [ ] 集成图片处理逻辑
- [ ] 添加三变体预览
- [ ] 实现上传进度显示

#### 1.3 编辑页面集成
- [ ] 修改 `admin/events/[id]/edit/page.tsx`
- [ ] 移除现有 `image_url` 文本输入
- [ ] 集成 `EventImageUpload` 组件
- [ ] 处理表单提交逻辑

### 阶段2: 显示功能 (Week 3)

#### 2.1 图片显示组件
- [ ] 创建 `EventImageDisplay` 组件
- [ ] 实现加载状态处理
- [ ] 添加错误回退机制
- [ ] 集成懒加载功能

#### 2.2 详情页修改
- [ ] 修改 `EventPoster` 组件
- [ ] 使用 `useEventImage` Hook
- [ ] 显示 Large 变体图片
- [ ] 添加加载和错误状态

#### 2.3 列表页修改
- [ ] 修改 `EventCard` 组件
- [ ] 使用 `useEventImagesBatch` Hook
- [ ] 显示 Medium 变体图片
- [ ] 实现懒加载

### 阶段3: 性能优化 (Week 4)

#### 3.1 批量查询优化
- [ ] 实现批量图片查询逻辑
- [ ] 优化 React Query 缓存策略
- [ ] 添加预加载机制

#### 3.2 懒加载优化
- [ ] 创建 `LazyImage` 组件
- [ ] 实现 Intersection Observer
- [ ] 添加骨架屏效果

#### 3.3 缓存和性能
- [ ] 配置 React Query 缓存时间
- [ ] 实现图片预加载策略
- [ ] 添加性能监控点

## 📁 文件结构

```
src/
├── components/
│   ├── events/
│   │   ├── EventImageUpload.tsx
│   │   ├── EventImageDisplay.tsx
│   │   └── LazyImage.tsx
│   └── ui/
│       └── ImageSkeleton.tsx
├── hooks/
│   ├── useEventImage.ts
│   ├── useEventImagesBatch.ts
│   ├── useEventImageUpload.ts
│   └── useImageProcessor.ts
├── lib/
│   ├── image-processor.ts
│   └── image-utils.ts
└── types/
    └── event-image.ts
```

## 🔧 技术实现细节

### Canvas API 图片处理

**核心函数**:
```typescript
async function resizeImage(
  file: File, 
  width: number, 
  height: number, 
  quality: number
): Promise<Blob>
```

**处理流程**:
1. 创建 Image 对象加载原图
2. 创建 Canvas 元素设置目标尺寸
3. 使用 `drawImage` 进行缩放
4. 使用 `toBlob` 输出压缩后的图片

### 批量查询策略

**API 设计**:
```
GET /images/batch?events=event1,event2&variant=medium
```

**前端实现**:
- 收集页面中所有需要图片的 eventId
- 批量请求减少网络开销
- 结果缓存到 React Query

### 懒加载实现

**Intersection Observer 配置**:
```typescript
const options = {
  root: null,
  rootMargin: '50px',
  threshold: 0.1
};
```

## 🎨 用户体验设计

### 上传流程 UX
1. **文件选择**: 拖拽或点击选择
2. **处理进度**: 显示 "处理中..." 和进度条
3. **变体预览**: 并排显示三个变体
4. **上传进度**: 显示每个变体的上传状态
5. **完成确认**: 显示成功消息

### 显示流程 UX
1. **骨架屏**: 图片加载时显示占位符
2. **渐进加载**: 先显示模糊版本
3. **错误回退**: 显示默认占位图片
4. **懒加载**: 进入视口时才加载

## 🔍 测试策略

### 单元测试
- [ ] 图片处理函数测试
- [ ] Hook 逻辑测试
- [ ] 组件渲染测试

### 集成测试
- [ ] 上传流程端到端测试
- [ ] 图片显示功能测试
- [ ] 错误处理测试

### 性能测试
- [ ] 图片处理性能测试
- [ ] 懒加载效果测试
- [ ] 缓存策略验证

## 🚀 部署注意事项

### 环境变量
- `NEXT_PUBLIC_API_URL`: API 基础地址
- `NEXT_PUBLIC_CDN_URL`: CDN 地址（可选）

### 构建优化
- 确保 Canvas API polyfill（如需要）
- 图片组件代码分割
- 懒加载组件预加载

## 🔧 安全考虑

### 文件验证
- **文件类型**: 仅允许 JPEG、PNG、WebP
- **文件大小**: 原图最大 10MB
- **图片尺寸**: 最大 4000x4000 像素

### 上传限制
- **频率限制**: 每分钟最多 10 次上传
- **并发限制**: 同时最多 3 个上传任务
- **内容检查**: 基础的图片内容验证

## 📚 相关文档

- [后端实施方案](./backend-event-image-implementation.md)
- [图片管理系统文档](./image-management-system.md)
- [性能优化指南](./performance-optimization.md)
