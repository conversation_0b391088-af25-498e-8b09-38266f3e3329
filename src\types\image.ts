/**
 * 图片模块类型定义
 * 基于后端API文档的图片接口规范
 */

// 图片分类枚举
export type ImageCategory = 'event' | 'circle' | 'venue';

// 图片类型枚举
export type ImageType = 'poster' | 'logo' | 'banner' | 'gallery';

// 图片变体枚举
export type ImageVariant = 'original' | 'large' | 'medium' | 'thumb';

// 图片元数据
export interface ImageMetadata {
  size: number;
  dimensions: {
    width: number;
    height: number;
  };
  format: string;
}

// 图片基础信息
export interface ImageInfo {
  id: string;
  groupId?: string;
  relativePath: string;
  variant: ImageVariant;
  metadata: ImageMetadata;
  createdAt?: string;
  updatedAt?: string;
}

// 图片上传请求数据
export interface ImageUploadRequest {
  file: File;
  category: ImageCategory;
  resourceId: string;
  imageType: ImageType;
  variant: ImageVariant;
  groupId?: string;
}

// 图片上传响应数据
export interface ImageUploadResponse {
  code: number;
  message: string;
  data: ImageInfo;
}

// 图片列表查询参数
export interface ImageListParams {
  category: ImageCategory;
  resourceId: string;
  page?: number;
  pageSize?: number;
  variant?: ImageVariant;
  imageType?: ImageType;
}

// 图片列表响应数据
export interface ImageListResponse {
  code: number;
  message: string;
  data: {
    images: ImageInfo[];
    pagination?: {
      page: number;
      pageSize: number;
      total: number;
      totalPages: number;
    };
  };
}

// 图片删除请求数据
export interface ImageDeleteRequest {
  relativePaths: string[];
}

// 图片删除响应数据
export interface ImageDeleteResponse {
  code: number;
  message: string;
  data?: {
    deletedCount: number;
    failedPaths?: string[];
  };
}

// 图片预览状态
export interface ImagePreviewState {
  isOpen: boolean;
  currentIndex: number;
  images: ImageInfo[];
}

// 文件上传状态
export interface FileUploadState {
  file: File;
  progress: number;
  status: 'pending' | 'uploading' | 'success' | 'error';
  error?: string;
  result?: ImageInfo;
}

// 图片上传组件Props
export interface ImageUploadProps {
  category: ImageCategory;
  resourceId: string;
  imageType: ImageType;
  variant?: ImageVariant;
  groupId?: string;
  multiple?: boolean;
  accept?: string;
  maxSize?: number; // 字节
  onUploadSuccess?: (images: ImageInfo[]) => void;
  onUploadError?: (error: Error) => void;
  className?: string;
  disabled?: boolean;
}

// 图片卡片组件Props
export interface ImageCardProps {
  image: ImageInfo;
  selected?: boolean;
  onSelect?: (image: ImageInfo, selected: boolean) => void;
  onPreview?: (image: ImageInfo) => void;
  onDelete?: (image: ImageInfo) => void;
  showActions?: boolean;
  className?: string;
}

// 图片网格组件Props
export interface ImageGridProps {
  images: ImageInfo[];
  loading?: boolean;
  selectedImages?: Set<string>;
  onSelectionChange?: (selectedImages: Set<string>) => void;
  onPreview?: (image: ImageInfo, index: number) => void;
  onDelete?: (images: ImageInfo[]) => void;
  showActions?: boolean;
  className?: string;
}

// 图片预览组件Props
export interface ImagePreviewProps {
  isOpen: boolean;
  images: ImageInfo[];
  currentIndex: number;
  onClose: () => void;
  onNext?: () => void;
  onPrevious?: () => void;
  onDelete?: (image: ImageInfo) => void;
  showActions?: boolean;
}

// 图片管理器组件Props
export interface ImageManagerProps {
  category: ImageCategory;
  resourceId: string;
  allowUpload?: boolean;
  allowDelete?: boolean;
  showFilters?: boolean;
  className?: string;
}

// 图片筛选器状态
export interface ImageFilterState {
  imageType?: ImageType;
  variant?: ImageVariant;
  sortBy?: 'createdAt' | 'size' | 'name';
  sortOrder?: 'asc' | 'desc';
}

// 图片操作结果
export interface ImageOperationResult {
  success: boolean;
  message: string;
  data?: any;
  errors?: string[];
}

// 批量操作类型
export type BatchOperation = 'delete' | 'download' | 'move';

// 批量操作参数
export interface BatchOperationParams {
  operation: BatchOperation;
  images: ImageInfo[];
  options?: Record<string, any>;
}

// 图片URL生成选项
export interface ImageUrlOptions {
  variant?: ImageVariant;
  quality?: number;
  format?: 'webp' | 'jpeg' | 'png';
}

// 图片加载状态
export interface ImageLoadState {
  loading: boolean;
  error?: string;
  loaded: boolean;
}

// 拖拽上传状态
export interface DragUploadState {
  isDragOver: boolean;
  isDragActive: boolean;
  files: File[];
}

// 图片压缩选项
export interface ImageCompressionOptions {
  maxWidth?: number;
  maxHeight?: number;
  quality?: number;
  format?: 'jpeg' | 'webp' | 'png';
}

// 图片处理结果
export interface ImageProcessResult {
  file: File;
  originalSize: number;
  compressedSize: number;
  compressionRatio: number;
}
