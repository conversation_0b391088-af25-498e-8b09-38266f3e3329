import { useQuery, useQueryClient } from '@tanstack/react-query';

import { queryKeys } from '@/constants/queryKeys';
import { request } from '@/lib/http';
import { useAdminSuccessToast } from '@/hooks/useAdminSuccessToast';
import type {
  AdminVenue,
  AdminVenuesResponse,
  AdminVenuesParams,
  CreateVenueInput,
  UpdateVenueInput
} from '@/types/venue';

// 管理员venues列表
export function useAdminVenues(params?: AdminVenuesParams) {
  return useQuery({
    queryKey: queryKeys.adminVenues(params),
    placeholderData: (prev) => prev,
    queryFn: () => {
      const qs = new URLSearchParams();
      if (params?.page) qs.append("page", params.page);
      if (params?.pageSize) qs.append("pageSize", params.pageSize);
      if (params?.keyword) qs.append("keyword", params.keyword);
      if (params?.city) qs.append("city", params.city);
      if (params?.capacity_min) qs.append("capacity_min", params.capacity_min);
      if (params?.capacity_max) qs.append("capacity_max", params.capacity_max);
      if (params?.has_parking) qs.append("has_parking", params.has_parking);
      if (params?.has_wifi) qs.append("has_wifi", params.has_wifi);

      const url = "/admin/venues" + (qs.size ? `?${qs.toString()}` : "");
      return request<AdminVenuesResponse>(url);
    },
    staleTime: 1000 * 60 * 5,
  });
}

// 管理员venue详情
export function useAdminVenueDetail(id: string | undefined) {
  return useQuery({
    queryKey: queryKeys.adminVenueDetail(id ?? ""),
    enabled: !!id,
    queryFn: () => request<AdminVenue>(`/admin/venues/${id}`),
    staleTime: 1000 * 60 * 5,
  });
}

// 创建venue
export function useCreateVenue() {
  const qc = useQueryClient();
  return useAdminSuccessToast(
    (payload: CreateVenueInput) =>
      request<AdminVenue>("/admin/venues", {
        method: "POST",
        body: JSON.stringify(payload),
      }),
    {
      onSuccess: () => {
        // 失效所有 admin venues 相关的查询
        qc.invalidateQueries({
          predicate: (query) =>
            Array.isArray(query.queryKey) &&
            query.queryKey[0] === "admin" &&
            query.queryKey[1] === "venues"
        });
      },
    },
    "创建成功"
  );
}

// 更新venue
export function useUpdateVenue(id: string) {
  const qc = useQueryClient();
  return useAdminSuccessToast(
    (payload: UpdateVenueInput) =>
      request<AdminVenue>(`/admin/venues/${id}`, {
        method: "PUT",
        body: JSON.stringify(payload),
      }),
    {
      onSuccess: () => {
        // 失效所有 admin venues 相关的查询（不管参数如何）
        qc.invalidateQueries({
          predicate: (query) =>
            Array.isArray(query.queryKey) &&
            query.queryKey[0] === "admin" &&
            query.queryKey[1] === "venues"
        });
        qc.invalidateQueries({ queryKey: queryKeys.adminVenueDetail(id) });
      },
    },
    "修改成功"
  );
}

// 删除venue
export function useDeleteVenue() {
  const qc = useQueryClient();
  return useAdminSuccessToast(
    (id: string) => request(`/admin/venues/${id}`, { method: "DELETE" }),
    {
      onSuccess: () => {
        // 失效所有 admin venues 相关的查询
        qc.invalidateQueries({
          predicate: (query) =>
            Array.isArray(query.queryKey) &&
            query.queryKey[0] === "admin" &&
            query.queryKey[1] === "venues"
        });
      },
    },
    "删除成功"
  );
}

// 公开venues列表（用于前端选择器）
export function usePublicVenues(params?: { keyword?: string; pageSize?: number }) {
  return useQuery({
    queryKey: ['venues', params],
    queryFn: () => {
      const qs = new URLSearchParams();
      if (params?.keyword) qs.append("keyword", params.keyword);
      if (params?.pageSize) qs.append("pageSize", params.pageSize.toString());

      const url = "/venues" + (qs.size ? `?${qs.toString()}` : "");
      return request<AdminVenuesResponse>(url);
    },
    staleTime: 1000 * 60 * 5,
  });
}

// 公开venue详情
export function usePublicVenueDetail(id: string) {
  return useQuery({
    queryKey: ['venues', id],
    queryFn: () => request(`/venues/${id}`),
    enabled: !!id,
    staleTime: 1000 * 60 * 5,
  });
}
