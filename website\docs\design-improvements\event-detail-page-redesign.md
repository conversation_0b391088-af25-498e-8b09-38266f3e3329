# Event Detail Page 重设计方案

## 当前问题分析

### 视觉设计问题
1. **布局过于简单**：缺乏现代展会网站的视觉冲击力
2. **信息层次不清**：所有信息平铺展示，缺乏重点突出
3. **视觉元素单调**：缺乏品牌感和吸引力
4. **响应式体验不佳**：在不同屏幕尺寸下的适配有待优化

### 功能缺失
1. **实用信息**：交通指南、住宿推荐、票务信息
2. **展会统计**：参展商数量、预期参观人数等关键数据
3. **日程安排**：展会时间表、重要活动安排
4. **便民服务**：会场设施、服务指南

## 重设计方案

### 1. 英雄区域 (Hero Section)
```tsx
// 建议的新布局结构
<HeroSection>
  <BackgroundImage src={event.image_url} />
  <HeroContent>
    <EventBadge status="upcoming" />
    <EventTitle>{event.name}</EventTitle>
    <EventSubtitle>{event.date} • {event.venue_name}</EventSubtitle>
    <CTAButtons>
      <Button variant="primary">查看详情</Button>
      <Button variant="outline">官方网站</Button>
    </CTAButtons>
  </HeroContent>
</HeroSection>
```

### 2. 快速信息卡片
```tsx
<QuickInfoCards>
  <InfoCard icon={<Calendar />} title="日期" value={event.date} />
  <InfoCard icon={<MapPin />} title="地点" value={event.venue_name} />
  <InfoCard icon={<Users />} title="参展商" value={`${circlesCount}+`} />
  <InfoCard icon={<Clock />} title="开放时间" value="10:00 - 18:00" />
</QuickInfoCards>
```

### 3. 标签页导航
```tsx
<TabNavigation>
  <Tab id="overview">概览</Tab>
  <Tab id="exhibitors">参展商</Tab>
  <Tab id="schedule">日程</Tab>
  <Tab id="venue">会场信息</Tab>
  <Tab id="travel">交通住宿</Tab>
</TabNavigation>
```

### 4. 增强的参展商列表
```tsx
<ExhibitorsSection>
  <SectionHeader>
    <Title>参展商 ({total})</Title>
    <ViewToggle options={['grid', 'list', 'map']} />
  </SectionHeader>
  
  <FilterBar>
    <SearchInput />
    <CategoryFilter />
    <SortOptions />
    <ViewOptions />
  </FilterBar>
  
  <ExhibitorsList>
    {/* 增强的卡片设计 */}
  </ExhibitorsList>
</ExhibitorsSection>
```

## 技术实现建议

### 1. 组件重构
- 将 EventHeader 拆分为多个专门的子组件
- 创建可复用的 InfoCard、TabNavigation 等组件
- 使用 Framer Motion 添加适当的动画效果

### 2. 性能优化
- 图片懒加载和优化
- 虚拟滚动优化
- 预加载关键资源

### 3. 用户体验
- 添加骨架屏和加载状态
- 优化移动端体验
- 增加无障碍支持
- 改善信息层次和视觉引导

## 参考案例

### 国际展会网站
1. **CES**: 大胆的视觉设计，清晰的信息架构
2. **MWC**: 优秀的移动端体验
3. **IFA**: 丰富的互动元素

### 设计原则
1. **信息优先级**：重要信息突出显示
2. **视觉层次**：使用颜色、大小、间距创建层次
3. **功能实用性**：专注于核心功能，避免过度设计
4. **品牌一致性**：保持整体设计风格统一
5. **性能优先**：确保良好的加载速度和响应性
