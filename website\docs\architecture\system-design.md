# System Design

> 本节提供 Ayafeed 前后端与基础设施的整体架构图。

```mermaid
graph TD;
  Browser -->|HTTPS| Frontend(Next.js App);
  Frontend -->|REST| API[Cloudflare Workers API];
  API --> DB[(PostgreSQL)];
  API --> KV[(Cloudflare KV)];
  API --> Queue[Message Queue];
  Queue --> Worker[Background Workers];
```

- 前端部署：Vercel（Preview） / Cloudflare Pages（Prod）
- API：Cloudflare Workers，单文件入口，边缘运行
- 数据库：PostgreSQL（Supabase 管理），读写分离
- 缓存：Cloudflare KV + HTTP Cache-Control
- 消息队列：用于生成地图缩略图等异步任务

> 若架构有重大更新，请同步修改此图并提交 ADR。 