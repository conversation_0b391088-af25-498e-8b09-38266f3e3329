"use client"
import { <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON> } from "react-leaflet"

import "leaflet/dist/leaflet.css"
import { cn } from "@/lib/utils"

interface Venue {
  id: string // "tokyo-big-sight"
  name: string // "東京ビッグサイト 西ホール"
  address?: string // 可选字段：详细地址（如未来需要逆地址）
  lat: number // 纬度 e.g. 35.6298
  lng: number // 经度 e.g. 139.7976
}

interface Props {
  venue: Venue
  className?: string
}

export default function VenueLocationMap({ venue, className }: Props) {
  const { lat, lng, name } = venue
  return (
    <div className={cn("relative z-0", className)}>
      <MapContainer
        center={[lat, lng]}
        zoom={17}
        className="h-full min-h-[18rem] w-full"
        zoomControl={true}
        scrollWheelZoom={false}
      >
      <TileLayer
        attribution='&copy; <a href="https://www.openstreetmap.org/copyright">OpenStreetMap</a> contributors'
        url="https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png"
      />
        <Marker position={[lat, lng]}>
          <Popup>{name}</Popup>
        </Marker>
      </MapContainer>
    </div>
  )
}
