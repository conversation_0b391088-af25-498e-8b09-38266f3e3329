import { describe, test, expect, vi, beforeEach } from 'vitest'
import { render, screen, fireEvent } from '@testing-library/react'
import { renderWithProviders } from '@/__test__/test-utils'
import AdminHeader from '../header'

// Mock useAuth hook
const mockLogout = vi.fn()
const mockUseAuth = vi.fn()

vi.mock('@/contexts/user', async (importOriginal) => {
  const actual = await importOriginal()
  return {
    ...actual,
    useAuth: () => mockUseAuth()
  }
})

describe('AdminHeader (Radix Migration)', () => {
  beforeEach(() => {
    vi.clearAllMocks()
    
    // 设置默认的 mock 返回值
    mockUseAuth.mockReturnValue({
      logout: mockLogout
    })
  })

  test('renders admin header with title', () => {
    renderWithProviders(<AdminHeader />)
    
    // 检查标题
    expect(screen.getByText('后台管理系统')).toBeInTheDocument()
    
    // 检查退出登录按钮
    expect(screen.getByText('退出登录')).toBeInTheDocument()
  })

  test('renders logout button with correct styling', () => {
    renderWithProviders(<AdminHeader />)
    
    const logoutButton = screen.getByRole('button', { name: '退出登录' })
    
    // 检查按钮存在
    expect(logoutButton).toBeInTheDocument()
    
    // 检查按钮样式类 (ghost variant, sm size)
    expect(logoutButton).toHaveClass('hover:bg-accent', 'hover:text-accent-foreground')
    expect(logoutButton).toHaveClass('h-9', 'rounded-md', 'px-3')
  })

  test('calls logout function when logout button is clicked', () => {
    renderWithProviders(<AdminHeader />)
    
    const logoutButton = screen.getByRole('button', { name: '退出登录' })
    
    // 点击退出登录按钮
    fireEvent.click(logoutButton)
    
    // 验证 logout 函数被调用
    expect(mockLogout).toHaveBeenCalledTimes(1)
  })

  test('header has correct layout and styling', () => {
    const { container } = renderWithProviders(<AdminHeader />)
    
    const header = container.querySelector('header')
    
    // 检查 header 的样式类
    expect(header).toHaveClass(
      'h-14',
      'border-b',
      'flex',
      'items-center',
      'justify-between',
      'px-6',
      'bg-card/60',
      'backdrop-blur-sm'
    )
  })

  test('title has correct styling', () => {
    renderWithProviders(<AdminHeader />)
    
    const title = screen.getByText('后台管理系统')
    
    // 检查标题样式
    expect(title).toHaveClass('font-semibold')
  })

  test('logout button is clickable and not disabled', () => {
    renderWithProviders(<AdminHeader />)
    
    const logoutButton = screen.getByRole('button', { name: '退出登录' })
    
    // 检查按钮可点击
    expect(logoutButton).not.toBeDisabled()
    expect(logoutButton).toHaveAttribute('type', 'button')
  })

  test('component renders without crashing', () => {
    const { container } = renderWithProviders(<AdminHeader />)
    expect(container.firstChild).toBeInTheDocument()
  })

  test('handles logout function being undefined gracefully', () => {
    // 测试当 logout 函数未定义时的情况
    mockUseAuth.mockReturnValue({
      logout: undefined
    })

    renderWithProviders(<AdminHeader />)
    
    const logoutButton = screen.getByRole('button', { name: '退出登录' })
    
    // 应该仍然渲染按钮
    expect(logoutButton).toBeInTheDocument()
    
    // 点击不应该报错
    expect(() => fireEvent.click(logoutButton)).not.toThrow()
  })

  test('button has correct accessibility attributes', () => {
    renderWithProviders(<AdminHeader />)
    
    const logoutButton = screen.getByRole('button', { name: '退出登录' })
    
    // 检查可访问性属性
    expect(logoutButton).toHaveAttribute('type', 'button')
    expect(logoutButton).toHaveTextContent('退出登录')
  })

  test('header layout is responsive', () => {
    renderWithProviders(<AdminHeader />)
    
    const title = screen.getByText('后台管理系统')
    const logoutButton = screen.getByRole('button', { name: '退出登录' })
    
    // 验证两个元素都存在（测试 justify-between 布局）
    expect(title).toBeInTheDocument()
    expect(logoutButton).toBeInTheDocument()
  })

  test('maintains consistent styling across renders', () => {
    const { rerender } = renderWithProviders(<AdminHeader />)
    
    let logoutButton = screen.getByRole('button', { name: '退出登录' })
    const initialClasses = logoutButton.className
    
    // 重新渲染
    rerender(<AdminHeader />)
    
    logoutButton = screen.getByRole('button', { name: '退出登录' })
    expect(logoutButton.className).toBe(initialClasses)
  })

  test('button has correct focus and hover states', () => {
    renderWithProviders(<AdminHeader />)
    
    const logoutButton = screen.getByRole('button', { name: '退出登录' })
    
    // 检查 focus 和 hover 相关的样式类
    expect(logoutButton).toHaveClass('outline-none')
    expect(logoutButton).toHaveClass('focus-visible:ring-2')
    expect(logoutButton).toHaveClass('focus-visible:ring-ring')
    expect(logoutButton).toHaveClass('focus-visible:ring-offset-2')
  })
})
