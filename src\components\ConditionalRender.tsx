/**
 * 条件渲染组件
 * 基于后端文档包的认证集成规范
 */

'use client';

import React from 'react';
import { usePermissions } from '@/hooks/usePermissions';

interface ConditionalRenderProps {
  children: React.ReactNode;
  permission: keyof ReturnType<typeof usePermissions>;
  fallback?: React.ReactNode;
}

export function ConditionalRender({
  children,
  permission,
  fallback = null
}: ConditionalRenderProps) {
  const permissions = usePermissions();

  if (!permissions[permission]) {
    return <>{fallback}</>;
  }

  return <>{children}</>;
}
