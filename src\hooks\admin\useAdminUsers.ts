import { useQuery } from "@tanstack/react-query";

import { queryKeys } from "@/constants/queryKeys";
import { request } from "@/lib/http";
import type { User } from "@/types/user";

export interface AdminUsersParams {
  page?: number;
  pageSize?: number;
  keyword?: string;
}

export function useAdminUsers(params?: AdminUsersParams) {
  return useQuery({
    queryKey: queryKeys.adminUsers(params),
    placeholderData: (prev) => prev,
    queryFn: () => {
      const qs = new URLSearchParams();
      if (params?.page) qs.append("page", String(params.page));
      if (params?.keyword) qs.append("keyword", params.keyword);
      if (params?.pageSize) qs.append("pageSize", String(params.pageSize));
      const url = "/admin/users" + (qs.size ? `?${qs.toString()}` : "");
      return request<User[]>(url);
    },
    staleTime: 1000 * 60 * 5,
  });
} 