<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>测试表单提交</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        .test-section {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .success { background-color: #d4edda; border-color: #c3e6cb; }
        .error { background-color: #f8d7da; border-color: #f5c6cb; }
        .info { background-color: #d1ecf1; border-color: #bee5eb; }
        button {
            background-color: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background-color: #0056b3;
        }
        .log {
            background-color: #f8f9fa;
            border: 1px solid #dee2e6;
            padding: 10px;
            margin: 10px 0;
            border-radius: 4px;
            font-family: monospace;
            white-space: pre-wrap;
            max-height: 300px;
            overflow-y: auto;
        }
    </style>
</head>
<body>
    <h1>Admin Events 表单提交测试</h1>
    
    <div class="test-section info">
        <h3>测试说明</h3>
        <p>这个页面用于测试 admin/events 页面的表单提交功能。</p>
        <p>修复内容：将 MultilingualEventInputSchema 中的 venue_name_* 必填字段替换为 venue_id 必填字段。</p>
    </div>

    <div class="test-section">
        <h3>测试步骤</h3>
        <ol>
            <li>打开 <a href="http://localhost:3001/admin/events/new" target="_blank">创建展会页面</a></li>
            <li>填写所有必填字段：
                <ul>
                    <li>中文名称、日文名称、英文名称</li>
                    <li>中文日期、日文日期、英文日期</li>
                    <li>选择展会场馆（venue_id）</li>
                </ul>
            </li>
            <li>点击"创建展会"按钮</li>
            <li>观察是否有网络请求发出</li>
            <li>检查是否有错误信息或成功反馈</li>
        </ol>
    </div>

    <div class="test-section">
        <h3>快速测试</h3>
        <button onclick="openNewEventPage()">打开创建展会页面</button>
        <button onclick="openEditEventPage()">打开编辑展会页面</button>
        <button onclick="checkNetworkRequests()">检查网络请求</button>
    </div>

    <div class="test-section">
        <h3>测试日志</h3>
        <div id="log" class="log">等待测试开始...</div>
        <button onclick="clearLog()">清空日志</button>
    </div>

    <script>
        function log(message) {
            const logElement = document.getElementById('log');
            const timestamp = new Date().toLocaleTimeString();
            logElement.textContent += `[${timestamp}] ${message}\n`;
            logElement.scrollTop = logElement.scrollHeight;
        }

        function clearLog() {
            document.getElementById('log').textContent = '';
        }

        function openNewEventPage() {
            log('打开创建展会页面...');
            window.open('http://localhost:3001/admin/events/new', '_blank');
        }

        function openEditEventPage() {
            log('打开编辑展会页面（需要先有展会数据）...');
            // 这里可以添加一个测试展会ID
            window.open('http://localhost:3001/admin/events', '_blank');
        }

        function checkNetworkRequests() {
            log('请在浏览器开发者工具的 Network 标签页中检查网络请求');
            log('1. 按 F12 打开开发者工具');
            log('2. 切换到 Network 标签页');
            log('3. 在表单页面填写数据并提交');
            log('4. 观察是否有 POST /admin/events 请求');
        }

        // 页面加载完成后的初始化
        document.addEventListener('DOMContentLoaded', function() {
            log('测试页面已加载');
            log('修复内容：移除了 venue_name_en/ja/zh 必填字段，只保留 venue_id 必填字段');
            log('这应该解决表单提交时无反应的问题');
        });
    </script>
</body>
</html>
