#!/usr/bin/env ts-node
/**
 * check_feature_first.ts
 *
 * 用于 CI：确保已迁移模块不再在旧层级目录存在残留文件。
 * 使用方法：
 *    pnpm tsx scripts/check_feature_first.ts bookmark circles events
 */
import { resolve } from 'path';

import fg from 'fast-glob';

const migratedModules = process.argv.slice(2);
if (!migratedModules.length) {
  console.error('请传入已迁移模块名，如: bookmark');
  process.exit(1);
}

const root = resolve(__dirname, '..');
const oldLayerDirs = ['controllers', 'services', 'schemas', 'repositories'];

async function main() {
  const violations: string[] = [];
  for (const moduleName of migratedModules) {
    for (const dir of oldLayerDirs) {
      const pattern = `src/${dir}/${moduleName}*.ts`;
      const matches = await fg(pattern, { cwd: root });
      matches.forEach((file: string) => violations.push(file));
    }
  }

  if (violations.length) {
    console.error('\u274C 未完成模块迁移，仍检测到旧目录文件：');
    violations.forEach((v) => console.error('  -', v));
    process.exit(1);
  }
  console.log('\u2705 Feature-first 结构检查通过');
}

main();
