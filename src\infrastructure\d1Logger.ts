import { Context } from 'hono';
import { v4 as uuid } from 'uuid';

import { getDB } from './db';
import type { Logger } from './logger';

/**
 * 基于 Cloudflare D1 数据库的 Logger，实现持久化审计日志。
 *
 * - `message` 作为 action 字段存储，例如 CREATE_EVENT
 * - `meta` 可包含 targetType / targetId 及其它任意键值
 */
export class D1Logger implements Logger {
  constructor(private readonly c: Context) {}

  private async write(message: string, meta?: Record<string, unknown>) {
    // 获取当前登录用户
    const auth = this.c.get('auth') as
      | { user?: { id: string; username: string } }
      | undefined;
    const user = auth?.user;
    if (!user) return; // 未登录跳过记录

    const { targetType, targetId, ...rest } = (meta || {}) as any;
    const metaStr = Object.keys(rest).length ? JSON.stringify(rest) : null;

    const db = getDB(this.c);
    const id = uuid();
    await db
      .prepare(
        'INSERT INTO logs (id, user_id, username, action, target_type, target_id, meta) VALUES (?, ?, ?, ?, ?, ?, ?)'
      )
      .bind(id, user.id, user.username, message, targetType, targetId, metaStr)
      .run();
  }

  info(message: string, meta?: Record<string, unknown>) {
    return this.write(message, meta);
  }
  warn(message: string, meta?: Record<string, unknown>) {
    return this.write(message, meta);
  }
  error(message: string, meta?: Record<string, unknown>) {
    return this.write(message, meta);
  }
  debug(message: string, meta?: Record<string, unknown>) {
    return this.write(message, meta);
  }
}
