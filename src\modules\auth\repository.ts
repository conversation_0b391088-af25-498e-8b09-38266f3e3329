import type { D1Database } from '@cloudflare/workers-types';

import { D1AuthRepository } from '@/infrastructure/db/authRepository';

/**
 * AuthUser 聚合根
 */
export interface AuthUser {
  id: string;
  username: string;
  role: string;
  hashedPassword?: string;
}

/**
 * 认证相关持久化接口
 */
export interface AuthRepository {
  /** 创建用户及主密码 */
  createUser(
    id: string,
    username: string,
    role: string,
    hashedPassword: string
  ): Promise<AuthUser>;

  /** 通过用户名获取用户及散列密码（登录用） */
  findUserWithPasswordByUsername(username: string): Promise<AuthUser | null>;

  /** 创建会话 */
  createSession(
    sessionId: string,
    userId: string,
    expiresAt: Date
  ): Promise<void>;

  /** 校验会话并返回用户 */
  validateSession(sessionId: string): Promise<AuthUser | null>;

  /** 失效会话 */
  invalidateSession(sessionId: string): Promise<void>;

  /** 清理过期会话 */
  cleanupExpiredSessions(): Promise<void>;
}

/**
 * 仓储工厂：目前仅提供 D1 实现
 */
export function createAuthRepository(db: D1Database): AuthRepository {
  return new D1AuthRepository(db);
}
