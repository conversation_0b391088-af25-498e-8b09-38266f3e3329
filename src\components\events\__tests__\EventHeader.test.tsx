/* eslint-disable @next/next/no-img-element */
import { expect, test, vi } from "vitest"

import EventHeader from "@/components/events/EventHeader"
import { renderWithProviders, screen } from "@test/test-utils"

// Mock 子组件
vi.mock("@/components/events/EventPoster", () => {
  return {
    __esModule: true,
    default: ({ eventName }: { eventName?: string | null }) => (
      <div data-testid="event-poster">
        {eventName && <img alt={`${eventName} - Event Poster`} />}
      </div>
    ),
  }
})

vi.mock("@/components/events/EventInfo", () => {
  return {
    __esModule: true,
    default: ({ event }: { event: any }) => (
      <div data-testid="event-info">
        {event && (
          <>
            <h1>{event.name}</h1>
            <div>{event.date}</div>
            <div>{event.venue_name}</div>
            {event.venue_address && <div>{event.venue_address}</div>}
            {event.url && <a href={event.url}>Official Website</a>}
          </>
        )}
      </div>
    ),
  }
})

vi.mock("@/components/events/EventMap", () => {
  return {
    __esModule: true,
    default: () => <div data-testid="event-map" />,
  }
})

const sampleEvent = {
  id: "reitaisai-22",
  name: "Reitaisai 22",
  date: "May 3, 2025 (Sat) 10:30 – 15:30",
  date_sort: 20250503,
  venue_name: "Tokyo Big Sight",
  venue_address: "3-11-1 Ariake, Koto City, Tokyo",
  venue_lat: 35.6298,
  venue_lng: 139.793,
  url: "https://reitaisai.com/rts22/",
  image_url: "/images/events/reitaisai-22/thumb.jpg",
} as any

test("renders event basic info and map", () => {
  renderWithProviders(<EventHeader event={sampleEvent} />)

  // 检查子组件是否渲染
  expect(screen.getByTestId("event-poster")).toBeInTheDocument()
  expect(screen.getByTestId("event-info")).toBeInTheDocument()
  expect(screen.getByTestId("event-map")).toBeInTheDocument()

  // 检查事件信息是否正确传递给子组件
  expect(screen.getByRole("heading", { name: /Reitaisai 22/ })).toBeInTheDocument()
  expect(screen.getByText("May 3, 2025 (Sat) 10:30 – 15:30")).toBeInTheDocument()
  expect(screen.getByText("Tokyo Big Sight")).toBeInTheDocument()
  expect(screen.getByText("3-11-1 Ariake, Koto City, Tokyo")).toBeInTheDocument()
  expect(screen.getByRole("link", { name: "Official Website" })).toHaveAttribute("href", "https://reitaisai.com/rts22/")
})

test("renders with null event", () => {
  renderWithProviders(<EventHeader event={null} />)

  // 子组件应该仍然渲染，但处理 null 事件
  expect(screen.getByTestId("event-poster")).toBeInTheDocument()
  expect(screen.getByTestId("event-info")).toBeInTheDocument()
  expect(screen.getByTestId("event-map")).toBeInTheDocument()
})

test("renders with loading state", () => {
  renderWithProviders(<EventHeader event={sampleEvent} isLoading={true} />)

  expect(screen.getByTestId("event-poster")).toBeInTheDocument()
  expect(screen.getByTestId("event-info")).toBeInTheDocument()
  expect(screen.getByTestId("event-map")).toBeInTheDocument()
})