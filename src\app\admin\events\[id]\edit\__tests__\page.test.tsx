"use client";

import { useRouter, useParams } from "next/navigation";
import React from "react";
import type { Mock } from "vitest";

import EditEventPage from "../page";

import {
  renderWithProviders,
  screen,
  fireEvent,
  waitFor,
} from "@test/test-utils";


// ------- mocks -------
const mutateMock = vi.fn();
const sampleMultilingualDetail = {
  id: "event-1",
  name_en: "Original Event",
  name_ja: "原展会",
  name_zh: "原展会",
  date_en: "July 1, 2025",
  date_ja: "2025年7月1日",
  date_zh: "2025年7月1日",
  date_sort: 20250701,
  venue_id: "tokyo-big-sight",
  url: "https://example.com",
  image_url: "/images/events/original/thumb.jpg",
};

vi.mock("next/navigation", () => ({
  useRouter: vi.fn(),
  useParams: vi.fn(),
}));

vi.mock("@/hooks/admin/useAdminEventDetail", () => ({
  useAdminEventDetail: () => ({
    multilingualData: sampleMultilingualDetail,
    isLoading: false
  }),
}));

// Mock venue hooks
vi.mock("@/hooks/admin/useVenues", () => ({
  useAdminVenues: () => ({
    data: {
      items: [
        {
          id: "tokyo-big-sight",
          name: "Tokyo Big Sight",
          address: "3-11-1 Ariake, Koto City, Tokyo",
          lat: 35.6298,
          lng: 139.793,
          capacity: 50000,
        },
      ],
      total: 1,
      page: 1,
      pageSize: 20,
    },
    isLoading: false,
    error: null,
  }),
  usePublicVenues: () => ({
    data: {
      items: [
        {
          id: "tokyo-big-sight",
          name: "Tokyo Big Sight",
          address: "3-11-1 Ariake, Koto City, Tokyo",
          lat: 35.6298,
          lng: 139.793,
          capacity: 50000,
        },
      ],
      total: 1,
      page: 1,
      pageSize: 50,
    },
    isLoading: false,
    error: null,
  }),
}));

const createState = { isPending: false };

vi.mock("@/hooks/admin/useUpdateEvent", () => ({
  useUpdateEvent: () => ({
    mutate: mutateMock,
    get isPending() {
      return createState.isPending;
    },
  }),
}));

const mockPush = vi.fn();

describe("EditEventPage", () => {
  beforeEach(() => {
    vi.resetAllMocks();
    (useRouter as unknown as Mock).mockReturnValue({ push: mockPush });
    (useParams as unknown as Mock).mockReturnValue({ id: "event-1" });
  });

  test("should render form with initial values", async () => {
    renderWithProviders(<EditEventPage />);

    // 等待表单加载完成
    await waitFor(() => {
      // 检查中文标签页的内容
      expect(screen.getByDisplayValue("原展会")).toBeInTheDocument();
      expect(screen.getByDisplayValue("2025年7月1日")).toBeInTheDocument();
      // venue字段已经改为选择器，不再是文本输入
    });
  });

  test("should show validation error when name cleared", async () => {
    renderWithProviders(<EditEventPage />);

    // 等待表单加载
    await waitFor(() => {
      expect(screen.getByDisplayValue("原展会")).toBeInTheDocument();
    });

    // 清空中文名称字段
    fireEvent.change(screen.getByLabelText("中文名称 *"), { target: { value: "" } });
    fireEvent.click(screen.getByRole("button", { name: "更新展会" }));

    await waitFor(() => {
      expect(screen.getByText("中文名称必填")).toBeInTheDocument();
    });

    expect(mutateMock).not.toHaveBeenCalled();
  });

  test("should render form with loaded data", async () => {
    renderWithProviders(<EditEventPage />);

    // 等待表单完全加载，检查数据是否正确显示
    await waitFor(() => {
      expect(screen.getByDisplayValue("原展会")).toBeInTheDocument();
      expect(screen.getByDisplayValue("2025年7月1日")).toBeInTheDocument();
    });

    // 检查表单元素是否存在
    expect(screen.getByLabelText("中文名称 *")).toBeInTheDocument();
    expect(screen.getByLabelText("中文日期 *")).toBeInTheDocument();
    expect(screen.getByRole("button", { name: "更新展会" })).toBeInTheDocument();
  });

  test("should show loading state when mutation is pending", () => {
    createState.isPending = true;

    renderWithProviders(<EditEventPage />);

    const button = screen.getByRole("button", { name: "保存中..." });
    expect(button).toBeDisabled();

    createState.isPending = false;
  });

  test("should have update button available", async () => {
    renderWithProviders(<EditEventPage />);

    // 等待表单完全加载
    await waitFor(() => {
      expect(screen.getByDisplayValue("原展会")).toBeInTheDocument();
    });

    // 检查更新按钮是否存在且可点击
    const submitButton = screen.getByRole("button", { name: "更新展会" });
    expect(submitButton).toBeInTheDocument();
    expect(submitButton).not.toBeDisabled();
  });
}); 