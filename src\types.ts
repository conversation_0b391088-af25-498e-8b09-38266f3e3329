import { D1Database } from '@cloudflare/workers-types';

// The shape of the environment variables for the Hono app
export type Env = {
  Bindings: {
    DB: D1Database;
    ENVIRONMENT: 'development' | 'production';
  };
};

// Shape of the auth context set by the middleware
export type AuthContext = {
  user: {
    id: string;
    username: string;
  } | null;
  session: {
    id: string;
  } | null;
};

// Define the full Hono app type by extending the base env
export type HonoApp = {
  Bindings: Env['Bindings'];
  Variables: {
    auth: AuthContext;
  };
};
