"use client";

import Link from "next/link";
import { useRouter } from "next/navigation";
import React from "react";

import { But<PERSON> } from "@/components/ui/button";
import { useAdminCircles } from "@/hooks/admin/useAdminCircles";
import { useDeleteCircle } from "@/hooks/admin/useDeleteCircle";

export default function AdminCirclesPage() {
  const router = useRouter();

  // 暂不支持分页 / 搜索，可后续注入 params
  const { data: circles = [], isLoading } = useAdminCircles();
  const deleteCircle = useDeleteCircle();

  function handleDelete(id: string) {
    if (!confirm("确定删除该社团吗？")) return;
    deleteCircle.mutate(id);
  }

  return (
    <div className="space-y-4">
      <div className="flex justify-between items-center">
        <h1 className="text-2xl font-bold">社团管理</h1>
        <Button onClick={() => { router.push("/admin/circles/new"); }}>新增社团</Button>
      </div>

      {isLoading ? (
        <p>加载中...</p>
      ) : (
        <table className="w-full border text-sm">
          <thead>
            <tr className="bg-muted">
              <th className="border px-2 py-1">ID</th>
              <th className="border px-2 py-1">名称</th>
              <th className="border px-2 py-1">作者</th>
              <th className="border px-2 py-1 w-32">操作</th>
            </tr>
          </thead>
          <tbody>
            {circles.map((c) => (
              <tr key={c.id}>
                <td className="border px-2 py-1">{c.id}</td>
                <td className="border px-2 py-1">{c.name}</td>
                <td className="border px-2 py-1">{c.author}</td>
                <td className="border px-2 py-1 space-x-2 text-center">
                  <Link
                    href={`/admin/circles/${c.id}/edit`}
                    className="text-primary hover:underline"
                  >
                    编辑
                  </Link>
                  <button
                    onClick={() => { handleDelete(c.id); }}
                    className="text-destructive hover:underline"
                    disabled={deleteCircle.isPending}
                  >
                    删除
                  </button>
                </td>
              </tr>
            ))}
          </tbody>
        </table>
      )}
    </div>
  );
} 