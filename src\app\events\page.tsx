// app/events/page.tsx (Server Component)
import dynamic from "next/dynamic";
import { Metadata } from "next";
import { QueryClient, dehydrate } from "@tanstack/react-query";
import { HydrationBoundary } from "@tanstack/react-query";
import { getEventsQuery } from "@/api/generated/ayafeedComponents";

const EventsList = dynamic(() => import("./EventsList"));

const SITE_URL = process.env.NEXT_PUBLIC_SITE_URL ?? "";

export const metadata: Metadata = {
  title: "会展列表 | Ayafeed",
  description: "最新同人/漫展查询，浏览即将举行的活动",
  alternates: { canonical: `${SITE_URL}/events` },
  openGraph: {
    title: "会展列表 | Ayafeed",
    description: "最新同人/漫展查询，浏览即将举行的活动",
    url: `${SITE_URL}/events`,
    type: "website",
    images: [{ url: "/next.svg" }],
  },
  twitter: {
    card: "summary_large_image",
    title: "会展列表 | Ayafeed",
    description: "最新同人/漫展查询，浏览即将举行的活动",
    images: ["/next.svg"],
  },
};

export default async function EventsPage() {
  // 服务器预取第一页数据，提升首屏渲染性能
  const qc = new QueryClient();
  const vars = { queryParams: { page: "1", pageSize: "12" } } as const;
  const { queryKey, queryFn } = getEventsQuery(vars);
  await qc.prefetchQuery({ queryKey, queryFn });

  return (
    <div className="px-4 md:px-12 py-8">
      <HydrationBoundary state={dehydrate(qc)}>
        <EventsList />
      </HydrationBoundary>
    </div>
  );
}
