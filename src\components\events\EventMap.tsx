'use client'

import { useState } from 'react'
import dynamic from 'next/dynamic'
import { useTranslations } from 'next-intl'
import { MapPin } from 'lucide-react'

import { cn } from '@/lib/utils'
import type { EventMapProps } from './types'

// 动态导入地图组件，避免 SSR 问题
const VenueLocationMap = dynamic(() => import('@/components/VenueLocationMap'), {
  ssr: false,
  loading: () => <MapLoadingComponent />
})

/**
 * 地图加载组件
 */
function MapLoadingComponent() {
  const t = useTranslations('EventHeader')
  
  return (
    <div className="h-full min-h-[18rem] w-full bg-gray-100 rounded-lg flex items-center justify-center">
      <div className="text-center text-gray-500">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-gray-400 mx-auto mb-2" />
        <p className="text-sm">{t('mapLoading')}</p>
      </div>
    </div>
  )
}

/**
 * 地图错误组件
 */
function MapErrorComponent({ onRetry }: { onRetry: () => void }) {
  const t = useTranslations('EventHeader')
  
  return (
    <div className="h-full min-h-[18rem] w-full bg-gray-50 rounded-lg flex items-center justify-center border border-gray-200">
      <div className="text-center text-gray-500 p-4">
        <MapPin className="h-8 w-8 mx-auto mb-2 text-gray-400" />
        <p className="text-sm mb-3">{t('mapError')}</p>
        <button
          onClick={onRetry}
          className="px-3 py-1 text-xs bg-gray-200 hover:bg-gray-300 rounded transition-colors"
        >
          重试
        </button>
      </div>
    </div>
  )
}

/**
 * 事件地图组件
 * 
 * 功能：
 * - 显示事件场馆位置地图
 * - 处理地图加载状态和错误
 * - 支持重试机制
 * - 响应式设计
 */
export default function EventMap({
  venue,
  isLoading = false,
  error = null,
  className,
  isPreview = false
}: EventMapProps) {
  const [mapError, setMapError] = useState<string | null>(error)
  const [retryKey, setRetryKey] = useState(0)

  const handleRetry = () => {
    setMapError(null)
    setRetryKey(prev => prev + 1)
  }


  if (isLoading) {
    return (
      <div className={cn("w-full", className)}>
        <MapLoadingComponent />
      </div>
    )
  }

  if (mapError) {
    return (
      <div className={cn("w-full", className)}>
        <MapErrorComponent onRetry={handleRetry} />
      </div>
    )
  }

  return (
    <div className={cn("w-full", className)}>
      <div className={cn(
        "relative h-full min-h-[18rem] w-full rounded-lg overflow-hidden border border-gray-200",
        isPreview && "cursor-pointer hover:border-primary/50 transition-colors"
      )}>
        <VenueLocationMap
          key={retryKey}
          venue={venue}
          className="h-full w-full"
        />
        {isPreview && (
          <div className="absolute inset-0 bg-black/0 hover:bg-black/5 transition-colors flex items-center justify-center opacity-0 hover:opacity-100">
            <div className="bg-white/90 backdrop-blur-sm px-3 py-2 rounded-lg text-sm font-medium">
              点击查看详细地图
            </div>
          </div>
        )}
      </div>
    </div>
  )
}
