import { Context } from 'hono';

import { <PERSON><PERSON>, D1Logger } from '../infrastructure';

interface AuditOptions {
  action: string;
  targetType?: string;
  targetId?: string;
  meta?: Record<string, unknown>;
}

/**
 * 记录审计日志（桥接 Logger 接口）
 * 在需要的地方调用 await recordLog(c, {...})
 */
export async function recordLog(c: Context, opts: AuditOptions) {
  // 获取注入的 logger，若无则默认使用 D1Logger
  const logger: Logger =
    (c.get('logger') as Logger | undefined) ?? new D1Logger(c);

  await logger.info(opts.action, {
    targetType: opts.targetType,
    targetId: opts.targetId,
    ...opts.meta,
  });
}
