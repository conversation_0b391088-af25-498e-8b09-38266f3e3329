/**
 * 搜索表单组件示例
 * 基于后端文档包的常用示例
 */

'use client';

import React, { useState } from 'react';
import { useSearch, useSearchSuggestions } from '@/hooks/useSearch';

interface SearchFormProps {
  onResults?: (results: any[]) => void;
  placeholder?: string;
  showFilters?: boolean;
}

export function SearchForm({ 
  onResults, 
  placeholder = '搜索事件、社团...',
  showFilters = true 
}: SearchFormProps) {
  const [query, setQuery] = useState('');
  const [type, setType] = useState<'events' | 'circles' | 'all'>('all');
  const [showSuggestions, setShowSuggestions] = useState(false);
  
  const { data: searchResults, isLoading: isSearching } = useSearch({
    q: query,
    type,
  });
  
  const { data: suggestions } = useSearchSuggestions(query);
  
  React.useEffect(() => {
    if (searchResults?.results) {
      onResults?.(searchResults.results);
    }
  }, [searchResults, onResults]);
  
  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    setShowSuggestions(false);
  };
  
  const handleSuggestionClick = (suggestion: string) => {
    setQuery(suggestion);
    setShowSuggestions(false);
  };
  
  return (
    <div className="relative">
      <form onSubmit={handleSubmit} className="flex gap-2">
        <div className="flex-1 relative">
          <input
            type="text"
            value={query}
            onChange={(e) => {
              setQuery(e.target.value);
              setShowSuggestions(true);
            }}
            onFocus={() => setShowSuggestions(true)}
            onBlur={() => setTimeout(() => setShowSuggestions(false), 200)}
            placeholder={placeholder}
            className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
          />
          
          {/* 搜索建议 */}
          {showSuggestions && suggestions && suggestions.length > 0 && (
            <div className="absolute top-full left-0 right-0 bg-white border border-gray-200 rounded-lg shadow-lg z-10 mt-1">
              {suggestions.map((suggestion, index) => (
                <button
                  key={index}
                  type="button"
                  onClick={() => handleSuggestionClick(suggestion)}
                  className="w-full text-left px-4 py-2 hover:bg-gray-100 first:rounded-t-lg last:rounded-b-lg"
                >
                  {suggestion}
                </button>
              ))}
            </div>
          )}
        </div>
        
        {showFilters && (
          <select
            value={type}
            onChange={(e) => setType(e.target.value as any)}
            className="px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
          >
            <option value="all">全部</option>
            <option value="events">事件</option>
            <option value="circles">社团</option>
          </select>
        )}
        
        <button
          type="submit"
          disabled={isSearching || !query.trim()}
          className="px-6 py-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600 disabled:opacity-50 disabled:cursor-not-allowed flex items-center"
        >
          {isSearching ? (
            <>
              <svg className="animate-spin -ml-1 mr-2 h-4 w-4 text-white" fill="none" viewBox="0 0 24 24">
                <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
              </svg>
              搜索中...
            </>
          ) : (
            '搜索'
          )}
        </button>
      </form>
      
      {/* 搜索结果统计 */}
      {searchResults && (
        <div className="mt-2 text-sm text-gray-500">
          找到 {searchResults.total} 个结果
        </div>
      )}
    </div>
  );
}
