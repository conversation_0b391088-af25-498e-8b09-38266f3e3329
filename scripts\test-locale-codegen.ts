#!/usr/bin/env tsx
/**
 * 测试语言支持代码生成保护机制
 *
 * 验证代码生成后处理脚本是否正常工作
 */

import { execSync } from 'child_process';
import fs from 'fs';
import path from 'path';

const CONTEXT_FILE = path.join(process.cwd(), 'src/api/generated/ayafeedContext.ts');

/**
 * 备份当前的 context 文件
 */
function backupContextFile(): string {
  const backupPath = `${CONTEXT_FILE}.backup`;
  if (fs.existsSync(CONTEXT_FILE)) {
    fs.copyFileSync(CONTEXT_FILE, backupPath);
    console.log(`📋 Backed up context file to: ${backupPath}`);
  }
  return backupPath;
}

/**
 * 恢复备份的 context 文件
 */
function restoreContextFile(backupPath: string) {
  if (fs.existsSync(backupPath)) {
    fs.copyFileSync(backupPath, CONTEXT_FILE);
    fs.unlinkSync(backupPath);
    console.log('🔄 Restored context file from backup');
  }
}

/**
 * 检查文件是否包含语言支持
 */
function checkLocaleSupport(): boolean {
  if (!fs.existsSync(CONTEXT_FILE)) {
    console.error('❌ Context file not found');
    return false;
  }

  const content = fs.readFileSync(CONTEXT_FILE, 'utf8');
  
  const hasImport = content.includes('getCurrentLocale');
  const hasLocaleLogic = content.includes('locale: currentLocale');
  
  console.log('🔍 Checking locale support:');
  console.log(`  - getCurrentLocale import: ${hasImport ? '✅' : '❌'}`);
  console.log(`  - locale logic: ${hasLocaleLogic ? '✅' : '❌'}`);
  
  return hasImport && hasLocaleLogic;
}

/**
 * 测试原始代码生成（不带后处理）
 */
function testRawGeneration(): boolean {
  console.log('\n🧪 Testing raw code generation...');
  
  try {
    execSync('pnpm gen:rq:raw', { stdio: 'pipe' });
    console.log('✅ Raw generation completed');
    
    const hasLocaleSupport = checkLocaleSupport();
    if (!hasLocaleSupport) {
      console.log('✅ Confirmed: raw generation does not include locale support');
      return true;
    } else {
      console.log('⚠️ Unexpected: raw generation already includes locale support');
      return false;
    }
  } catch (error) {
    console.error('❌ Raw generation failed:', error);
    return false;
  }
}

/**
 * 测试带后处理的代码生成
 */
function testPostProcessedGeneration(): boolean {
  console.log('\n🧪 Testing post-processed code generation...');
  
  try {
    execSync('pnpm gen:rq', { stdio: 'pipe' });
    console.log('✅ Post-processed generation completed');
    
    const hasLocaleSupport = checkLocaleSupport();
    if (hasLocaleSupport) {
      console.log('✅ Confirmed: post-processed generation includes locale support');
      return true;
    } else {
      console.log('❌ Failed: post-processed generation missing locale support');
      return false;
    }
  } catch (error) {
    console.error('❌ Post-processed generation failed:', error);
    return false;
  }
}

/**
 * 主测试函数
 */
function main() {
  console.log('🚀 Starting locale codegen protection testing...\n');
  
  // 备份当前文件
  const backupPath = backupContextFile();
  
  let allTestsPassed = true;
  
  try {
    // 测试1: 原始代码生成
    const rawTest = testRawGeneration();
    allTestsPassed = allTestsPassed && rawTest;
    
    // 测试2: 带后处理的代码生成
    const postProcessedTest = testPostProcessedGeneration();
    allTestsPassed = allTestsPassed && postProcessedTest;
    
    // 输出结果
    console.log('\n📊 Test Results:');
    console.log(`  - Raw generation test: ${rawTest ? '✅ PASS' : '❌ FAIL'}`);
    console.log(`  - Post-processed generation test: ${postProcessedTest ? '✅ PASS' : '❌ FAIL'}`);
    console.log(`  - Overall: ${allTestsPassed ? '✅ ALL TESTS PASSED' : '❌ SOME TESTS FAILED'}`);
    
    if (allTestsPassed) {
      console.log('\n🎉 Locale codegen protection is working correctly!');
      console.log('You can now safely regenerate API code without losing locale support.');
    } else {
      console.log('\n❌ Locale codegen protection needs debugging.');
      console.log('Please check the patch-locale-support script and try again.');
    }
    
  } finally {
    // 恢复备份
    restoreContextFile(backupPath);
  }
  
  process.exit(allTestsPassed ? 0 : 1);
}

// 运行测试
if (require.main === module) {
  main();
}
