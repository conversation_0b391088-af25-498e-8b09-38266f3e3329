import * as z from 'zod'

/**
 * EventSchema
 * --------------------------------------------------
 * 后端「展会（Event）」数据结构运行时校验。
 * 在以下场景使用：
 *   1. GET /events/{id}
 *   2. React Query Hook useEventDetail
 *
 * 字段说明：
 *   - id            展会主键 ID
 *   - name          展会中文/日文名称
 *   - date          举办日期及时间（原始字符串）
 *   - date_sort     排序专用 8 位数字日期（yyyymmdd），无需 Date 转换
 *   - image_url     竖版海报 URL（可选）
 *   - venue_name    场馆名称（可选）
 *   - venue_address 场馆地址（可选）
 *   - venue_lat     场馆纬度（可选）
 *   - venue_lng     场馆经度（可选）
 *   - url           官方网站
 *   - description   补充简介，可为空
 *   - created_at    创建时间
 *   - updated_at    更新时间
 *
 * ⚠️  任何字段变动请同步更新此注释及 Schema。
 */
export const EventSchema = z.object({
  id: z.string(),
  name: z.string(),
  date: z.string(),
  date_sort: z.number(),
  image_url: z.string().optional(),

  // 场馆信息
  venue_name: z.string().optional(),
  venue_address: z.string().optional(),
  venue_lat: z.number().optional(),
  venue_lng: z.number().optional(),

  url: z.string().url().optional(),
  description: z.string().optional(),
  created_at: z.coerce.date().optional(),
  updated_at: z.coerce.date().optional(),
})

export type Event = z.infer<typeof EventSchema>

export const EventInputSchema = z.object({
  id: z.string().optional(),
  name: z.string().min(1, "名称必填"),
  date: z.string().min(1, "日期必填"),
  image_url: z.preprocess(
    (v) => (typeof v === "string" && v.trim() === "" ? undefined : v),
    z.string().url("需要合法 URL").optional()
  ),

  // 场馆信息
  venue_name: z.string().optional(),
  venue_address: z.string().optional(),
  venue_lat: z.preprocess(
    (v) => {
      if (v === "" || v === null || v === undefined) return undefined;
      if (typeof v === "number" && Number.isNaN(v)) return undefined;
      if (typeof v === "string" && v.trim() === "") return undefined;
      return v;
    },
    z.number().optional()
  ),
  venue_lng: z.preprocess(
    (v) => {
      if (v === "" || v === null || v === undefined) return undefined;
      if (typeof v === "number" && Number.isNaN(v)) return undefined;
      if (typeof v === "string" && v.trim() === "") return undefined;
      return v;
    },
    z.number().optional()
  ),

  url: z.preprocess(
    (v) => (typeof v === "string" && v.trim() === "" ? undefined : v),
    z.string().url("需要合法 URL").optional()
  ),
  description: z.string().optional(),
})

export type EventInput = z.infer<typeof EventInputSchema>

/**
 * 多语言事件输入 Schema
 * 支持中文、日文、英文三种语言的所有字段
 * 用于后台管理页面的创建和编辑功能
 */
export const MultilingualEventInputSchema = z.object({
  id: z.string().optional(),

  // 名称 - 三种语言
  name_en: z.string().min(1, "英文名称必填"),
  name_ja: z.string().min(1, "日文名称必填"),
  name_zh: z.string().min(1, "中文名称必填"),

  // 日期 - 三种语言
  date_en: z.string().min(1, "英文日期必填"),
  date_ja: z.string().min(1, "日文日期必填"),
  date_zh: z.string().min(1, "中文日期必填"),

  // 排序日期（自动生成）
  date_sort: z.number().optional(),

  // 图片 URL
  image_url: z.preprocess(
    (v) => (typeof v === "string" && v.trim() === "" ? undefined : v),
    z.string().optional()
  ),

  // 场馆 ID（必填）
  venue_id: z.string().min(1, "请选择展会场馆"),

  // 官网 URL
  url: z.preprocess(
    (v) => (typeof v === "string" && v.trim() === "" ? undefined : v),
    z.string().url("需要合法 URL").optional()
  ),
})

export type MultilingualEventInput = z.infer<typeof MultilingualEventInputSchema>