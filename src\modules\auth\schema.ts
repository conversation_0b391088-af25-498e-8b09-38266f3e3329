import { z } from '@hono/zod-openapi';

// 认证凭证 Schema（AuthCredentials）
export const authSchema = z
  .object({
    username: z
      .string()
      .min(3, 'Username must be at least 3 characters long')
      .openapi({ example: 'alice' }),
    password: z
      .string()
      .min(6, 'Password must be at least 6 characters long')
      .openapi({ example: 'secret123' }),
  })
  .openapi('AuthCredentials');

export type AuthCredentials = z.infer<typeof authSchema>;
