"use client";

import Link from "next/link";
import { useRouter } from "next/navigation";
import { useEffect, useState } from "react";
import { ShieldX, ArrowLeft, Home } from "lucide-react";

import { RadixButton } from "@/components/ui/radix-components";

export default function ForbiddenPage() {
  const router = useRouter();
  const [countdown, setCountdown] = useState(10);

  useEffect(() => {
    // 倒计时逻辑
    const timer = setInterval(() => {
      setCountdown((prev) => {
        if (prev <= 1) {
          // 倒计时结束，跳转回首页
          router.push("/");
          return 0;
        }
        return prev - 1;
      });
    }, 1000);

    return () => clearInterval(timer);
  }, [router]);

  const handleGoBack = () => {
    // 尝试返回上一页，如果没有历史记录则跳转到首页
    if (window.history.length > 1) {
      router.back();
    } else {
      router.push("/");
    }
  };

  return (
    <div className="min-h-screen flex flex-col items-center justify-center gap-8 px-4">
      <div className="text-center space-y-4">
        <div className="flex justify-center">
          <ShieldX className="h-24 w-24 text-destructive" />
        </div>

        <h1 className="text-4xl font-bold text-foreground">访问被拒绝</h1>

        <div className="space-y-2">
          <p className="text-lg text-muted-foreground">
            抱歉，您没有权限访问此页面
          </p>
          <p className="text-sm text-muted-foreground">
            此页面需要管理员或编辑者权限才能访问
          </p>
        </div>

        <div className="bg-muted/50 rounded-lg p-4 border">
          <p className="text-sm text-muted-foreground">
            {countdown > 0 ? (
              <>将在 <span className="font-mono font-bold text-primary">{countdown}</span> 秒后自动返回首页</>
            ) : (
              "正在跳转..."
            )}
          </p>
        </div>
      </div>

      <div className="flex gap-4">
        <RadixButton
          onClick={handleGoBack}
          variant="outline"
          className="flex items-center gap-2"
        >
          <ArrowLeft className="h-4 w-4" />
          返回上一页
        </RadixButton>

        <Link href="/">
          <RadixButton className="flex items-center gap-2">
            <Home className="h-4 w-4" />
            返回首页
          </RadixButton>
        </Link>
      </div>
    </div>
  );
}