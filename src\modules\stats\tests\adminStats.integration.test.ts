import { describe, it, expect } from 'vitest';

import app from '@/app';

type D1Database = any;

function createMockDB(role: 'admin' | 'viewer' = 'admin'): D1Database {
  const totals = { circles: 3, artists: 5, events: 8 };
  const monthlyRows = [{ month: '01', count: 2 }];

  return {
    prepare: (query: string) => {
      const upper = query.toUpperCase();

      const buildResponse = () => ({
        bind: (..._args: any[]) => buildResponse(),
        first: async () => {
          if (upper.includes('COUNT(*)') && upper.includes('FROM CIRCLES'))
            return { total: totals.circles };
          if (upper.includes('COUNT(*)') && upper.includes('FROM ARTISTS'))
            return { total: totals.artists };
          if (
            upper.includes('COUNT(*)') &&
            upper.includes('FROM EVENTS') &&
            !upper.includes('GROUP BY')
          )
            return { total: totals.events };
          if (upper.includes('FROM AUTH_SESSION'))
            return { id: 'u1', username: 'tester', role };
          if (upper.includes('DELETE FROM AUTH_SESSION'))
            return { success: true };
          return null;
        },
        all: async () => {
          if (upper.includes('FROM EVENTS') && upper.includes('GROUP BY')) {
            return { results: monthlyRows };
          }
          if (upper.includes('FROM AUTH_SESSION')) {
            return { results: [{ id: 'u1', username: 'tester', role }] };
          }
          return { results: [] };
        },
        run: async () => ({ success: true }),
      });

      return buildResponse();
    },
  };
}

// @ts-ignore
const Request = globalThis.Request;

describe('/admin/stats route', () => {
  it('should return stats data', async () => {
    const req = new Request('http://localhost/admin/stats?year=2025', {
      headers: {
        Cookie: 'auth_session=admin_session',
      },
    });

    const res = await app.fetch(req, { DB: createMockDB() });
    expect(res.status).toBe(200);
    const json = (await res.json()) as any;
    expect(json.year).toBe(2025);
    expect(json.totals.circles).toBe(3);
  });
});
