/* eslint-disable @next/next/no-img-element */
import { expect, test, vi } from "vitest";

import CirclesGrid from "@/components/events/CirclesGrid";
import { renderWithProviders, screen } from "@test/test-utils";

// Mock react-virtuoso VirtuosoGrid to render static list
vi.mock("react-virtuoso", () => {
  return {
    __esModule: true,
    VirtuosoGrid: ({ data, itemContent }: any) => (
      <div data-testid="virtuoso-grid">
        {data.map((item: any, idx: number) => itemContent(idx, item))}
      </div>
    ),
  };
});

// Mock next/image
vi.mock("next/image", () => ({
  default: (props: any) => {
    // eslint-disable-next-line jsx-a11y/alt-text
    return <img {...props} />;
  },
}));

const sampleCircles = [
  {
    id: "c1",
    circle_name: "天空之城",
    booth_id: "<PERSON>01<PERSON>",
    artist_name: "<PERSON>",
    circle_urls: "{}",
    category: "comic",
  },
  {
    id: "c2",
    circle_name: "幻想乡",
    booth_id: "B12b",
    artist_name: "<PERSON>",
    circle_urls: "{}",
    category: "game",
  },
] as any;

test("renders circles in grid", () => {
  renderWithProviders(<CirclesGrid data={sampleCircles} />);

  expect(screen.getByTestId("virtuoso-grid")).toBeInTheDocument();
  expect(screen.getByText("天空之城")).toBeInTheDocument();
  expect(screen.getByText("幻想乡")).toBeInTheDocument();
}); 