'use client'

import { useLocale } from 'next-intl';
import { getEventsQuery, useGetEvents } from '@/api/generated/ayafeedComponents';
import { useAyafeedContext } from '@/api/generated/ayafeedContext';
import { deepMerge } from '@/api/generated/ayafeedUtils';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { useState } from 'react';

/**
 * 测试语言参数是否正确添加到查询键
 */
export default function TestLocalePage() {
  const locale = useLocale();
  const [showApiTest, setShowApiTest] = useState(false);

  // 测试 useAyafeedContext 返回的内容
  const context = useAyafeedContext();

  // 测试查询键生成
  const variables = { queryParams: { page: '1', pageSize: '5' } };
  const queryInfo = getEventsQuery(variables);

  // 测试合并后的查询键
  const mergedVariables = deepMerge(context.fetcherOptions, variables);
  const mergedQueryInfo = getEventsQuery(mergedVariables);

  // 实际的 API 调用测试
  const apiTest = useGetEvents(
    { queryParams: { page: '1', pageSize: '3' } },
    {
      enabled: showApiTest,
      staleTime: 60 * 1000
    }
  );

  return (
    <div className="container mx-auto p-6 space-y-6">
      <Card>
        <CardHeader>
          <CardTitle>语言参数测试</CardTitle>
          <p className="text-sm text-muted-foreground">
            当前语言: <strong>{locale}</strong>
          </p>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="flex gap-2 mb-4">
            <Button
              onClick={() => setShowApiTest(!showApiTest)}
              variant={showApiTest ? "default" : "outline"}
            >
              {showApiTest ? "停止API测试" : "开始API测试"}
            </Button>
          </div>

          <div>
            <h3 className="font-semibold mb-2">useAyafeedContext 返回值:</h3>
            <pre className="bg-muted p-3 rounded text-sm overflow-x-auto">
              {JSON.stringify(context, null, 2)}
            </pre>
          </div>

          <div>
            <h3 className="font-semibold mb-2">原始查询变量:</h3>
            <pre className="bg-muted p-3 rounded text-sm overflow-x-auto">
              {JSON.stringify(variables, null, 2)}
            </pre>
          </div>

          <div>
            <h3 className="font-semibold mb-2">合并后的查询变量:</h3>
            <pre className="bg-muted p-3 rounded text-sm overflow-x-auto">
              {JSON.stringify(mergedVariables, null, 2)}
            </pre>
          </div>

          <div>
            <h3 className="font-semibold mb-2">原始查询键:</h3>
            <pre className="bg-muted p-3 rounded text-sm overflow-x-auto">
              {JSON.stringify(queryInfo.queryKey, null, 2)}
            </pre>
          </div>

          <div>
            <h3 className="font-semibold mb-2">合并后的查询键:</h3>
            <pre className="bg-muted p-3 rounded text-sm overflow-x-auto">
              {JSON.stringify(mergedQueryInfo.queryKey, null, 2)}
            </pre>
          </div>

          {showApiTest && (
            <div>
              <h3 className="font-semibold mb-2">实际API调用结果:</h3>
              <div className="space-y-2">
                <p><strong>查询键:</strong></p>
                <pre className="bg-muted p-3 rounded text-sm overflow-x-auto">
                  {JSON.stringify(apiTest.queryKey, null, 2)}
                </pre>
                <p><strong>加载状态:</strong> {apiTest.isLoading ? '加载中...' : '已完成'}</p>
                <p><strong>错误:</strong> {apiTest.error ? String(apiTest.error) : '无'}</p>
                <p><strong>数据条数:</strong> {apiTest.data?.items?.length || 0}</p>
                {apiTest.data?.items?.[0] && (
                  <div>
                    <p><strong>第一个事件:</strong></p>
                    <pre className="bg-muted p-2 rounded text-xs overflow-x-auto">
                      {JSON.stringify(apiTest.data.items[0], null, 2)}
                    </pre>
                  </div>
                )}
              </div>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
}
