/**
 * 多语言数据处理工具
 * 基于后端文档包的多语言集成规范
 */

import type { Locale } from '@/services/i18n';

// 多语言文本对象类型
export interface MultilingualText {
  zh?: string;
  ja?: string;
  en?: string;
}

// 从API响应中提取本地化文本
export function extractLocalizedText(
  textObj: MultilingualText | string,
  locale: Locale,
  fallback: string = ''
): string {
  if (typeof textObj === 'string') {
    return textObj;
  }
  
  if (!textObj) {
    return fallback;
  }
  
  // 按优先级返回文本
  return textObj[locale] || 
         textObj['zh'] || 
         textObj['en'] || 
         textObj['ja'] || 
         fallback;
}

// 处理API响应中的多语言字段
export function processMultilingualResponse<T extends Record<string, any>>(
  data: T,
  locale: Locale,
  multilingualFields: (keyof T)[]
): T {
  const processed = { ...data };
  
  multilingualFields.forEach(field => {
    if (processed[field] && typeof processed[field] === 'object') {
      processed[field] = extractLocalizedText(
        processed[field] as MultilingualText,
        locale,
        ''
      );
    }
  });
  
  return processed;
}

// 创建包含语言信息的查询键（用于React Query）
export function createLocaleQueryKey(baseKey: unknown[], locale: Locale): unknown[] {
  return [...baseKey, { locale }];
}
