import { describe, it, expect, vi, beforeEach } from 'vitest'
import { renderWithProviders, screen } from '@test/test-utils'
import LanguageSwitcher from '../language-switcher'

// Mock next-intl
vi.mock('next-intl', () => ({
  useLocale: vi.fn(() => 'ja'),
  NextIntlClientProvider: ({ children }: { children: React.ReactNode }) => children,
}))

// Mock next/navigation
vi.mock('next/navigation', () => ({
  useRouter: vi.fn(() => ({
    refresh: vi.fn(),
  })),
}))

// Mock the locale utils
vi.mock('@/lib/locale-utils', () => ({
  setLocaleToCookie: vi.fn(),
  SUPPORTED_LOCALES: ['ja', 'en', 'zh'],
}))

describe('LanguageSwitcher', () => {
  beforeEach(() => {
    vi.clearAllMocks()
  })

  it('renders language switcher button', () => {
    renderWithProviders(<LanguageSwitcher />)

    const button = screen.getByRole('button')
    expect(button).toBeInTheDocument()

    // Should have a globe icon
    const globeIcon = button.querySelector('svg')
    expect(globeIcon).toBeInTheDocument()
  })

  it('uses correct locale from next-intl', () => {
    renderWithProviders(<LanguageSwitcher />)

    // The component should render without errors when using next-intl's useLocale
    const button = screen.getByRole('button')
    expect(button).toBeInTheDocument()
  })

  it('has proper accessibility attributes', () => {
    renderWithProviders(<LanguageSwitcher />)

    const button = screen.getByRole('button')
    expect(button).toHaveAttribute('aria-haspopup', 'menu')
    expect(button).toHaveAttribute('aria-expanded', 'false')
  })
})
