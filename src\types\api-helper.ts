/**
 * API 请求/响应 辅助类型工具
 * ---------------------------------------
 * 依赖 openapi-typescript 生成的 `paths` 类型，
 * 通过索引 Path + Method (+ Status) 快速获得
 * Request 与 Response 的 JSON 数据类型，避免业务侧
 * 手动书写冗余接口类型定义。
 *
 * 示例：
 * ```ts
 * import type { Req, Res } from "@/types/api-helper"
 *
 * // POST /auth/register 请求体
 * type RegisterBody = Req<"/auth/register", "post">;
 *
 * // GET /events 200 响应体
 * type EventsResponse = Res<"/events", "get">;
 *
 * // DELETE /admin/users/{id} 200 响应体
 * type DeleteUserResult = Res<"/admin/users/{id}", "delete", 200>;
 * ```
 */

import type { paths } from "./api-types";

/**
 * 统一抽取 `application/json` 对应的类型
 */
export type JsonContent<T> = T extends {
  content: {
    "application/json": infer R;
  };
}
  ? R
  : never;

/** 提取指定 Path 可用的 HTTP 方法 */
export type MethodOf<P extends keyof paths> = Exclude<keyof paths[P], "parameters">;

/**
 * Request Body 辅助类型
 * @typeParam P 路径，如 "/auth/register"
 * @typeParam M 方法，默认 "post" | "get" 等
 */
export type Req<
  P extends keyof paths,
  M extends MethodOf<P>
> = paths[P][M] extends { requestBody?: infer RB }
  ? JsonContent<RB>
  : never;

/**
 * Response 辅助类型
 * @typeParam P 路径，如 "/events"
 * @typeParam M 方法，默认 "get" 等
 * @typeParam S HTTP Status Code，默认 200
 */
export type Res<
  P extends keyof paths,
  M extends MethodOf<P>,
  S extends number | string = 200
> = paths[P][M] extends { responses: Record<string | number, unknown> }
  ? S extends keyof paths[P][M]["responses"]
    ? JsonContent<paths[P][M]["responses"][S]>
    : never
  : never; 