import { <PERSON>, CardContent, CardHeader } from "@/components/ui/card";
import { Skeleton } from "@/components/ui/skeleton";

export default function EventCardSkeleton() {
  return (
    <Card className="overflow-hidden">
      <CardHeader className="p-0">
        {/* 图片区域骨架 */}
        <Skeleton className="w-full h-48" />
      </CardHeader>
      <CardContent className="p-4 space-y-2">
        {/* 标题 */}
        <Skeleton className="h-5 w-3/4" />
        {/* 日期+会场 */}
        <Skeleton className="h-4 w-1/2" />
      </CardContent>
    </Card>
  );
} 