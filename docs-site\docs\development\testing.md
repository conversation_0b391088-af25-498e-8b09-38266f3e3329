﻿# 测试指南（Testing Guide）

Ayafeed API 使用 **Vitest** 进行单元与集成测试。

## 测试类型

| 目录                  | 目的                            |
| --------------------- | ------------------------------- |
| `tests/unit`          | 纯函数、Service、Util           |
| `tests/integration`   | 路由 + DB（使用 D1 本地数据库） |
| `src/modules/*/tests` | 模块特定测试                    |

## 运行

```bash
pnpm test                # 全量
pnpm test src/modules    # 指定路径
pnpm test --watch        # watch mode
```

## Mock 策略

- **Logger / Cache**：注入内存实现。
- **第三方请求**：使用 `vi.stubGlobal(fetch)`。

## 数据隔离

- 每个测试文件自动迁移并回滚事务，确保数据独立。

## Coverage

```bash
vitest run --coverage
```

阈值配置见 `vitest.config.ts`。
