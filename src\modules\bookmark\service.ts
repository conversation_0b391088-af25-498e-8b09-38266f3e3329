import type { D1Database } from '@cloudflare/workers-types';

import { createBookmarkRepository } from './repository';

/**
 * 切换收藏状态：若存在则删除（返回 false），否则插入（返回 true）。
 * 返回最新 isBookmarked 状态。
 */
export async function toggleBookmark(
  db: D1Database,
  userId: string,
  circleId: string
): Promise<{ isBookmarked: boolean }> {
  const repo = createBookmarkRepository(db);
  const isBookmarked = await repo.toggle(userId, circleId);
  return { isBookmarked };
}
