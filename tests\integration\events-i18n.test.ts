import { describe, it, expect, vi } from 'vitest';
import app from '../../src/app';

// Mock venue service
vi.mock('@/modules/venue/service', () => ({
  getVenueById: vi.fn(async (db, venueId, locale) => {
    if (venueId === 'tokyo-big-sight') {
      return {
        id: 'tokyo-big-sight',
        name:
          locale === 'ja'
            ? '東京ビッグサイト'
            : locale === 'zh'
              ? '东京 Big Sight'
              : 'Tokyo Big Sight',
        address:
          locale === 'ja'
            ? '東京都江東区有明3-11-1'
            : locale === 'zh'
              ? '东京都江东区有明3-11-1'
              : '3-11-1 Ariake, Koto City, Tokyo',
        lat: 35.6298,
        lng: 139.793,
        capacity: 35000,
        website_url: 'https://www.bigsight.jp/',
        phone: '+81-3-5530-1111',
        description:
          locale === 'ja'
            ? '東京ビッグサイトは日本最大の展示場です'
            : locale === 'zh'
              ? '东京 Big Sight 是日本最大的展览中心'
              : "Tokyo Big Sight is Japan's largest convention center",
        facilities: '{"wifi": true, "parking": true, "restaurant": true}',
        transportation:
          '{"train": "Yurikamome Line", "station": "Kokusai-tenjijo-seimon"}',
        parking_info: '{"spaces": 5000, "fee": "¥1000/day"}',
      };
    }
    return null;
  }),
}));

// 类型定义
interface EventsResponse {
  items: Array<{
    id: string;
    name: string;
    venue_name: string;
    [key: string]: any;
  }>;
  total: number;
  page: number;
  pageSize: number;
}

interface EventResponse {
  id: string;
  name: string;
  venue_name: string;
  [key: string]: any;
}

// 构造 mock D1 数据库
const mockDB = {
  prepare: (query: string) => {
    const upper = query.toUpperCase();
    const buildResp = () => ({
      all: async () => {
        if (upper.includes('FROM EVENTS')) {
          return {
            results: [
              {
                id: 'event-1',
                name_en: 'Test Event',
                name_ja: 'テストイベント',
                name_zh: '测试活动',
                date_en: 'May 3, 2025 (Sat) 10:30 – 15:30',
                date_ja: '2025年5月3日(土・祝) 10:30 – 15:30',
                date_zh: '2025年5月3日(周六) 10:30 – 15:30',
                date_sort: 20250503,
                image_url: null,
                venue_id: 'tokyo-big-sight',
                description: null,
                url: null,
                created_at: '2025-01-01T00:00:00Z',
                updated_at: '2025-01-01T00:00:00Z',
              },
            ],
          };
        }
        return { results: [] };
      },
      first: async () => {
        if (upper.includes('COUNT(*)')) return { total: 1 };
        if (upper.includes('FROM EVENTS')) {
          return {
            id: 'event-1',
            name_en: 'Test Event',
            name_ja: 'テストイベント',
            name_zh: '测试活动',
            date_en: 'May 3, 2025 (Sat) 10:30 – 15:30',
            date_ja: '2025年5月3日(土・祝) 10:30 – 15:30',
            date_zh: '2025年5月3日(周六) 10:30 – 15:30',
            date_sort: 20250503,
            image_url: null,
            venue_name_en: 'Tokyo Big Sight',
            venue_name_ja: '東京ビッグサイト',
            venue_name_zh: '东京 Big Sight',
            venue_address_en: null,
            venue_address_ja: null,
            venue_address_zh: null,
            venue_lat: 35.6298,
            venue_lng: 139.793,
            description: null,
            url: null,
            created_at: '2025-01-01T00:00:00Z',
            updated_at: '2025-01-01T00:00:00Z',
          };
        }
        return null;
      },
      run: async () => ({ success: true }),
      bind: (..._args: any[]) => buildResp(),
    });
    return buildResp();
  },
};

// @ts-ignore
const Request = globalThis.Request;

function withEnv(url: string, init?: RequestInit) {
  const base = url.startsWith('http') ? url : `http://localhost${url}`;
  return app.fetch(new Request(base, init), { DB: mockDB });
}

describe('Events API 国际化测试', () => {
  it('应该根据 Accept-Language 头返回中文内容', async () => {
    const res = await withEnv('/events', {
      headers: {
        'accept-language': 'zh-CN,zh;q=0.9,en;q=0.8',
      },
    });

    expect(res.status).toBe(200);
    expect(res.headers.get('content-language')).toBe('zh');
    expect(res.headers.get('set-cookie')).toMatch(/locale=zh/);

    const data = (await res.json()) as EventsResponse;
    // 验证返回的数据结构包含中文字段
    if (data.items && data.items.length > 0) {
      const firstEvent = data.items[0];
      expect(firstEvent).toHaveProperty('name');
      expect(firstEvent).toHaveProperty('venue_name');
      // 确保没有返回多语言的原始字段
      expect(firstEvent).not.toHaveProperty('name_en');
      expect(firstEvent).not.toHaveProperty('name_ja');
      expect(firstEvent).not.toHaveProperty('name_zh');
    }
  });

  it('应该根据 Cookie 优先返回日语内容', async () => {
    const res = await withEnv('/events', {
      headers: {
        'accept-language': 'zh-CN,zh;q=0.9,en;q=0.8',
        cookie: 'locale=ja',
      },
    });

    expect(res.status).toBe(200);
    expect(res.headers.get('content-language')).toBe('ja');
    // Cookie 已存在且正确，不应该设置新的 Cookie
    expect(res.headers.get('set-cookie')).toBeNull();
  });

  it('应该在无语言偏好时默认返回英语内容', async () => {
    const res = await withEnv('/events');

    expect(res.status).toBe(200);
    expect(res.headers.get('content-language')).toBe('en');
    expect(res.headers.get('set-cookie')).toMatch(/locale=en/);
  });

  it('events/{id} 路由应该正确处理语言切换', async () => {
    // 首先获取一个事件ID（如果有的话）
    const listRes = await withEnv('/events');
    const listData = (await listRes.json()) as EventsResponse;

    if (listData.items && listData.items.length > 0) {
      const eventId = listData.items[0].id;

      // 测试中文请求
      const zhRes = await withEnv(`/events/${eventId}`, {
        headers: {
          'accept-language': 'zh-CN,zh;q=0.9,en;q=0.8',
        },
      });

      expect(zhRes.status).toBe(200);
      expect(zhRes.headers.get('content-language')).toBe('zh');

      const zhData = (await zhRes.json()) as EventResponse;
      expect(zhData).toHaveProperty('name');
      expect(zhData).toHaveProperty('venue');
      expect(zhData.venue).toHaveProperty('name');
      expect(zhData).not.toHaveProperty('name_en');
      expect(zhData).not.toHaveProperty('name_ja');
      expect(zhData).not.toHaveProperty('name_zh');

      // 测试日语请求
      const jaRes = await withEnv(`/events/${eventId}`, {
        headers: {
          cookie: 'locale=ja',
        },
      });

      expect(jaRes.status).toBe(200);
      expect(jaRes.headers.get('content-language')).toBe('ja');
    }
  });

  it('应该正确处理不支持的语言并降级到英语', async () => {
    const res = await withEnv('/events', {
      headers: {
        'accept-language': 'fr-FR,fr;q=0.9,de;q=0.8',
      },
    });

    expect(res.status).toBe(200);
    expect(res.headers.get('content-language')).toBe('en');
    expect(res.headers.get('set-cookie')).toMatch(/locale=en/);
  });

  it('应该正确设置 Vary 头以支持缓存', async () => {
    const res = await withEnv('/events', {
      headers: {
        'accept-language': 'zh-CN,zh;q=0.9,en;q=0.8',
      },
    });

    expect(res.status).toBe(200);
    expect(res.headers.get('vary')).toBe('Accept-Language, X-Locale');
  });
});
