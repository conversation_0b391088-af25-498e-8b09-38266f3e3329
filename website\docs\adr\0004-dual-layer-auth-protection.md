# ADR-0004: 双重权限保护机制

## 状态

已接受

## 背景

在实现后台管理系统的权限控制时，我们遇到了以下问题：

1. **单一保护层不足**：仅依赖中间件或客户端组件都存在安全风险
2. **用户体验问题**：未登录用户访问受保护页面时出现空白页面
3. **权限不足处理**：需要友好的错误提示和自动跳转机制
4. **登录重定向**：用户登录后应该返回原始访问页面

## 决策

我们决定实现**双重权限保护机制**，结合服务器端和客户端的优势：

### 1. 服务器端保护（中间件）
- **职责**：检查认证状态（token 存在性）
- **优势**：减少不必要的客户端渲染，提高性能
- **实现**：Next.js 中间件检查 `auth_token` cookie

### 2. 客户端保护（RoleGuard）
- **职责**：验证用户角色权限，处理用户体验
- **优势**：灵活的权限控制，友好的用户反馈
- **实现**：React 组件包装需要权限的页面

## 实现细节

### 中间件配置
```typescript
// middleware.ts
export function middleware(request: NextRequest) {
  if (pathname.startsWith("/admin")) {
    const authToken = request.cookies.get("auth_token");
    if (!authToken?.value) {
      const loginUrl = new URL("/login", request.url);
      loginUrl.searchParams.set("redirect", pathname);
      return NextResponse.redirect(loginUrl);
    }
  }
}

export const config = {
  matcher: ["/admin", "/admin/:path*"],
};
```

### RoleGuard 组件
```typescript
// src/components/RoleGuard.tsx
export default function RoleGuard({ allow, children }: RoleGuardProps) {
  const { user, isLoading } = useAuth();
  
  useEffect(() => {
    if (isLoading) return;
    
    if (!user) {
      // 重定向到登录页面
      router.replace(`/login?redirect=${currentPath}`);
    } else if (!allow.includes(user.role)) {
      // 重定向到403页面
      router.replace("/403");
    }
  }, [isLoading, user, allow]);
  
  // 渲染逻辑...
}
```

### 403 错误页面
- 友好的错误说明
- 10秒自动跳转倒计时
- 手动返回选项
- 现代化UI设计

## 后果

### 积极影响

1. **安全性提升**
   - 双重保护确保未授权访问被有效阻止
   - 服务器端验证防止客户端绕过

2. **用户体验改善**
   - 消除空白页面问题
   - 友好的错误提示和自动跳转
   - 保持用户访问意图（登录后返回原页面）

3. **开发体验优化**
   - 清晰的权限控制模式
   - 可复用的 RoleGuard 组件
   - 统一的错误处理机制

### 潜在风险

1. **复杂性增加**
   - 需要维护两套权限检查逻辑
   - 中间件和客户端组件的同步

2. **性能考虑**
   - 额外的客户端权限检查
   - 可能的重复重定向

### 缓解措施

1. **代码组织**
   - 将权限逻辑集中在专门的组件和服务中
   - 使用 TypeScript 确保类型安全

2. **测试覆盖**
   - 为 RoleGuard 组件编写单元测试
   - 测试各种权限场景

3. **文档维护**
   - 在认证文档中详细说明使用方法
   - 提供最佳实践指南

## 相关决策

- [ADR-0002: Cookie-based Authentication](./0002-cookie-based-auth.md)
- [ADR-0003: I18n Events](./0003-i18n-events.md)

## 参考资料

- [Next.js Middleware Documentation](https://nextjs.org/docs/app/building-your-application/routing/middleware)
- [React Router Auth Example](https://reactrouter.com/en/main/examples/auth)
- [OWASP Authentication Cheat Sheet](https://cheatsheetseries.owasp.org/cheatsheets/Authentication_Cheat_Sheet.html)
