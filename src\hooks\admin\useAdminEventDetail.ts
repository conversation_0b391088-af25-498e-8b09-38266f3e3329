import { useLocale } from "next-intl";
import { useMemo } from "react";

import {
  useGetAdminEventsId,
  type GetAdminEventsIdResponse
} from "@/api/generated/ayafeedComponents";
import type { AdminEvent } from "./useAdminEvents";
import type { MultilingualEventInput } from "@/schemas/event";
import { getLocalizedField } from "@/app/events/[id]/utils";

/**
 * 将 API 响应的事件详情数据转换为后台管理显示格式（用于列表显示）
 */
function transformAdminEventDetailData(
  apiEvent: GetAdminEventsIdResponse,
  locale: string
): AdminEvent {
  return {
    id: apiEvent.id,
    name: getLocalizedField({
      zh: apiEvent.name_zh,
      ja: apiEvent.name_ja,
      en: apiEvent.name_en
    }, locale) || apiEvent.name_en || '',
    date: getLocalizedField({
      zh: apiEvent.date_zh,
      ja: apiEvent.date_ja,
      en: apiEvent.date_en
    }, locale) || apiEvent.date_en || '',
    // 现在使用venue_id，venue名称需要通过venue API获取
    venue_name: `Venue ID: ${apiEvent.venue_id}`,
    venue_address: undefined, // 需要通过venue API获取
    url: apiEvent.url || undefined,
    image_url: apiEvent.image_url || undefined,
    created_at: apiEvent.created_at,
    updated_at: apiEvent.updated_at,
  };
}

/**
 * 将 API 响应的事件详情数据转换为多语言表单格式（用于编辑表单）
 */
function transformApiEventToMultilingualForm(
  apiEvent: GetAdminEventsIdResponse
): MultilingualEventInput {
  return {
    id: apiEvent.id,
    name_en: apiEvent.name_en,
    name_ja: apiEvent.name_ja,
    name_zh: apiEvent.name_zh,
    date_en: apiEvent.date_en,
    date_ja: apiEvent.date_ja,
    date_zh: apiEvent.date_zh,
    date_sort: apiEvent.date_sort,
    image_url: apiEvent.image_url || undefined,
    venue_id: apiEvent.venue_id || "",
    url: apiEvent.url || undefined,
  };
}

export function useAdminEventDetail(id: string | undefined) {
  const locale = useLocale();

  // 使用生成的 API hook
  const { data, isLoading, error, ...rest } = useGetAdminEventsId(
    id ? { pathParams: { id } } : { pathParams: { id: '' } },
    {
      enabled: !!id,
      staleTime: 1000 * 60 * 5,
    }
  );

  // 转换数据格式 - 提供两种格式，使用 useMemo 避免不必要的重新创建
  const transformedData = useMemo(() => {
    return data ? transformAdminEventDetailData(data, locale) : undefined;
  }, [data, locale]);

  const multilingualData = useMemo(() => {
    return data ? transformApiEventToMultilingualForm(data) : undefined;
  }, [data]);

  return {
    data: transformedData,           // 用于列表显示的本地化数据
    multilingualData,               // 用于编辑表单的多语言数据
    rawData: data,                  // 原始 API 数据
    isLoading,
    error,
    ...rest
  };
}