import { useLocale } from "next-intl";
import { useQuery } from "@tanstack/react-query";

import {
  useGetAdminEvents,
  type GetAdminEventsQueryParams
} from "@/api/generated/ayafeedComponents";
import type { PaginatedResult } from "@/api/generated/ayafeedSchemas";
import { queryKeys } from "@/constants/queryKeys";
import { getLocalizedField } from "@/app/events/[id]/utils";

// 后台管理显示的事件数据类型
export interface AdminEvent {
  id: string;
  name: string;
  date: string;
  venue_name: string;
  venue_address?: string;
  url?: string;
  image_url?: string;
  created_at?: string;
  updated_at?: string;
}

export interface AdminEventsParams {
  page?: string;
  pageSize?: string;
  keyword?: string;
  /** ISO YYYYMMDD 开始日期 */
  date_from?: string;
  /** ISO YYYYMMDD 结束日期 */
  date_to?: string;
}

/**
 * 将 API 响应的事件数据转换为后台管理显示格式
 * 根据当前语言选择合适的字段值
 */
function transformAdminEventData(
  apiEvent: PaginatedResult['items'][0],
  locale: string
): AdminEvent {
  return {
    id: apiEvent.id,
    name: getLocalizedField({
      zh: apiEvent.name_zh,
      ja: apiEvent.name_ja,
      en: apiEvent.name_en
    }, locale) || apiEvent.name_en || '',
    date: getLocalizedField({
      zh: apiEvent.date_zh,
      ja: apiEvent.date_ja,
      en: apiEvent.date_en
    }, locale) || apiEvent.date_en || '',
    venue_name: getLocalizedField({
      zh: apiEvent.venue_name_zh,
      ja: apiEvent.venue_name_ja,
      en: apiEvent.venue_name_en
    }, locale) || apiEvent.venue_name_en || '',
    venue_address: getLocalizedField({
      zh: apiEvent.venue_address_zh,
      ja: apiEvent.venue_address_ja,
      en: apiEvent.venue_address_en
    }, locale) || undefined,
    url: apiEvent.url || undefined,
    image_url: apiEvent.image_url || undefined,
    created_at: apiEvent.created_at,
    updated_at: apiEvent.updated_at,
  };
}

export function useAdminEvents(params?: AdminEventsParams) {
  const locale = useLocale();

  // 构建查询参数
  const queryParams: GetAdminEventsQueryParams = {};
  if (params?.page) queryParams.page = params.page;
  if (params?.pageSize) queryParams.pageSize = params.pageSize;
  if (params?.keyword) queryParams.keyword = params.keyword;
  if (params?.date_from) queryParams.date_from = params.date_from;
  if (params?.date_to) queryParams.date_to = params.date_to;

  // 使用生成的 API hook
  const { data, isLoading, error, ...rest } = useGetAdminEvents({
    queryParams: Object.keys(queryParams).length > 0 ? queryParams : undefined
  });

  // 转换数据格式
  const transformedData = data?.items?.map(item =>
    transformAdminEventData(item, locale)
  ) || [];

  return {
    data: transformedData,
    isLoading,
    error,
    pagination: data ? {
      total: data.total,
      page: data.page,
      pageSize: data.pageSize,
    } : undefined,
    ...rest
  };
}