import { describe, it, expect } from 'vitest';

import app from '@/app';

// 按原 tests/integration/circles.test.ts 内容粘贴👇

const mockDB = {
  prepare: (query: string) => ({
    all: async () => ({ results: [{ id: '1', name: 'Circle1' }] }),
    first: async () => {
      // 如果是 COUNT 查询，返回 total
      if (query.includes('COUNT')) {
        return { total: 1 };
      }
      // 否则返回单个记录
      return { id: '1', name: 'Circle1' };
    },
    bind: (...args: any[]) => ({
      all: async () => ({ results: [{ id: '1', name: 'Circle1' }] }),
      first: async () => {
        // 如果是 COUNT 查询，返回 total
        if (query.includes('COUNT')) {
          return { total: 1 };
        }
        // 否则返回单个记录
        return { id: '1', name: 'Circle1' };
      },
      run: async () => ({ success: true }),
    }),
  }),
};

// @ts-ignore
const Request = globalThis.Request;

function withEnv(url: string, env: any) {
  const base = url.startsWith('http') ? url : `http://localhost${url}`;
  return app.fetch(new Request(base), env);
}

describe('Circles API', () => {
  it('should return all circles', async () => {
    const res = await withEnv('/circles', { DB: mockDB });
    expect(res.status).toBe(200);
    const data = (await res.json()) as any;
    expect(Array.isArray(data.items)).toBe(true);
    expect(data.page).toBe(1);
    expect(data.pageSize).toBe(50);
    expect(data.total).toBe(1);
  });

  it('should return circle detail', async () => {
    const res = await withEnv('/circles/1', { DB: mockDB });
    expect(res.status).toBe(200);
    const data = (await res.json()) as any;
    expect(data.id).toBe('1');
  });

  it('should return 404 for not found circle', async () => {
    const res = await withEnv('/api/circles/not-exist-id', { DB: mockDB });
    expect(res.status).toBe(404);
  });

  it('should support fields filtering', async () => {
    const res = await withEnv('/circles?fields=id,name', { DB: mockDB });
    expect(res.status).toBe(200);
    const data = (await res.json()) as any;
    expect(Array.isArray(data.items)).toBe(true);
    expect(data.page).toBe(1);
    expect(data.total).toBe(1);
    expect(Object.keys(data.items[0]).sort()).toEqual(['id', 'name']);
  });
});
