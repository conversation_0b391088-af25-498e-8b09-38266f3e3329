import { renderHook, waitFor } from '@testing-library/react';
import { vi } from 'vitest';

import { useCreateEvent } from '../useCreateEvent';
import type { MultilingualEventInput } from '@/schemas/event';

// Mock the generated API hook
const mockMutate = vi.fn();

vi.mock('@/api/generated/ayafeedComponents', () => ({
  usePostAdminEvents: vi.fn(() => ({
    mutate: mockMutate,
    isPending: false,
    error: null,
  })),
}));

// Mock query client
const mockInvalidateQueries = vi.fn();
vi.mock('@tanstack/react-query', () => ({
  useQueryClient: () => ({
    invalidateQueries: mockInvalidateQueries,
  }),
}));

describe('useCreateEvent', () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  test('should create hook without errors', () => {
    const { result } = renderHook(() => useCreateEvent());

    expect(result.current).toBeDefined();
    expect(typeof result.current.mutate).toBe('function');
  });

  test('should transform multilingual input to API format', async () => {
    const { result } = renderHook(() => useCreateEvent());

    const multilingualInput: MultilingualEventInput = {
      id: 'test-event',
      name_en: 'Test Event',
      name_ja: 'テストイベント',
      name_zh: '测试活动',
      date_en: 'August 1, 2025',
      date_ja: '2025年8月1日',
      date_zh: '2025年8月1日',
      date_sort: 20250801,
      venue_id: 'tokyo-big-sight',
      image_url: '/images/events/test/thumb.jpg',
      url: 'https://example.com',
    };

    result.current.mutate(multilingualInput);

    expect(mockMutate).toHaveBeenCalledWith(
      {
        body: {
          name_en: 'Test Event',
          name_ja: 'テストイベント',
          name_zh: '测试活动',
          date_en: 'August 1, 2025',
          date_ja: '2025年8月1日',
          date_zh: '2025年8月1日',
          date_sort: 20250801,
          venue_id: 'tokyo-big-sight',
          image_url: '/images/events/test/thumb.jpg',
          url: 'https://example.com',
        },
      },
      expect.any(Object)
    );
  });

  test('should handle optional fields correctly', () => {
    const { result } = renderHook(() => useCreateEvent());

    const minimalInput: MultilingualEventInput = {
      name_en: 'Test Event',
      name_ja: 'テストイベント',
      name_zh: '测试活动',
      date_en: 'August 1, 2025',
      date_ja: '2025年8月1日',
      date_zh: '2025年8月1日',
      venue_id: 'test-venue',
    };

    result.current.mutate(minimalInput);

    expect(mockMutate).toHaveBeenCalledWith(
      {
        body: expect.objectContaining({
          venue_id: 'test-venue',
          image_url: null,
          url: null,
        }),
      },
      expect.any(Object)
    );
  });

  test('should handle mutation call', () => {
    const { result } = renderHook(() => useCreateEvent());

    const multilingualInput = {
      name_en: 'Test',
      name_ja: 'テスト',
      name_zh: '测试',
      date_en: 'Test Date',
      date_ja: 'テスト日付',
      date_zh: '测试日期',
      venue_id: 'test-venue',
    };

    // Should not throw error
    expect(() => {
      result.current.mutate(multilingualInput);
    }).not.toThrow();

    expect(mockMutate).toHaveBeenCalled();
  });
});
