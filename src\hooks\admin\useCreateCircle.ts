import { useQueryClient } from "@tanstack/react-query";

import { queryKeys } from "@/constants/queryKeys";
import { useAdminSuccessToast } from "@/hooks/useAdminSuccessToast";
import { request } from "@/lib/http";

type CircleInput = Record<string, any>;

export function useCreateCircle() {
  const qc = useQueryClient();

  return useAdminSuccessToast(
    (payload: CircleInput) =>
      request(`/admin/circles`, {
        method: "POST",
        body: JSON.stringify(payload),
      }),
    {
      onSuccess: () => {
        qc.invalidateQueries({ queryKey: queryKeys.adminCircles() });
      },
    },
    "创建成功"
  );
} 