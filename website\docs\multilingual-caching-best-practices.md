# 多语言缓存最佳实践指南

## 🎯 核心原则

### 1. 查询键必须包含语言信息

**✅ 正确做法**
```tsx
// 查询键包含语言，确保缓存隔离
queryKey: ['events', { locale: 'zh', page: 1 }]
queryKey: ['circles', { locale: 'ja', tags: ['anime'] }]
```

**❌ 错误做法**
```tsx
// 查询键缺少语言信息，导致缓存污染
queryKey: ['events', { page: 1 }]
queryKey: ['circles', { tags: ['anime'] }]
```

### 2. 请求头也应包含语言信息

**✅ 双重保障**
```tsx
// 查询键 + 请求头都包含语言信息
const response = await fetch('/api/events', {
  headers: {
    'Accept-Language': locale,
    'X-Locale': locale,
  }
});
```

## 📊 方案对比

| 方案 | 查询键 | 请求头 | 缓存效果 | 推荐度 |
|------|--------|--------|----------|--------|
| **A: 仅查询键** | ✅ 包含语言 | ❌ 无语言 | 🟢 优秀 | ⭐⭐⭐⭐ |
| **B: 仅请求头** | ❌ 无语言 | ✅ 包含语言 | 🔴 差 | ⭐ |
| **C: 双重保障** | ✅ 包含语言 | ✅ 包含语言 | 🟢 优秀 | ⭐⭐⭐⭐⭐ |

## 🛠️ 实现方案

### 方案 C: 双重保障（推荐）

#### 1. 查询键设计
```tsx
export function createLocaleQueryKey(
  baseKey: string | string[], 
  locale: Locale, 
  params?: Record<string, unknown>
) {
  return [
    ...Array.isArray(baseKey) ? baseKey : [baseKey],
    { locale, ...params }
  ];
}
```

#### 2. API 客户端
```tsx
class LocaleAwareApiClient {
  private buildHeaders(locale: Locale): Headers {
    const headers = new Headers();
    headers.set('Accept-Language', locale);
    headers.set('X-Locale', locale);
    return headers;
  }
}
```

#### 3. 查询 Hook
```tsx
export function useEventsQuery(params?: EventParams) {
  const locale = useLocale() as Locale;
  
  return useQuery({
    // ✅ 查询键包含语言
    queryKey: createLocaleQueryKey('events', locale, params),
    queryFn: async () => {
      // ✅ 请求头也包含语言
      const response = await localeApi.getEvents(locale, params);
      return response.data;
    },
  });
}
```

## 🔄 缓存管理策略

### 智能缓存失效
```tsx
// 语言切换时只失效当前语言的查询
queryClient.invalidateQueries({
  predicate: (query) => {
    const isLocaleSensitive = isLocaleSensitiveQuery(query.queryKey);
    const isCurrentLocale = isQueryForLocale(query.queryKey, currentLocale);
    return isLocaleSensitive && isCurrentLocale;
  }
});
```

### 缓存隔离效果
```tsx
// 不同语言的数据完全隔离
['events', { locale: 'zh' }] // 中文事件缓存
['events', { locale: 'ja' }] // 日文事件缓存
['events', { locale: 'en' }] // 英文事件缓存
```

## 📈 性能优势

### 缓存命中率提升
- **语言切换**: 从 0% 提升到 80%+
- **重复访问**: 避免不必要的网络请求
- **用户体验**: 即时响应，无需等待

### 网络请求优化
```
用户操作序列:
1. 访问中文页面 → 发起请求 ✅
2. 切换到日文   → 发起请求 ✅
3. 切换回中文   → 使用缓存 🚀 (节省请求)
4. 再切换日文   → 使用缓存 🚀 (节省请求)
```

## 🔧 实用工具

### 开发调试
```tsx
// 查看缓存统计
const stats = cacheManager.getCacheStats();
console.log('缓存分布:', stats.byLocale);

// 调试 API 调用
debugApiCalls(); // 启用 API 调试模式
```

### 缓存管理
```tsx
// 清理特定语言缓存
cacheManager.clearLocaleCache('ja');

// 获取缓存状态
const queries = queryClient.getQueryCache().getAll();
```

## ⚠️ 常见陷阱

### 1. 查询键不一致
```tsx
// ❌ 错误：查询键结构不一致
queryKey: ['events', locale, { page: 1 }]  // 语言在中间
queryKey: ['events', { locale, page: 1 }]  // 语言在对象中

// ✅ 正确：统一的查询键结构
queryKey: createLocaleQueryKey('events', locale, { page: 1 })
```

### 2. 忘记处理语言切换
```tsx
// ❌ 错误：清除所有缓存
queryClient.clear();

// ✅ 正确：选择性失效
cacheManager.handleLocaleSwitch(fromLocale, toLocale);
```

### 3. 混合语言数据
```tsx
// ❌ 错误：可能缓存混合语言的数据
queryKey: ['events'] // 无语言标识

// ✅ 正确：明确的语言标识
queryKey: ['events', { locale: 'zh' }]
```

## 🎯 总结

### 推荐的最佳实践

1. **查询键包含语言** - 确保缓存隔离
2. **请求头包含语言** - 服务端正确处理
3. **智能缓存管理** - 避免过度清理
4. **统一的 API 客户端** - 简化开发
5. **完善的调试工具** - 便于排查问题

### 核心收益

- 🚀 **性能提升**: 减少 60-80% 重复请求
- 💾 **缓存效率**: 真正的多语言缓存
- 🎨 **用户体验**: 即时语言切换
- 🔧 **开发体验**: 清晰的架构和工具

这种方案既保证了缓存的正确性，又提供了最佳的用户体验，是多语言应用的推荐做法。

## 🔧 后端配合修改建议

### 1. HTTP 头部处理

#### 语言检测优先级
```javascript
// Node.js/Express 示例
function detectLocale(req) {
  // 优先级：自定义头部 > Accept-Language > 默认语言
  return req.headers['x-locale'] ||
         req.headers['accept-language']?.split(',')[0] ||
         'zh';
}

app.use((req, res, next) => {
  req.locale = detectLocale(req);
  next();
});
```

#### 响应头部设置
```javascript
// 在响应中包含语言信息
app.use((req, res, next) => {
  res.setHeader('Content-Language', req.locale);
  res.setHeader('X-Response-Locale', req.locale);
  next();
});
```

### 2. API 端点设计

#### 方案 A: 查询参数（可选）
```javascript
// GET /api/events?locale=zh&page=1
app.get('/api/events', (req, res) => {
  const locale = req.query.locale || req.locale;
  const events = await getEventsByLocale(locale, req.query);
  res.json({ data: events, locale });
});
```

#### 方案 B: 仅请求头（推荐）
```javascript
// GET /api/events?page=1
// Headers: X-Locale: zh, Accept-Language: zh
app.get('/api/events', (req, res) => {
  const locale = req.locale; // 从中间件获取
  const events = await getEventsByLocale(locale, req.query);
  res.json({
    data: events,
    locale,
    timestamp: new Date().toISOString()
  });
});
```

### 3. 数据库查询优化

#### 多语言数据结构
```sql
-- 方案 A: 单表多列
CREATE TABLE events (
  id UUID PRIMARY KEY,
  name_zh VARCHAR(255),
  name_ja VARCHAR(255),
  name_en VARCHAR(255),
  description_zh TEXT,
  description_ja TEXT,
  description_en TEXT,
  created_at TIMESTAMP
);

-- 方案 B: 关联表（推荐）
CREATE TABLE events (
  id UUID PRIMARY KEY,
  venue_lat DECIMAL,
  venue_lng DECIMAL,
  start_date TIMESTAMP,
  created_at TIMESTAMP
);

CREATE TABLE event_translations (
  event_id UUID REFERENCES events(id),
  locale VARCHAR(5),
  name VARCHAR(255),
  description TEXT,
  PRIMARY KEY (event_id, locale)
);
```

#### 查询示例
```javascript
// 获取指定语言的事件数据
async function getEventsByLocale(locale, params) {
  const { page = 1, limit = 20, category } = params;

  const query = `
    SELECT
      e.id,
      e.venue_lat,
      e.venue_lng,
      e.start_date,
      et.name,
      et.description,
      et.locale
    FROM events e
    LEFT JOIN event_translations et ON e.id = et.event_id
    WHERE et.locale = $1
    ${category ? 'AND e.category = $2' : ''}
    ORDER BY e.start_date DESC
    LIMIT $${category ? 3 : 2} OFFSET $${category ? 4 : 3}
  `;

  const offset = (page - 1) * limit;
  const values = category
    ? [locale, category, limit, offset]
    : [locale, limit, offset];

  return await db.query(query, values);
}
```

### 4. 缓存策略

#### Redis 缓存键设计
```javascript
// 包含语言的缓存键
function getCacheKey(type, locale, params) {
  const paramStr = Object.entries(params)
    .sort(([a], [b]) => a.localeCompare(b))
    .map(([k, v]) => `${k}:${v}`)
    .join('|');

  return `${type}:${locale}:${paramStr}`;
}

// 使用示例
const cacheKey = getCacheKey('events', 'zh', { page: 1, category: 'anime' });
// 结果: "events:zh:category:anime|page:1"
```

#### 缓存实现
```javascript
async function getEventsWithCache(locale, params) {
  const cacheKey = getCacheKey('events', locale, params);

  // 尝试从缓存获取
  let events = await redis.get(cacheKey);
  if (events) {
    return JSON.parse(events);
  }

  // 从数据库查询
  events = await getEventsByLocale(locale, params);

  // 存入缓存（5分钟过期）
  await redis.setex(cacheKey, 300, JSON.stringify(events));

  return events;
}
```

### 5. API 响应格式标准化

#### 统一响应结构
```javascript
// 标准响应格式
function createResponse(data, locale, meta = {}) {
  return {
    success: true,
    data,
    locale,
    timestamp: new Date().toISOString(),
    meta: {
      total: meta.total || data.length,
      page: meta.page || 1,
      limit: meta.limit || 20,
      hasMore: meta.hasMore || false,
      ...meta
    }
  };
}

// 使用示例
app.get('/api/events', async (req, res) => {
  try {
    const locale = req.locale;
    const events = await getEventsWithCache(locale, req.query);
    const total = await getEventsCount(locale, req.query);

    res.json(createResponse(events, locale, {
      total,
      page: parseInt(req.query.page) || 1,
      hasMore: events.length === parseInt(req.query.limit || 20)
    }));
  } catch (error) {
    res.status(500).json({
      success: false,
      error: error.message,
      locale: req.locale
    });
  }
});
```

### 6. 搜索功能实现

#### 全文搜索
```javascript
// PostgreSQL 全文搜索示例
async function searchContent(locale, query, type = 'all') {
  const searchQuery = `
    SELECT
      'event' as type,
      e.id,
      et.name,
      et.description,
      ts_rank(
        to_tsvector('${locale === 'ja' ? 'japanese' : 'simple'}', et.name || ' ' || et.description),
        plainto_tsquery('${locale === 'ja' ? 'japanese' : 'simple'}', $1)
      ) as rank
    FROM events e
    JOIN event_translations et ON e.id = et.event_id
    WHERE et.locale = $2
    AND to_tsvector('${locale === 'ja' ? 'japanese' : 'simple'}', et.name || ' ' || et.description)
        @@ plainto_tsquery('${locale === 'ja' ? 'japanese' : 'simple'}', $1)
    ${type === 'events' || type === 'all' ? '' : 'AND 1=0'}

    UNION ALL

    SELECT
      'circle' as type,
      c.id,
      ct.name,
      ct.description,
      ts_rank(
        to_tsvector('${locale === 'ja' ? 'japanese' : 'simple'}', ct.name || ' ' || ct.description),
        plainto_tsquery('${locale === 'ja' ? 'japanese' : 'simple'}', $1)
      ) as rank
    FROM circles c
    JOIN circle_translations ct ON c.id = ct.circle_id
    WHERE ct.locale = $2
    AND to_tsvector('${locale === 'ja' ? 'japanese' : 'simple'}', ct.name || ' ' || ct.description)
        @@ plainto_tsquery('${locale === 'ja' ? 'japanese' : 'simple'}', $1)
    ${type === 'circles' || type === 'all' ? '' : 'AND 1=0'}

    ORDER BY rank DESC
    LIMIT 50
  `;

  return await db.query(searchQuery, [query, locale]);
}
```

### 7. 错误处理和日志

#### 语言相关的错误处理
```javascript
// 错误消息多语言化
const errorMessages = {
  zh: {
    NOT_FOUND: '未找到相关内容',
    INVALID_PARAMS: '参数无效',
    SERVER_ERROR: '服务器错误'
  },
  ja: {
    NOT_FOUND: 'コンテンツが見つかりません',
    INVALID_PARAMS: 'パラメータが無効です',
    SERVER_ERROR: 'サーバーエラー'
  },
  en: {
    NOT_FOUND: 'Content not found',
    INVALID_PARAMS: 'Invalid parameters',
    SERVER_ERROR: 'Server error'
  }
};

function createError(code, locale) {
  const messages = errorMessages[locale] || errorMessages.zh;
  return {
    success: false,
    error: {
      code,
      message: messages[code] || messages.SERVER_ERROR
    },
    locale
  };
}
```

#### 请求日志记录
```javascript
// 记录语言相关的请求信息
app.use((req, res, next) => {
  const startTime = Date.now();

  res.on('finish', () => {
    const duration = Date.now() - startTime;
    console.log({
      method: req.method,
      url: req.url,
      locale: req.locale,
      userAgent: req.headers['user-agent'],
      duration,
      status: res.statusCode,
      timestamp: new Date().toISOString()
    });
  });

  next();
});
```

### 8. 性能优化建议

#### 数据库索引
```sql
-- 为多语言查询创建复合索引
CREATE INDEX idx_event_translations_locale_name
ON event_translations(locale, name);

CREATE INDEX idx_event_translations_locale_search
ON event_translations USING gin(to_tsvector('simple', name || ' ' || description));

-- 为日文搜索创建专门索引
CREATE INDEX idx_event_translations_ja_search
ON event_translations USING gin(to_tsvector('japanese', name || ' ' || description))
WHERE locale = 'ja';
```

#### 连接池配置
```javascript
// 针对多语言应用的连接池优化
const pool = new Pool({
  host: process.env.DB_HOST,
  database: process.env.DB_NAME,
  user: process.env.DB_USER,
  password: process.env.DB_PASSWORD,
  // 增加连接池大小以应对多语言并发查询
  max: 20,
  idleTimeoutMillis: 30000,
  connectionTimeoutMillis: 2000,
});
```

### 9. API 文档示例

#### OpenAPI 规范
```yaml
paths:
  /api/events:
    get:
      summary: 获取事件列表
      parameters:
        - name: Accept-Language
          in: header
          schema:
            type: string
            enum: [zh, ja, en]
          description: 首选语言
        - name: X-Locale
          in: header
          schema:
            type: string
            enum: [zh, ja, en]
          description: 明确指定的语言
        - name: page
          in: query
          schema:
            type: integer
            minimum: 1
            default: 1
      responses:
        200:
          description: 成功
          headers:
            Content-Language:
              schema:
                type: string
              description: 响应内容的语言
          content:
            application/json:
              schema:
                type: object
                properties:
                  success:
                    type: boolean
                  data:
                    type: array
                    items:
                      $ref: '#/components/schemas/Event'
                  locale:
                    type: string
                  meta:
                    $ref: '#/components/schemas/PaginationMeta'
```

这些后端修改建议将确保前后端的多语言缓存策略完美配合，提供最佳的用户体验。
