import { describe, it, expect } from 'vitest';

import app from '@/app';

// 简易 Mock DB
function createMockDB(role: 'admin' | 'viewer' = 'admin') {
  const logRows = [
    {
      id: 'l1',
      action: 'CREATE_USER',
      target_type: 'user',
      created_at: '2025-01-01',
    },
    {
      id: 'l2',
      action: 'DELETE_EVENT',
      target_type: 'event',
      created_at: '2025-01-02',
    },
  ];

  return {
    prepare: (query: string) => {
      const upper = query.toUpperCase();
      const buildResponse = () => ({
        bind: (..._args: any[]) => buildResponse(),
        all: async () => {
          if (upper.includes('SELECT * FROM LOGS')) {
            return { results: logRows };
          }
          if (upper.includes('FROM AUTH_SESSION')) {
            return { results: [{ id: 'u1', username: 'tester', role }] };
          }
          return { results: [] };
        },
        first: async () => {
          if (upper.includes('COUNT(*)') && upper.includes('FROM LOGS'))
            return { total: logRows.length };
          if (upper.includes('FROM AUTH_SESSION'))
            return { id: 'u1', username: 'tester', role };
          return null;
        },
        run: async () => ({ success: true }),
      });
      return buildResponse();
    },
  };
}

// @ts-ignore
const Request = globalThis.Request;

describe('/admin/logs route', () => {
  it('should return paginated logs', async () => {
    const req = new Request('http://localhost/admin/logs?page=1&pageSize=2', {
      headers: {
        Cookie: 'auth_session=admin_session',
      },
    });

    const res = await app.fetch(req, { DB: createMockDB() });
    expect(res.status).toBe(200);
    const json = (await res.json()) as any;
    expect(json.page).toBe(1);
    expect(json.pageSize).toBe(2);
    expect(json.total).toBe(2);
    expect(json.items.length).toBe(2);
  });
});
