# 数据库Schema整合完成

## 📋 整合概述

所有历史migration已成功整合到 `db/schema.sql` 中，形成了完整的、最新的数据库结构定义。

## 🗂️ 整合的Migration

### ✅ 已整合的Migration文件

| Migration | 功能 | 状态 |
|-----------|------|------|
| `001_add_images_table.sql` | 图片表和索引 | ✅ 已整合 |
| `002_change_date_sort_to_int.sql` | date_sort字段类型修改 | ✅ 已整合 |
| `003_i18n_events_and_user_locale.sql` | 多语言支持 | ✅ 已整合 |
| `004_add_translation_tables.sql` | 翻译表 | ✅ 已整合 |
| `004_create_venues_table.sql` | venues表拆分 | 📄 保留作参考 |

## 🏗️ 当前数据库结构

### 核心表

#### 1. **venues** - 场馆表（新增）
```sql
CREATE TABLE venues (
  id TEXT PRIMARY KEY,                    -- venue唯一标识
  name_en/ja/zh TEXT NOT NULL,           -- 多语言名称
  address_en/ja/zh TEXT,                 -- 多语言地址
  lat REAL NOT NULL,                     -- 纬度
  lng REAL NOT NULL,                     -- 经度
  capacity INTEGER,                      -- 容量
  website_url TEXT,                      -- 官网
  phone TEXT,                           -- 电话
  description_en/ja/zh TEXT,            -- 多语言描述
  facilities TEXT,                      -- 设施信息(JSON)
  transportation TEXT,                  -- 交通信息(JSON)
  parking_info TEXT,                    -- 停车信息(JSON)
  created_at/updated_at TEXT
);
```

#### 2. **events** - 展会表（已更新）
```sql
CREATE TABLE events (
  id TEXT PRIMARY KEY,
  name_en/ja/zh TEXT NOT NULL,           -- 多语言名称
  date_en/ja/zh TEXT NOT NULL,           -- 多语言日期
  date_sort INTEGER NOT NULL,            -- 排序日期
  image_url TEXT,                        -- 海报图片
  venue_id TEXT NOT NULL,                -- 场馆外键 ⭐
  url TEXT,                             -- 官网
  created_at/updated_at TEXT
);
```

#### 3. **翻译表**
- `artist_translations` - Artist翻译
- `circle_translations` - Circle翻译

#### 4. **其他核心表**
- `circles` - 社团表
- `artists` - 作者表
- `appearances` - 参展关系表
- `auth_user` - 用户表（含locale字段）
- `auth_session` / `auth_key` - 认证表
- `bookmarks` - 收藏表
- `images` - 图片表
- `logs` - 审计日志表

### 索引优化

```sql
-- venues表索引
CREATE INDEX idx_venues_name_en ON venues(name_en);
CREATE INDEX idx_venues_lat_lng ON venues(lat, lng);

-- events表索引
CREATE INDEX idx_events_venue_id ON events(venue_id);
CREATE INDEX idx_events_date_sort ON events(date_sort);

-- 翻译表索引
CREATE INDEX idx_artist_translations_locale ON artist_translations(locale);
CREATE INDEX idx_circle_translations_locale ON circle_translations(locale);
CREATE INDEX idx_circle_translations_locale_name ON circle_translations(locale, name);

-- 其他索引...
```

## 🚀 部署指南

### 新环境部署

```bash
# 1. 本地开发环境
wrangler d1 execute ayafeed-dev --local --file=db/schema.sql
wrangler d1 execute ayafeed-dev --local --file=db/seeds/000_base.sql
wrangler d1 execute ayafeed-dev --local --file=db/seeds/001_admin.sql

# 2. 生产环境
wrangler d1 execute ayafeed-production --file=db/schema.sql
wrangler d1 execute ayafeed-production --file=db/seeds/000_base.sql
wrangler d1 execute ayafeed-production --file=db/seeds/001_admin.sql
```

### 使用重建脚本（推荐）

```bash
# 本地环境
pnpm tsx scripts/rebuild-database.ts --env=development

# 生产环境
pnpm tsx scripts/rebuild-database.ts --env=production --confirm
```

### 现有环境升级

如果您有现有的数据库需要升级到venues表结构：

```bash
# 使用venues迁移脚本
pnpm tsx scripts/migrate-venues.ts --env=development

# 或手动执行venues migration
wrangler d1 execute ayafeed-dev --local --file=db/migrations/004_create_venues_table.sql
```

## 🧹 Migration文件清理

整合完成后，可以清理已整合的migration文件：

```bash
# 预览清理操作
pnpm tsx scripts/cleanup-migrations.ts --dry-run

# 执行清理（会备份原文件）
pnpm tsx scripts/cleanup-migrations.ts --confirm
```

## 📊 数据结构变化

### 主要变化

1. **venues表独立** - 场馆信息从events表拆分出来
2. **多语言完整支持** - 所有表都支持en/ja/zh三种语言
3. **翻译表** - artist和circle支持独立的翻译表
4. **图片管理** - 完整的图片元数据管理
5. **用户本地化** - 用户表包含locale字段

### API影响

- **GET /venues** - 新增venues API
- **GET /events/{id}** - 返回包含venue信息的event
- **所有API** - 支持Accept-Language头进行本地化

### 前端影响

- **Event类型** - 支持新的venue关联结构
- **组件兼容** - 自动适配新旧数据格式
- **类型安全** - 完整的TypeScript类型定义

## ✅ 验证清单

部署后请验证以下功能：

- [ ] 数据库表创建成功
- [ ] venues表包含示例数据
- [ ] events表正确关联venues
- [ ] API接口正常响应
- [ ] 前端页面正常显示
- [ ] 多语言切换正常
- [ ] 地图功能正常

## 📝 后续维护

### Schema更新
今后的数据库结构变更应该：
1. 直接修改 `db/schema.sql`
2. 创建对应的migration脚本（用于现有环境升级）
3. 更新相关文档

### 最佳实践
- 新环境：直接使用 `db/schema.sql`
- 现有环境：使用migration脚本升级
- 定期备份：保持数据安全
- 测试优先：先在开发环境验证

## 🎉 总结

通过这次整合，我们实现了：
- ✅ 数据库结构的统一管理
- ✅ venues表的成功拆分
- ✅ 完整的多语言支持
- ✅ 清晰的部署流程
- ✅ 向后兼容的API设计

现在您可以安全地使用新的数据库结构，享受更好的数据组织和管理体验！
