import { useMutation, useQueryClient } from "@tanstack/react-query"

// 直接调用由 OpenAPI Codegen 生成的 fetcher
import { fetchPostCirclesCircleIdBookmark } from "@/api/generated/ayafeedComponents"
import { CircleAppearance } from "@/schemas"

/**
 * 切换书签（收藏）状态 —— 带乐观更新
 * 1. onMutate 立即更新本地列表，保存快照
 * 2. onError 失败回滚
 * 3. onSettled 最终再失效一次，确保与后端一致
 */
export function useToggleBookmark() {
  const qc = useQueryClient()

  // CircleAppearance 中目前没有 isBookmarked 字段，这里按需扩展
  type CircleWithBookmark = CircleAppearance & { isBookmarked?: boolean }

  return useMutation({
    // 直接调用生成的 fetcher，并仅返回 data 字段，保持旧行为兼容
    mutationFn: (circleId: string) =>
      fetchPostCirclesCircleIdBookmark({
        pathParams: { circleId },
      }).then((res) => {
        if (!res.data) {
          throw new Error("Empty response")
        }
        return res.data
      }),

    /**
     * 乐观更新：取消正在进行的同 key 请求 → 快照旧数据 → 写入新数据
     */
    onMutate: async (circleId: string) => {
      // 1. 取消可能正在进行的 circles 查询，避免竞态
      await qc.cancelQueries({ queryKey: ["circles"] })

      // 2. 获取所有 "circles" 开头的查询及其旧数据
      const prevQueries = qc.getQueriesData<CircleWithBookmark[]>({
        queryKey: ["circles"],
      })

      // 3. 立即写入乐观数据
      prevQueries.forEach(([key, _old]) => {
        qc.setQueryData<CircleWithBookmark[]>(key, (prev) =>
          prev?.map((c) =>
            c.circle_id === circleId
              ? { ...c, isBookmarked: !c.isBookmarked }
              : c
          )
        )
      })

      // 把旧数据作为 context 用于错误回滚
      return { prevQueries }
    },

    /**
     * 请求失败时回滚
     */
    onError: (_err, _circleId, context) => {
      context?.prevQueries?.forEach(([key, data]) => {
        qc.setQueryData(key, data)
      })
    },

    /**
     * 成功或失败都会执行：再失效一次，确保与后端最终一致
     */
    onSettled: () => {
      qc.invalidateQueries({ queryKey: ["circles"] })
    },
  })
}