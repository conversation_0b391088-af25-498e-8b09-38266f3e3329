/* eslint-disable no-constant-binary-expression */
// 测试文件允许常量布尔表达式
import { describe, expect, test } from "vitest"

import { cn } from "@/lib/utils"

describe("cn utility", () => {
  test("merges multiple class names", () => {
    const result = cn("p-2", "m-4", "text-center")
    expect(result).toBe("p-2 m-4 text-center")
  })

  test("deduplicates and resolves conflicting classes (tailwind-merge)", () => {
    const result = cn("p-2", "p-4")
    expect(result).toBe("p-4")
  })

  test("handles conditional / falsy values gracefully", () => {
    const isActive = true
    const result = cn("btn", isActive && "btn-active", false && "hidden")
    expect(result).toBe("btn btn-active")
  })
}) 