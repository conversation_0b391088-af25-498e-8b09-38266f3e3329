import { describe, it, expect, beforeEach, vi } from 'vitest';
import { OpenAPIHono } from '@hono/zod-openapi';

import type { HonoApp } from '@/types';
import { routes as imageRoutes } from '../routes';
import { routes as adminImageRoutes } from '../adminRoutes';

// Mock environment
const mockEnv = {
  DB: {
    prepare: vi.fn(() => ({
      bind: vi.fn(() => ({
        run: vi.fn(() => Promise.resolve({ success: true })),
        first: vi.fn(() =>
          Promise.resolve({
            id: 'test-image-id',
            group_id: 'test-group-id',
            resource_type: 'event',
            resource_id: 'test-event',
            image_type: 'poster',
            variant: 'thumb',
            file_path: '/images/events/test-event/poster_thumb.jpg',
            file_size: 1024,
            width: 200,
            height: 300,
            format: 'jpeg',
            created_at: '2025-01-01T00:00:00Z',
            updated_at: '2025-01-01T00:00:00Z',
          })
        ),
        all: vi.fn(() => Promise.resolve({ results: [] })),
      })),
    })),
  },
  R2: {
    put: vi.fn(() => Promise.resolve()),
    delete: vi.fn(() => Promise.resolve()),
  },
  ENVIRONMENT: 'test',
};

// Mock infrastructure functions
vi.mock('@/infrastructure', () => ({
  getDB: vi.fn(() => mockEnv.DB),
  Cache: vi.fn(),
  Logger: vi.fn(),
}));

// Mock audit log
vi.mock('@/utils/auditLog', () => ({
  recordLog: vi.fn(),
}));

describe('Images Integration Tests', () => {
  let app: OpenAPIHono<HonoApp>;

  beforeEach(() => {
    vi.clearAllMocks();
    app = new OpenAPIHono<HonoApp>();

    // Add middleware to inject environment
    app.use('*', async (c, next) => {
      c.env = mockEnv as any;
      await next();
    });

    // Mount routes
    app.route('/images', imageRoutes);
    app.route('/admin/images', adminImageRoutes);
  });

  describe('Public Routes', () => {
    it('should get images by resource', async () => {
      // Mock database response
      const mockImages = [
        {
          id: 'test-image-1',
          group_id: 'test-group',
          resource_type: 'event',
          resource_id: 'test-event',
          variant: 'thumb',
          file_path: '/images/events/test-event/poster_thumb.jpg',
        },
      ];

      const mockStmt = {
        bind: vi.fn().mockReturnThis(),
        all: vi.fn().mockResolvedValue({ results: mockImages }),
      };

      mockEnv.DB.prepare.mockReturnValue(mockStmt);

      const req = new Request('http://localhost/images/event/test-event');
      const res = await app.request(req, mockEnv);

      expect(res.status).toBe(200);

      const data = await res.json();
      expect(data.code).toBe(0);
      expect(data.data.images).toHaveLength(1);
      expect(data.data.pagination).toBeDefined();
    });

    it('should get single image by id', async () => {
      const mockImage = {
        id: 'test-image-1',
        group_id: 'test-group',
        variant: 'thumb',
      };

      const mockStmt = {
        bind: vi.fn().mockReturnThis(),
        first: vi.fn().mockResolvedValue(mockImage),
      };

      mockEnv.DB.prepare.mockReturnValue(mockStmt);

      const req = new Request('http://localhost/images/test-image-1');
      const res = await app.request(req, mockEnv);

      expect(res.status).toBe(200);

      const data = await res.json();
      expect(data.code).toBe(0);
      expect(data.data).toEqual(mockImage);
    });

    it('should get batch images successfully', async () => {
      const mockImages = [
        {
          id: 'test-image-1',
          resource_type: 'event',
          resource_id: 'event1',
          image_type: 'poster',
          variant: 'medium',
          file_path: '/images/events/event1/poster_medium.jpg',
        },
        {
          id: 'test-image-2',
          resource_type: 'event',
          resource_id: 'event2',
          image_type: 'poster',
          variant: 'medium',
          file_path: '/images/events/event2/poster_medium.jpg',
        },
      ];

      const mockStmt = {
        bind: vi.fn().mockReturnThis(),
        all: vi.fn().mockResolvedValue({ results: mockImages }),
      };

      mockEnv.DB.prepare.mockReturnValue(mockStmt);

      const req = new Request(
        'http://localhost/images/batch?events=event1,event2,event3&variant=medium&imageType=poster'
      );
      const res = await app.request(req, mockEnv);

      expect(res.status).toBe(200);

      const data = await res.json();
      expect(data.code).toBe(200);
      expect(data.message).toBe('批量查询成功');
      expect(data.data).toEqual({
        event1: mockImages[0],
        event2: mockImages[1],
        event3: null,
      });
    });

    it('should return 400 when events parameter is missing', async () => {
      const req = new Request('http://localhost/images/batch');
      const res = await app.request(req, mockEnv);

      expect(res.status).toBe(400);

      const data = await res.json();
      expect(data.code).toBe(40001);
      expect(data.message).toBe('events 参数必填');
    });

    it('should return 400 when events parameter is empty', async () => {
      const req = new Request('http://localhost/images/batch?events=');
      const res = await app.request(req, mockEnv);

      expect(res.status).toBe(400);

      const data = await res.json();
      expect(data.code).toBe(40001);
      expect(data.message).toBe('events 参数不能为空');
    });

    it('should return 404 for non-existent image', async () => {
      const mockStmt = {
        bind: vi.fn().mockReturnThis(),
        first: vi.fn().mockResolvedValue(null),
      };

      mockEnv.DB.prepare.mockReturnValue(mockStmt);

      const req = new Request('http://localhost/images/non-existent');
      const res = await app.request(req, mockEnv);

      expect(res.status).toBe(404);

      const data = await res.json();
      expect(data.code).toBe(40404);
    });
  });

  describe('Admin Routes', () => {
    it('should upload image successfully', async () => {
      // Mock successful upload
      const mockCreatedImage = {
        id: 'test-image-1',
        group_id: 'test-group',
        file_path: '/images/events/test-event/poster_thumb.jpg',
        variant: 'thumb',
      };

      const mockStmt = {
        bind: vi.fn().mockReturnThis(),
        run: vi.fn().mockResolvedValue({ success: true }),
        first: vi.fn().mockResolvedValue(mockCreatedImage),
      };

      mockEnv.DB.prepare.mockReturnValue(mockStmt);
      mockEnv.R2.put.mockResolvedValue(undefined);

      // Create form data
      const formData = new FormData();
      formData.append(
        'file',
        new File(['test'], 'test.jpg', { type: 'image/jpeg' })
      );
      formData.append('category', 'event');
      formData.append('resourceId', 'test-event');
      formData.append('imageType', 'poster');
      formData.append('variant', 'thumb');

      const req = new Request('http://localhost/admin/images/upload', {
        method: 'POST',
        body: formData,
      });

      const res = await app.request(req, mockEnv);

      expect(res.status).toBe(201);

      const data = await res.json();
      expect(data.code).toBe(0);
      expect(data.message).toBe('图片上传成功');
      expect(data.data.id).toBe('test-image-1');
    });

    it('should reject invalid file type', async () => {
      const formData = new FormData();
      formData.append(
        'file',
        new File(['test'], 'test.txt', { type: 'text/plain' })
      );
      formData.append('category', 'event');
      formData.append('resourceId', 'test-event');
      formData.append('imageType', 'poster');
      formData.append('variant', 'thumb');

      const req = new Request('http://localhost/admin/images/upload', {
        method: 'POST',
        body: formData,
      });

      const res = await app.request(req, mockEnv);

      expect(res.status).toBe(400);

      const data = await res.json();
      expect(data.code).toBe(50001);
      expect(data.message).toContain('不支持的文件类型');
    });

    it('should delete images successfully', async () => {
      const mockStmt = {
        bind: vi.fn().mockReturnThis(),
        run: vi.fn().mockResolvedValue({ success: true }),
      };

      mockEnv.DB.prepare.mockReturnValue(mockStmt);
      mockEnv.R2.delete.mockResolvedValue(undefined);

      const req = new Request('http://localhost/admin/images', {
        method: 'DELETE',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          relativePaths: ['/images/events/test-event/poster.jpg'],
        }),
      });

      const res = await app.request(req, mockEnv);

      expect(res.status).toBe(200);

      const data = await res.json();
      expect(data.code).toBe(0);
      expect(data.data.deletedCount).toBe(1);
      expect(data.data.failedPaths).toHaveLength(0);
    });

    it('should handle R2 deletion failure gracefully', async () => {
      mockEnv.R2.delete.mockRejectedValue(new Error('R2 error'));

      const req = new Request('http://localhost/admin/images', {
        method: 'DELETE',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          relativePaths: ['/images/events/test-event/poster.jpg'],
        }),
      });

      const res = await app.request(req, mockEnv);

      expect(res.status).toBe(200);

      const data = await res.json();
      expect(data.code).toBe(0);
      expect(data.data.deletedCount).toBe(0);
      expect(data.data.failedPaths).toHaveLength(1);
    });
  });

  describe('Error Handling', () => {
    it('should handle missing file in upload', async () => {
      const formData = new FormData();
      formData.append('category', 'event');
      formData.append('resourceId', 'test-event');
      formData.append('imageType', 'poster');
      formData.append('variant', 'thumb');

      const req = new Request('http://localhost/admin/images/upload', {
        method: 'POST',
        body: formData,
      });

      const res = await app.request(req, mockEnv);

      expect(res.status).toBe(422);

      const data = await res.json();
      expect(data.fieldErrors).toBeDefined();
      expect(data.fieldErrors.file).toBe('文件不能为空');
    });

    it('should handle missing required fields', async () => {
      const formData = new FormData();
      formData.append(
        'file',
        new File(['test'], 'test.jpg', { type: 'image/jpeg' })
      );
      // Missing required fields

      const req = new Request('http://localhost/admin/images/upload', {
        method: 'POST',
        body: formData,
      });

      const res = await app.request(req, mockEnv);

      expect(res.status).toBe(400);

      const data = await res.json();
      expect(data.message || data.error || 'validation error').toContain(
        '必填字段'
      );
    });
  });
});
