/* eslint-disable react/display-name */
// 测试包装组件允许匿名函数，不需要 displayName
import { QueryClient, QueryClientProvider } from "@tanstack/react-query";
import { renderHook, waitFor } from "@testing-library/react";
import { http as mswHttp, HttpResponse } from "msw";
import React from "react";
import { describe, test, expect } from "vitest";

import { useAdminCircleDetail } from "@/hooks/admin/useAdminCircleDetail";
import { API_BASE } from "@/lib/http";
import { server } from "@test/testServer";

function createWrapper() {
  const client = new QueryClient({
    defaultOptions: { queries: { retry: false } },
  });
  return ({ children }: { children: React.ReactNode }) => (
    <QueryClientProvider client={client}>{children}</QueryClientProvider>
  );
}

describe("useAdminCircleDetail", () => {
  test("fetches circle detail when id provided", async () => {
    const mock = { id: "c1", name: "Circle 1" };
    server.use(
      mswHttp.get(`${API_BASE}/admin/circles/c1`, () => {
        return HttpResponse.json(mock);
      })
    );

    const { result } = renderHook(() => useAdminCircleDetail("c1"), {
      wrapper: createWrapper(),
    });

    await waitFor(() => {
      expect(result.current.data).toEqual(mock);
    });
  });

  test("query disabled when id is undefined", () => {
    const { result } = renderHook(() => useAdminCircleDetail(undefined), {
      wrapper: createWrapper(),
    });

    // Tanstack v5 expose isEnabled? Actually query has 'enabled' property once queryKey; We'll assert isFetched is false and fetchStatus is 'idle'.
    expect(result.current.fetchStatus).toBe("idle");
  });
}); 