'use client';

import React, { useState, useCallback } from 'react';
import Image from 'next/image';
import { cn } from '@/lib/utils';
import { useEventImage, useEventImagesBatch } from '@/hooks/useEventImage';
import { Skeleton } from '@/components/ui/skeleton';
import { ImageIcon, AlertCircle } from 'lucide-react';

// 事件图片显示组件的 Props
export interface EventImageDisplayProps {
  /** 事件 ID */
  eventId: string;
  /** 图片类型 */
  imageType?: 'poster' | 'banner' | 'thumbnail';
  /** 显示变体 */
  variant?: 'original' | 'large' | 'medium' | 'thumb';
  /** 图片宽度 */
  width?: number;
  /** 图片高度 */
  height?: number;
  /** 自定义类名 */
  className?: string;
  /** 图片 alt 文本 */
  alt?: string;
  /** 是否显示加载状态 */
  showLoading?: boolean;
  /** 是否显示错误状态 */
  showError?: boolean;
  /** 点击回调 */
  onClick?: () => void;
  /** 加载完成回调 */
  onLoad?: () => void;
  /** 加载错误回调 */
  onError?: (error: Error) => void;
}

/**
 * 事件图片显示组件
 * 支持多种变体和懒加载
 */
export function EventImageDisplay({
  eventId,
  imageType = 'poster',
  variant = 'medium',
  width,
  height,
  className,
  alt,
  showLoading = true,
  showError = true,
  onClick,
  onLoad,
  onError
}: EventImageDisplayProps) {
  const [imageError, setImageError] = useState(false);
  const [imageLoaded, setImageLoaded] = useState(false);

  // 查询图片数据
  const { data: images, isLoading, error } = useEventImage(eventId, variant);

  // 处理图片加载完成
  const handleImageLoad = useCallback(() => {
    setImageLoaded(true);
    onLoad?.();
  }, [onLoad]);

  // 处理图片加载错误
  const handleImageError = useCallback(() => {
    setImageError(true);
    const error = new Error(`Failed to load image for event ${eventId}`);
    onError?.(error);
  }, [eventId, onError]);

  // 处理图片数据：可能是单个图片对象或图片数组
  const image = Array.isArray(images) ? images[0] : images;

  // 构造图片 URL
  // relativePath 格式为 "/images/{id}/file"，需要添加 API 基础路径
  const imageUrl = image?.relativePath ?
    `${process.env.NEXT_PUBLIC_API_URL || 'http://localhost:8787'}${image.relativePath}` :
    null;

  // 计算默认尺寸
  const defaultDimensions = {
    original: { width: 1200, height: 900 },
    large: { width: 1200, height: 900 },
    medium: { width: 400, height: 300 },
    thumb: { width: 200, height: 150 }
  };

  const dimensions = {
    width: width || defaultDimensions[variant].width,
    height: height || defaultDimensions[variant].height
  };

  // 加载状态
  if (isLoading && showLoading) {
    return (
      <Skeleton 
        className={cn('rounded-lg', className)} 
        style={{ width: dimensions.width, height: dimensions.height }}
      />
    );
  }

  // 错误状态
  if ((error || imageError || !imageUrl) && showError) {
    return (
      <div 
        className={cn(
          'flex flex-col items-center justify-center rounded-lg border border-dashed border-muted-foreground/25 bg-muted/50',
          className
        )}
        style={{ width: dimensions.width, height: dimensions.height }}
      >
        <AlertCircle className="h-8 w-8 text-muted-foreground mb-2" />
        <p className="text-sm text-muted-foreground text-center">
          {error ? '加载失败' : '暂无图片'}
        </p>
      </div>
    );
  }

  // 无图片时的占位符
  if (!imageUrl) {
    return (
      <div 
        className={cn(
          'flex flex-col items-center justify-center rounded-lg border border-dashed border-muted-foreground/25 bg-muted/50',
          className
        )}
        style={{ width: dimensions.width, height: dimensions.height }}
      >
        <ImageIcon className="h-8 w-8 text-muted-foreground mb-2" />
        <p className="text-sm text-muted-foreground">暂无图片</p>
      </div>
    );
  }

  return (
    <div 
      className={cn('relative overflow-hidden rounded-lg', className)}
      style={{ width: dimensions.width, height: dimensions.height }}
      onClick={onClick}
    >
      {/* 加载状态覆盖层 */}
      {!imageLoaded && showLoading && (
        <Skeleton 
          className="absolute inset-0 z-10" 
        />
      )}
      
      {/* 图片 */}
      <Image
        src={imageUrl}
        alt={alt || `Event ${eventId} ${imageType}`}
        width={dimensions.width}
        height={dimensions.height}
        className={cn(
          'object-cover transition-opacity duration-300',
          imageLoaded ? 'opacity-100' : 'opacity-0',
          onClick && 'cursor-pointer hover:opacity-90'
        )}
        onLoad={handleImageLoad}
        onError={handleImageError}
        priority={variant === 'large' || variant === 'original'}
      />
    </div>
  );
}

/**
 * 事件图片网格显示组件
 * 显示事件的所有图片
 */
export interface EventImageGridProps {
  /** 事件 ID */
  eventId: string;
  /** 图片类型 */
  imageType?: 'poster' | 'banner' | 'thumbnail';
  /** 显示变体 */
  variant?: 'original' | 'large' | 'medium' | 'thumb';
  /** 网格列数 */
  columns?: number;
  /** 自定义类名 */
  className?: string;
  /** 图片点击回调 */
  onImageClick?: (image: any, index: number) => void;
}

export function EventImageGrid({
  eventId,
  imageType = 'poster',
  variant = 'medium',
  columns = 3,
  className,
  onImageClick
}: EventImageGridProps) {
  const { data: images, isLoading, error } = useEventImage(eventId, variant);

  if (isLoading) {
    return (
      <div className={cn(`grid grid-cols-${columns} gap-4`, className)}>
        {Array.from({ length: 6 }).map((_, index) => (
          <Skeleton key={index} className="aspect-[4/3] rounded-lg" />
        ))}
      </div>
    );
  }

  if (error) {
    return (
      <div className={cn("flex items-center gap-2 p-4 border border-red-200 bg-red-50 text-red-700 rounded-lg", className)}>
        <AlertCircle className="h-4 w-4" />
        <div className="text-sm">
          加载图片失败: {error.message}
        </div>
      </div>
    );
  }

  if (!images || images.length === 0) {
    return (
      <div className={cn('text-center py-8', className)}>
        <ImageIcon className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
        <p className="text-muted-foreground">暂无图片</p>
      </div>
    );
  }

  return (
    <div className={cn(`grid grid-cols-${columns} gap-4`, className)}>
      {images.map((image, index) => (
        <EventImageDisplay
          key={`${image.groupId}-${image.variant}-${index}`}
          eventId={eventId}
          imageType={imageType}
          variant={variant}
          className="aspect-[4/3]"
          onClick={() => onImageClick?.(image, index)}
        />
      ))}
    </div>
  );
}
