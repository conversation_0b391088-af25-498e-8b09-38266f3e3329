'use client'

import { useState, useEffect } from 'react'
import { useLocale } from 'next-intl'
import { getCurrentLocale, setLocaleToCookie, type Locale, SUPPORTED_LOCALES } from '@/lib/locale-utils'
import { request } from '@/lib/http'
import { ayafeedFetch } from '@/lib/openapi-fetcher'
import '@/lib/debug-locale' // 导入调试工具

export default function LocaleHeadersDebugPage() {
  const currentLocale = useLocale() as Locale
  const [testResults, setTestResults] = useState<string[]>([])
  const [isLoading, setIsLoading] = useState(false)

  useEffect(() => {
    // 页面加载时自动调试语言状态
    if (typeof window !== 'undefined' && (window as any).debugLocale) {
      (window as any).debugLocale.debugLocaleState()
    }
  }, [])

  const addResult = (message: string) => {
    setTestResults(prev => [...prev, `${new Date().toLocaleTimeString()}: ${message}`])
  }

  const testHttpRequest = async () => {
    setIsLoading(true)
    addResult('开始测试 HTTP 请求...')
    
    try {
      // 测试 request() 函数
      addResult(`当前语言（getCurrentLocale）: ${getCurrentLocale()}`)
      addResult(`当前语言（useLocale）: ${currentLocale}`)
      
      // 拦截 fetch 请求来查看实际发送的头部
      const originalFetch = window.fetch
      let capturedHeaders: Record<string, string> = {}
      
      window.fetch = async (input, init) => {
        if (typeof input === 'string' && input.includes('/api/')) {
          capturedHeaders = {}
          if (init?.headers) {
            if (init.headers instanceof Headers) {
              init.headers.forEach((value, key) => {
                capturedHeaders[key] = value
              })
            } else if (Array.isArray(init.headers)) {
              init.headers.forEach(([key, value]) => {
                capturedHeaders[key] = value
              })
            } else {
              capturedHeaders = { ...init.headers }
            }
          }
          addResult(`拦截到请求头: ${JSON.stringify(capturedHeaders, null, 2)}`)
        }
        return originalFetch(input, init)
      }

      try {
        // 测试一个简单的 API 请求
        await request('/events?limit=1')
        addResult('request() 函数调用成功')
      } catch (error) {
        addResult(`request() 函数调用失败: ${error}`)
      }

      try {
        // 测试 openapi-fetcher
        await ayafeedFetch({
          url: '/events?limit=1',
          method: 'GET'
        })
        addResult('ayafeedFetch() 函数调用成功')
      } catch (error) {
        addResult(`ayafeedFetch() 函数调用失败: ${error}`)
      }

      // 恢复原始 fetch
      window.fetch = originalFetch
      
    } catch (error) {
      addResult(`测试失败: ${error}`)
    } finally {
      setIsLoading(false)
    }
  }

  const changeLanguage = (newLocale: Locale) => {
    addResult(`切换语言到: ${newLocale}`)
    setLocaleToCookie(newLocale)
    addResult(`Cookie 已设置，当前 getCurrentLocale(): ${getCurrentLocale()}`)
    
    // 刷新页面
    window.location.reload()
  }

  const clearResults = () => {
    setTestResults([])
  }

  return (
    <div className="container mx-auto p-6">
      <h1 className="text-2xl font-bold mb-6">语言头调试页面</h1>
      
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <div className="space-y-4">
          <div className="bg-gray-100 p-4 rounded">
            <h2 className="text-lg font-semibold mb-2">当前状态</h2>
            <p>useLocale(): {currentLocale}</p>
            <p>getCurrentLocale(): {getCurrentLocale()}</p>
            <p>Document Cookie: {typeof document !== 'undefined' ? document.cookie : 'N/A'}</p>
          </div>

          <div className="space-y-2">
            <h2 className="text-lg font-semibold">语言切换</h2>
            {SUPPORTED_LOCALES.map(locale => (
              <button
                key={locale}
                onClick={() => changeLanguage(locale)}
                disabled={locale === currentLocale}
                className={`px-4 py-2 rounded mr-2 ${
                  locale === currentLocale
                    ? 'bg-blue-500 text-white'
                    : 'bg-gray-200 hover:bg-gray-300'
                }`}
              >
                {locale === 'ja' && '日本語'}
                {locale === 'en' && 'English'}
                {locale === 'zh' && '中文'}
              </button>
            ))}
          </div>

          <div className="space-y-2">
            <h2 className="text-lg font-semibold">测试操作</h2>
            <button
              onClick={testHttpRequest}
              disabled={isLoading}
              className="px-4 py-2 bg-green-500 text-white rounded hover:bg-green-600 disabled:opacity-50"
            >
              {isLoading ? '测试中...' : '测试 HTTP 请求头'}
            </button>
            <button
              onClick={clearResults}
              className="px-4 py-2 bg-red-500 text-white rounded hover:bg-red-600 ml-2"
            >
              清除结果
            </button>
          </div>
        </div>

        <div className="bg-gray-50 p-4 rounded">
          <h2 className="text-lg font-semibold mb-2">测试结果</h2>
          <div className="max-h-96 overflow-y-auto">
            {testResults.length === 0 ? (
              <p className="text-gray-500">点击&ldquo;测试 HTTP 请求头&rdquo;开始测试</p>
            ) : (
              <pre className="text-sm whitespace-pre-wrap">
                {testResults.join('\n')}
              </pre>
            )}
          </div>
        </div>
      </div>
    </div>
  )
}
