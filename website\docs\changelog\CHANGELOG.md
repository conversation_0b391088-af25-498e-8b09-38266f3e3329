# Changelog

所有可观测的变更将记录于此。格式遵循 [Keep a Changelog](https://keepachangelog.com/zh-CN/1.1.0/) 规范，并遵循 [语义化版本](https://semver.org/lang/zh-CN/)。

## [Unreleased]
### Removed
- 移除 E2E 测试相关配置和代码：
  - 删除 Playwright 配置和测试文件
  - 移除 MSW 相关配置
  - 清理 CI 工作流中的 E2E 测试任务
  - 移除相关依赖：`@playwright/test` 和 `msw`

<!-- 下一版本占位符 -->

## [0.2.1] - 2025-07-18
### Changed
- 会展模块 (`/events`) 迁移至 OpenAPI 生成的 React Query hooks：
  - 列表页使用 `useSuspenseGetEvents`。
  - 详情页使用 `useGetEventsId`、`useGetEventsIdCircles`。
- 移除自定义 `useEventDetail`、`useEventCircles` 及旧测试。
- 新增统一 Fetcher `src/lib/openapi-fetcher.ts`，自动处理 401 与 Toast。
- `pnpm sync:api` 同步脚本写入 `.husky/pre-commit` 与 CI workflow，确保类型与接口一致。

### Added
- README 新增 **OpenAPI 类型生成 & Fetcher** 章节，记录使用规范与脚本。
- 基础架构改进：OpenAPI 类型同步 P0  
  - `sync-openapi-types`  
  - `api-helper-types`  
  - `refactor-api-calls`  
- 基础架构改进：OpenAPI 类型同步 P1  
  - `http-generic-sugar`  
  - `zod-review`  
  - `lint-ignore-generated`

### Removed
- 旧 hooks 与对应文档引用。


## [0.2.0] - 2025-07-16
### Added
- 后台管理 CRUD 逻辑与数据刷新 (`admin-logic`).
- 后台管理接口联调及子任务 (`admin-integration`):
  - 统一 `ApiError` 处理与 Toast 提示。
  - 成功操作 Toast 反馈。
  - 表单与接口校验：`react-hook-form` + `zodResolver`。
  - 数据获取与缓存：使用 `TanStack Query` 统一封装（Circles / Events 模块）。
  - 管理端单元测试：列表 / 表单 / 删除 / 鉴权。
  - 用户管理模块：列表、编辑、权限分配。
  - 代码清理：移除 `console`，提取共用工具。
- 认证流程 (`auth-flow`): 登出、基于角色的访问控制。
- 关键逻辑单元测试补足 (`testing-critical`):
  - `src/lib/utils.ts` 纯函数单元测试。
  - `RoleGuard` 未授权跳转 `/403` 场景测试。
- 测试基建 (`test-setup`): 集成 `Vitest` + `Testing Library`。


## [0.2.2] - 2025-07-19
### Added
- 集成 `openapi-fetch`：生成 26 个 React Query hooks 与 4 个辅助类型文件（≈3.2K LOC）。

### Changed
- 在 `src/lib/http.ts` 中注入统一 401/Toast 拦截逻辑。
- 错误码表迁移至 OpenAPI `components.schemas.ErrorCodes`，移除 `genErrorEnum` 脚本。
- 更新文档：
  - `docs/api/README.md` 说明生成流程（+285 LOC）。
  - `docs/guides/contribution.md` 更新贡献流程章节（+64 LOC）。

### Removed
- 删除手写 API 模块：`src/api/events.ts`、`src/api/circles.json`、`src/api/data.tsx`，共计清理冗余代码 1.1K LOC。


## [0.2.3] - 2025-07-19
### Added
- Circles/Events 表单扩展：新增 `category`、`twitter`、`pixiv`、`web` 字段并完善必填/可选校验
- 管理端 Circles / Events 新建与编辑页覆盖新增字段
- 新增 Vitest 单元测试，覆盖表单校验与提交流程（10+ 用例）

### Changed
- `src/schemas/circle.ts`、`src/schemas/event.ts`：更新输入校验逻辑，支持新字段与空值预处理
- `src/api/generated/ayafeedFetcher.ts`：改为 re-export `ayafeedFetch`，统一网络调用层
- 看板 `docs/TODO.md`：移除已完成任务，保持列状态同步

### Removed
- —

## [0.2.4] - 2025-07-19
### Added
- 集成测试 `mutation-success-toast`：验证写操作成功时 X-Success-Message Header 触发 `toast.success`。

### Changed
- 更新看板 `docs/TODO.md`：将 `test-toast-success` 任务移至已完成列。

<!-- 下一版本占位符 -->

## [0.2.5] - 2025-07-21
### Added
- `feature-circle-detail`：前台社团详情页 `/circles/[id]`
  - Page、Loading、Error 边界组件
  - 参展记录虚拟列表 `AppearancesGrid`
  - 动态 Metadata 生成
  - 单元测试 `CircleDetailPage.test.tsx`
- 后台 Circles 写操作完成后自动跳转至前台详情页

### Changed
- Kanban `docs/TODO.md` 更新：`feature-circle-detail` 子任务全部完成并移动至已完成列。

### Fixed
- 修复创建/更新社团后跳转逻辑，保证立刻查看最新数据。

<!-- 下一版本占位符 --> 

## [0.2.6] - 2025-07-21
### Added
- `feature-events`：会展列表与详情页
  - 列表页展示会展卡片、筛选、分页
  - 详情页展示基本信息、参展社团列表
  - 相关组件单元测试与文档
- `optimization-api-error-handling`：统一错误处理机制
  - 全局 Toast 提示组件
  - API 错误统一拦截与展示
  - 错误边界与 Fallback UI
  - 相关测试用例

### Changed
- 看板任务状态更新：将已完成功能迁移至 CHANGELOG
- 更新相关文档与测试用例

<!-- 下一版本占位符 --> 