/**
 * Ayafeed 文档站点的自定义样式
 * 你可以在这里覆盖默认的Docusaurus变量或添加自定义CSS
 */

/* 你可以覆盖默认的Docusaurus CSS变量 */
:root {
  --ifm-color-primary: #2e8555;
  --ifm-color-primary-dark: #29784c;
  --ifm-color-primary-darker: #277148;
  --ifm-color-primary-darkest: #205d3b;
  --ifm-color-primary-light: #33925d;
  --ifm-color-primary-lighter: #359962;
  --ifm-color-primary-lightest: #3cad6e;
  --ifm-code-font-size: 95%;
  --docusaurus-highlighted-code-line-bg: rgba(0, 0, 0, 0.1);
}

/* 深色主题 */
[data-theme='dark'] {
  --ifm-color-primary: #25c2a0;
  --ifm-color-primary-dark: #21af90;
  --ifm-color-primary-darker: #1fa588;
  --ifm-color-primary-darkest: #1a8870;
  --ifm-color-primary-light: #29d5b0;
  --ifm-color-primary-lighter: #32d8b4;
  --ifm-color-primary-lightest: #4fddbf;
  --docusaurus-highlighted-code-line-bg: rgba(0, 0, 0, 0.3);
}

/* 自定义样式 */
.hero__title {
  font-size: 3rem;
}

.hero__subtitle {
  font-size: 1.5rem;
  margin-bottom: 2rem;
}

/* 代码块样式优化 */
.prism-code {
  font-size: 14px;
  line-height: 1.5;
}

/* 导航栏样式 */
.navbar__brand {
  font-weight: bold;
}

/* 侧边栏样式 */
.menu__link {
  font-size: 14px;
}

/* 响应式设计 */
@media screen and (max-width: 768px) {
  .hero__title {
    font-size: 2rem;
  }
  
  .hero__subtitle {
    font-size: 1.2rem;
  }
}

/* 自定义组件样式 */
.feature {
  text-align: center;
  padding: 2rem;
}

.feature__icon {
  font-size: 3rem;
  margin-bottom: 1rem;
}

.feature__title {
  font-size: 1.5rem;
  font-weight: bold;
  margin-bottom: 1rem;
}

.feature__description {
  font-size: 1rem;
  color: var(--ifm-color-content-secondary);
}

/* 卡片样式 */
.card {
  border: 1px solid var(--ifm-color-emphasis-200);
  border-radius: 8px;
  padding: 1.5rem;
  margin-bottom: 1rem;
  background: var(--ifm-background-color);
}

.card__header {
  font-size: 1.25rem;
  font-weight: bold;
  margin-bottom: 0.5rem;
}

.card__body {
  color: var(--ifm-color-content-secondary);
}

/* 徽章样式 */
.badge {
  display: inline-block;
  padding: 0.25rem 0.5rem;
  font-size: 0.75rem;
  font-weight: bold;
  border-radius: 4px;
  text-transform: uppercase;
}

.badge--primary {
  background-color: var(--ifm-color-primary);
  color: white;
}

.badge--secondary {
  background-color: var(--ifm-color-secondary);
  color: white;
}

.badge--success {
  background-color: var(--ifm-color-success);
  color: white;
}

.badge--warning {
  background-color: var(--ifm-color-warning);
  color: white;
}

.badge--danger {
  background-color: var(--ifm-color-danger);
  color: white;
}

/* 表格样式优化 */
table {
  border-collapse: collapse;
  width: 100%;
  margin-bottom: 1rem;
}

table th,
table td {
  border: 1px solid var(--ifm-color-emphasis-300);
  padding: 0.75rem;
  text-align: left;
}

table th {
  background-color: var(--ifm-color-emphasis-100);
  font-weight: bold;
}

/* 警告框样式 */
.admonition {
  margin-bottom: 1rem;
  padding: 1rem;
  border-left: 4px solid;
  border-radius: 4px;
}

.admonition--note {
  border-color: var(--ifm-color-info);
  background-color: var(--ifm-color-info-contrast-background);
}

.admonition--tip {
  border-color: var(--ifm-color-success);
  background-color: var(--ifm-color-success-contrast-background);
}

.admonition--warning {
  border-color: var(--ifm-color-warning);
  background-color: var(--ifm-color-warning-contrast-background);
}

.admonition--danger {
  border-color: var(--ifm-color-danger);
  background-color: var(--ifm-color-danger-contrast-background);
}
