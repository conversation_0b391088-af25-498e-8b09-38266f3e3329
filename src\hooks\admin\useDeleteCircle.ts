import { useQueryClient } from "@tanstack/react-query";

import { queryKeys } from "@/constants/queryKeys";
import { useAdminSuccessToast } from "@/hooks/useAdminSuccessToast";
import { request } from "@/lib/http";

export function useDeleteCircle() {
  const qc = useQueryClient();

  return useAdminSuccessToast(
    (id: string) => request(`/admin/circles/${id}`, { method: "DELETE" }),
    {
      onSuccess: () => {
        qc.invalidateQueries({ queryKey: queryKeys.adminCircles() });
      },
    },
    "删除成功"
  );
} 