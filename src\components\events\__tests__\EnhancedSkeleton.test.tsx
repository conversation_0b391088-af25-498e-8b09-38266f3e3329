import { expect, test } from "vitest"

import { 
  Skeleton,
  EventHeaderSkeleton,
  TabContentSkeleton,
  OverviewTabSkeleton,
  VenueTabSkeleton,
  TravelTabSkeleton
} from "@/components/events/EnhancedSkeleton"
import { renderWithProviders, screen } from "@test/test-utils"

test("renders basic Skeleton component", () => {
  const { container } = renderWithProviders(<Skeleton />)

  const skeletonElement = container.firstChild as HTMLElement
  expect(skeletonElement).toHaveClass("skeleton")
  expect(skeletonElement).toHaveClass("rounded-md")
})

test("renders Skeleton with custom className", () => {
  const { container } = renderWithProviders(<Skeleton className="custom-class h-10 w-20" />)

  const skeletonElement = container.firstChild as HTMLElement
  expect(skeletonElement).toHaveClass("skeleton")
  expect(skeletonElement).toHaveClass("custom-class")
  expect(skeletonElement).toHaveClass("h-10")
  expect(skeletonElement).toHaveClass("w-20")
})

test("renders EventHeaderSkeleton with all sections", () => {
  const { container } = renderWithProviders(<EventHeaderSkeleton />)

  // 检查主要结构
  expect(container.querySelector('.hero-gradient')).toBeInTheDocument()
  expect(container.querySelector('.map-container')).toBeInTheDocument()

  // 检查骨架元素数量
  const skeletonElements = container.querySelectorAll('.skeleton')
  expect(skeletonElements.length).toBeGreaterThan(10) // 应该有多个骨架元素

  // 检查特定的骨架元素
  expect(container.querySelector('.aspect-\\[4\\/5\\.6\\]')).toBeInTheDocument() // 海报骨架
})

test("renders TabContentSkeleton with filter bar and grid", () => {
  const { container } = renderWithProviders(<TabContentSkeleton />)

  // 检查筛选栏骨架
  const filterBarSkeleton = container.querySelector('.bg-card')
  expect(filterBarSkeleton).toBeInTheDocument()

  // 检查网格骨架
  const gridContainer = container.querySelector('.grid')
  expect(gridContainer).toBeInTheDocument()

  // 检查卡片骨架数量（应该有12个）
  const cardSkeletons = container.querySelectorAll('.card-hover')
  expect(cardSkeletons.length).toBe(12)
})

test("renders OverviewTabSkeleton with two cards", () => {
  const { container } = renderWithProviders(<OverviewTabSkeleton />)

  // 检查网格布局
  expect(container.querySelector('.grid')).toBeInTheDocument()
  expect(container.querySelector('.md\\:grid-cols-2')).toBeInTheDocument()

  // 检查卡片数量（应该有2个）
  const cardSkeletons = container.querySelectorAll('.card-hover')
  expect(cardSkeletons.length).toBe(2)

  // 检查每个卡片的内容结构
  const skeletonElements = container.querySelectorAll('.skeleton')
  expect(skeletonElements.length).toBeGreaterThan(10) // 每个卡片有多个骨架元素
})

test("renders VenueTabSkeleton with map container", () => {
  const { container } = renderWithProviders(<VenueTabSkeleton />)

  // 检查地图容器
  expect(container.querySelector('.map-container')).toBeInTheDocument()

  // 检查卡片结构
  expect(container.querySelector('.card-hover')).toBeInTheDocument()

  // 检查高度为400px的地图骨架
  expect(container.querySelector('.h-\\[400px\\]')).toBeInTheDocument()
})

test("renders TravelTabSkeleton with two columns", () => {
  const { container } = renderWithProviders(<TravelTabSkeleton />)

  // 检查网格布局
  expect(container.querySelector('.grid')).toBeInTheDocument()
  expect(container.querySelector('.md\\:grid-cols-2')).toBeInTheDocument()

  // 检查卡片数量（应该有2个）
  const cardSkeletons = container.querySelectorAll('.card-hover')
  expect(cardSkeletons.length).toBe(2)

  // 检查骨架元素
  const skeletonElements = container.querySelectorAll('.skeleton')
  expect(skeletonElements.length).toBeGreaterThan(15) // 两个卡片，每个有多个骨架元素
})

test("all skeleton components apply correct CSS classes", () => {
  const { container: headerContainer } = renderWithProviders(<EventHeaderSkeleton />)
  const { container: tabContainer } = renderWithProviders(<TabContentSkeleton />)
  const { container: overviewContainer } = renderWithProviders(<OverviewTabSkeleton />)
  const { container: venueContainer } = renderWithProviders(<VenueTabSkeleton />)
  const { container: travelContainer } = renderWithProviders(<TravelTabSkeleton />)

  // 检查所有容器都有正确的类
  expect(headerContainer.querySelector('.hero-gradient')).toBeInTheDocument()
  expect(tabContainer.querySelector('.card-hover')).toBeInTheDocument()
  expect(overviewContainer.querySelector('.card-hover')).toBeInTheDocument()
  expect(venueContainer.querySelector('.map-container')).toBeInTheDocument()
  expect(travelContainer.querySelector('.card-hover')).toBeInTheDocument()

  // 检查所有容器都有骨架元素
  ;[headerContainer, tabContainer, overviewContainer, venueContainer, travelContainer].forEach(container => {
    expect(container.querySelectorAll('.skeleton').length).toBeGreaterThan(0)
  })
})

test("skeleton elements have proper structure for accessibility", () => {
  const { container } = renderWithProviders(<EventHeaderSkeleton />)

  // 检查骨架元素的基本结构
  const skeletonElements = container.querySelectorAll('.skeleton')
  
  skeletonElements.forEach(element => {
    // 每个骨架元素都应该有 skeleton 类
    expect(element).toHaveClass('skeleton')
    // 应该有 rounded 类（rounded-md 或其他）
    expect(element.className).toMatch(/rounded/)
  })
})

test("TabContentSkeleton renders correct number of filter badges", () => {
  const { container } = renderWithProviders(<TabContentSkeleton />)

  // 检查筛选标签骨架（应该有6个）
  const filterSection = container.querySelector('.flex.flex-wrap.gap-2')
  if (filterSection) {
    const badgeSkeletons = filterSection.querySelectorAll('.skeleton')
    expect(badgeSkeletons.length).toBe(6)
  }
})

test("TabContentSkeleton renders correct grid structure", () => {
  const { container } = renderWithProviders(<TabContentSkeleton />)

  // 检查网格容器（使用更简单的选择器）
  const gridContainer = container.querySelector('.grid')
  expect(gridContainer).toBeInTheDocument()

  // 检查网格项目数量
  const cardElements = container.querySelectorAll('.card-hover')
  expect(cardElements.length).toBe(12)
})

test("OverviewTabSkeleton renders correct card structure", () => {
  const { container } = renderWithProviders(<OverviewTabSkeleton />)

  const cards = container.querySelectorAll('.card-hover')
  expect(cards.length).toBe(2)

  // 检查每个卡片的内部结构
  cards.forEach(card => {
    // 每个卡片应该有图标骨架
    const iconSkeleton = card.querySelector('.h-5.w-5')
    expect(iconSkeleton).toBeInTheDocument()

    // 每个卡片应该有多个信息行
    const infoRows = card.querySelectorAll('.flex.justify-between')
    expect(infoRows.length).toBe(4)
  })
})
