# 多语言集成指南

> 🌐 **目标**: 实现完整的国际化功能，支持中文、日文、英文三种语言

## 🎯 多语言架构概览

Ayafeed API 支持三种语言检测方式，优先级如下：
1. `X-Locale` 请求头（最高优先级）
2. `locale` Cookie
3. `Accept-Language` 请求头
4. 默认语言（zh）

## 🚀 快速实现

### 1. 语言管理服务
```typescript
// src/services/i18n.ts
export type Locale = 'zh' | 'ja' | 'en';

export interface LocaleConfig {
  code: Locale;
  name: string;
  nativeName: string;
  flag: string;
}

export const SUPPORTED_LOCALES: LocaleConfig[] = [
  { code: 'zh', name: 'Chinese', nativeName: '中文', flag: '🇨🇳' },
  { code: 'ja', name: 'Japanese', nativeName: '日本語', flag: '🇯🇵' },
  { code: 'en', name: 'English', nativeName: 'English', flag: '🇺🇸' },
];

export const DEFAULT_LOCALE: Locale = 'zh';

export class I18nService {
  private currentLocale: Locale = DEFAULT_LOCALE;
  
  constructor() {
    this.currentLocale = this.detectLocale();
  }
  
  // 检测用户语言偏好
  private detectLocale(): Locale {
    // 1. 检查localStorage
    const stored = localStorage.getItem('locale') as Locale;
    if (stored && this.isValidLocale(stored)) {
      return stored;
    }
    
    // 2. 检查浏览器语言
    const browserLang = navigator.language.split('-')[0] as Locale;
    if (this.isValidLocale(browserLang)) {
      return browserLang;
    }
    
    return DEFAULT_LOCALE;
  }
  
  private isValidLocale(locale: string): locale is Locale {
    return SUPPORTED_LOCALES.some(l => l.code === locale);
  }
  
  // 获取当前语言
  getCurrentLocale(): Locale {
    return this.currentLocale;
  }
  
  // 设置语言
  setLocale(locale: Locale): void {
    if (!this.isValidLocale(locale)) {
      console.warn(`Unsupported locale: ${locale}`);
      return;
    }
    
    this.currentLocale = locale;
    localStorage.setItem('locale', locale);
    
    // 设置Cookie（7天过期）
    document.cookie = `locale=${locale}; path=/; max-age=${7 * 24 * 60 * 60}`;
    
    // 触发语言变更事件
    window.dispatchEvent(new CustomEvent('localeChange', { 
      detail: { locale } 
    }));
  }
  
  // 获取语言配置
  getLocaleConfig(locale?: Locale): LocaleConfig {
    const targetLocale = locale || this.currentLocale;
    return SUPPORTED_LOCALES.find(l => l.code === targetLocale) || SUPPORTED_LOCALES[0];
  }
  
  // 格式化日期
  formatDate(date: string | Date, options?: Intl.DateTimeFormatOptions): string {
    const dateObj = typeof date === 'string' ? new Date(date) : date;
    return new Intl.DateTimeFormat(this.getLocaleForIntl(), options).format(dateObj);
  }
  
  // 格式化数字
  formatNumber(number: number, options?: Intl.NumberFormatOptions): string {
    return new Intl.NumberFormat(this.getLocaleForIntl(), options).format(number);
  }
  
  private getLocaleForIntl(): string {
    const localeMap = {
      'zh': 'zh-CN',
      'ja': 'ja-JP',
      'en': 'en-US',
    };
    return localeMap[this.currentLocale];
  }
}

export const i18nService = new I18nService();
```

### 2. API客户端集成
```typescript
// src/lib/api/i18n.ts
import { api } from './client';
import { i18nService } from '@/services/i18n';

// 设置API客户端的语言
export function setupApiI18n() {
  // 初始设置
  updateApiLocale(i18nService.getCurrentLocale());
  
  // 监听语言变更
  window.addEventListener('localeChange', (event: any) => {
    updateApiLocale(event.detail.locale);
  });
}

function updateApiLocale(locale: string) {
  api.configure({
    init: {
      headers: {
        'X-Locale': locale,
        'Content-Type': 'application/json',
      },
    },
  });
}

// 在应用启动时调用
setupApiI18n();
```

### 3. React状态管理
```typescript
// src/stores/i18n.ts
import { create } from 'zustand';
import { i18nService, type Locale } from '@/services/i18n';

interface I18nState {
  locale: Locale;
  isLoading: boolean;
  
  // Actions
  setLocale: (locale: Locale) => void;
  getLocaleConfig: () => ReturnType<typeof i18nService.getLocaleConfig>;
}

export const useI18nStore = create<I18nState>((set, get) => ({
  locale: i18nService.getCurrentLocale(),
  isLoading: false,
  
  setLocale: (locale: Locale) => {
    set({ isLoading: true });
    
    try {
      i18nService.setLocale(locale);
      set({ locale, isLoading: false });
    } catch (error) {
      console.error('Failed to set locale:', error);
      set({ isLoading: false });
    }
  },
  
  getLocaleConfig: () => {
    return i18nService.getLocaleConfig(get().locale);
  },
}));
```

### 4. 语言切换组件
```typescript
// src/components/LanguageSwitcher.tsx
import React from 'react';
import { useI18nStore } from '@/stores/i18n';
import { SUPPORTED_LOCALES } from '@/services/i18n';

export function LanguageSwitcher() {
  const { locale, setLocale, isLoading } = useI18nStore();
  
  return (
    <div className="relative">
      <select
        value={locale}
        onChange={(e) => setLocale(e.target.value as any)}
        disabled={isLoading}
        className="appearance-none bg-white border border-gray-300 rounded px-3 py-1 pr-8"
      >
        {SUPPORTED_LOCALES.map((localeConfig) => (
          <option key={localeConfig.code} value={localeConfig.code}>
            {localeConfig.flag} {localeConfig.nativeName}
          </option>
        ))}
      </select>
      
      {isLoading && (
        <div className="absolute right-2 top-1/2 transform -translate-y-1/2">
          <div className="animate-spin h-4 w-4 border-2 border-blue-500 border-t-transparent rounded-full"></div>
        </div>
      )}
    </div>
  );
}
```

## 🔧 高级功能

### 1. 内容本地化Hook
```typescript
// src/hooks/useLocalization.ts
import { useI18nStore } from '@/stores/i18n';
import { i18nService } from '@/services/i18n';

export function useLocalization() {
  const { locale } = useI18nStore();
  
  return {
    locale,
    formatDate: (date: string | Date, options?: Intl.DateTimeFormatOptions) => 
      i18nService.formatDate(date, options),
    
    formatNumber: (number: number, options?: Intl.NumberFormatOptions) => 
      i18nService.formatNumber(number, options),
    
    formatRelativeTime: (date: string | Date) => {
      const now = new Date();
      const target = typeof date === 'string' ? new Date(date) : date;
      const diffInSeconds = Math.floor((now.getTime() - target.getTime()) / 1000);
      
      const rtf = new Intl.RelativeTimeFormat(i18nService.getLocaleForIntl(), {
        numeric: 'auto'
      });
      
      if (diffInSeconds < 60) return rtf.format(-diffInSeconds, 'second');
      if (diffInSeconds < 3600) return rtf.format(-Math.floor(diffInSeconds / 60), 'minute');
      if (diffInSeconds < 86400) return rtf.format(-Math.floor(diffInSeconds / 3600), 'hour');
      return rtf.format(-Math.floor(diffInSeconds / 86400), 'day');
    },
    
    // 获取本地化的文本
    getLocalizedText: (textObj: Record<string, string>) => {
      return textObj[locale] || textObj['zh'] || textObj['en'] || Object.values(textObj)[0];
    },
  };
}
```

### 2. 多语言数据处理
```typescript
// src/utils/localization.ts
import type { Locale } from '@/services/i18n';

// 多语言文本对象类型
export interface MultilingualText {
  zh?: string;
  ja?: string;
  en?: string;
}

// 从API响应中提取本地化文本
export function extractLocalizedText(
  textObj: MultilingualText | string,
  locale: Locale,
  fallback: string = ''
): string {
  if (typeof textObj === 'string') {
    return textObj;
  }
  
  if (!textObj) {
    return fallback;
  }
  
  // 按优先级返回文本
  return textObj[locale] || 
         textObj['zh'] || 
         textObj['en'] || 
         textObj['ja'] || 
         fallback;
}

// 处理API响应中的多语言字段
export function processMultilingualResponse<T extends Record<string, any>>(
  data: T,
  locale: Locale,
  multilingualFields: (keyof T)[]
): T {
  const processed = { ...data };
  
  multilingualFields.forEach(field => {
    if (processed[field] && typeof processed[field] === 'object') {
      processed[field] = extractLocalizedText(
        processed[field] as MultilingualText,
        locale,
        ''
      );
    }
  });
  
  return processed;
}
```

### 3. 实际使用示例
```typescript
// src/components/EventCard.tsx
import React from 'react';
import { useLocalization } from '@/hooks/useLocalization';
import { extractLocalizedText } from '@/utils/localization';

interface Event {
  id: string;
  name: string | { zh?: string; ja?: string; en?: string };
  description: string | { zh?: string; ja?: string; en?: string };
  date: string;
  venue_name: string;
}

interface EventCardProps {
  event: Event;
}

export function EventCard({ event }: EventCardProps) {
  const { locale, formatDate, getLocalizedText } = useLocalization();
  
  // 处理多语言文本
  const eventName = typeof event.name === 'string' 
    ? event.name 
    : getLocalizedText(event.name);
    
  const eventDescription = typeof event.description === 'string'
    ? event.description
    : getLocalizedText(event.description);
  
  return (
    <div className="bg-white rounded-lg shadow-md p-6">
      <h3 className="text-xl font-bold mb-2">{eventName}</h3>
      <p className="text-gray-600 mb-4">{eventDescription}</p>
      
      <div className="flex justify-between items-center text-sm text-gray-500">
        <span>{event.venue_name}</span>
        <span>{formatDate(event.date, { 
          year: 'numeric', 
          month: 'short', 
          day: 'numeric' 
        })}</span>
      </div>
    </div>
  );
}
```

## 🎨 UI本地化

### 1. 静态文本翻译
```typescript
// src/locales/index.ts
export const translations = {
  zh: {
    common: {
      loading: '加载中...',
      error: '出错了',
      retry: '重试',
      cancel: '取消',
      confirm: '确认',
      save: '保存',
      delete: '删除',
      edit: '编辑',
      search: '搜索',
      filter: '筛选',
      more: '更多',
    },
    navigation: {
      home: '首页',
      events: '活动',
      circles: '社团',
      artists: '作者',
      search: '搜索',
      profile: '个人资料',
      admin: '管理',
    },
    auth: {
      login: '登录',
      logout: '退出',
      register: '注册',
      username: '用户名',
      password: '密码',
      email: '邮箱',
      loginSuccess: '登录成功',
      loginFailed: '登录失败',
    },
  },
  ja: {
    common: {
      loading: '読み込み中...',
      error: 'エラーが発生しました',
      retry: '再試行',
      cancel: 'キャンセル',
      confirm: '確認',
      save: '保存',
      delete: '削除',
      edit: '編集',
      search: '検索',
      filter: 'フィルター',
      more: 'もっと見る',
    },
    navigation: {
      home: 'ホーム',
      events: 'イベント',
      circles: 'サークル',
      artists: '作者',
      search: '検索',
      profile: 'プロフィール',
      admin: '管理',
    },
    auth: {
      login: 'ログイン',
      logout: 'ログアウト',
      register: '登録',
      username: 'ユーザー名',
      password: 'パスワード',
      email: 'メール',
      loginSuccess: 'ログイン成功',
      loginFailed: 'ログイン失敗',
    },
  },
  en: {
    common: {
      loading: 'Loading...',
      error: 'An error occurred',
      retry: 'Retry',
      cancel: 'Cancel',
      confirm: 'Confirm',
      save: 'Save',
      delete: 'Delete',
      edit: 'Edit',
      search: 'Search',
      filter: 'Filter',
      more: 'More',
    },
    navigation: {
      home: 'Home',
      events: 'Events',
      circles: 'Circles',
      artists: 'Artists',
      search: 'Search',
      profile: 'Profile',
      admin: 'Admin',
    },
    auth: {
      login: 'Login',
      logout: 'Logout',
      register: 'Register',
      username: 'Username',
      password: 'Password',
      email: 'Email',
      loginSuccess: 'Login successful',
      loginFailed: 'Login failed',
    },
  },
};

export type TranslationKey = keyof typeof translations.zh;
```

### 2. 翻译Hook
```typescript
// src/hooks/useTranslation.ts
import { useI18nStore } from '@/stores/i18n';
import { translations } from '@/locales';

export function useTranslation() {
  const { locale } = useI18nStore();
  
  const t = (key: string, params?: Record<string, string | number>) => {
    const keys = key.split('.');
    let value: any = translations[locale];
    
    for (const k of keys) {
      value = value?.[k];
    }
    
    if (typeof value !== 'string') {
      console.warn(`Translation missing for key: ${key} in locale: ${locale}`);
      return key;
    }
    
    // 简单的参数替换
    if (params) {
      return Object.entries(params).reduce(
        (str, [paramKey, paramValue]) => 
          str.replace(`{{${paramKey}}}`, String(paramValue)),
        value
      );
    }
    
    return value;
  };
  
  return { t, locale };
}
```

### 3. 使用示例
```typescript
// src/components/SearchForm.tsx
import React, { useState } from 'react';
import { useTranslation } from '@/hooks/useTranslation';

export function SearchForm() {
  const { t } = useTranslation();
  const [query, setQuery] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  
  return (
    <form className="flex gap-2">
      <input
        type="text"
        value={query}
        onChange={(e) => setQuery(e.target.value)}
        placeholder={t('common.search')}
        className="flex-1 px-3 py-2 border rounded"
      />
      <button
        type="submit"
        disabled={isLoading}
        className="px-4 py-2 bg-blue-500 text-white rounded disabled:opacity-50"
      >
        {isLoading ? t('common.loading') : t('common.search')}
      </button>
    </form>
  );
}
```

## 🔍 调试和测试

### 语言切换测试
```typescript
// src/utils/__tests__/i18n.test.ts
import { i18nService } from '@/services/i18n';

describe('I18nService', () => {
  beforeEach(() => {
    localStorage.clear();
    document.cookie = '';
  });
  
  it('should detect default locale', () => {
    expect(i18nService.getCurrentLocale()).toBe('zh');
  });
  
  it('should set and persist locale', () => {
    i18nService.setLocale('ja');
    expect(i18nService.getCurrentLocale()).toBe('ja');
    expect(localStorage.getItem('locale')).toBe('ja');
  });
  
  it('should format date correctly', () => {
    i18nService.setLocale('ja');
    const date = new Date('2024-01-01');
    const formatted = i18nService.formatDate(date);
    expect(formatted).toMatch(/2024/);
  });
});
```

---

**下一步**: 查看 [错误处理指南](./error-handling.md) 学习统一的错误处理方案 ⚠️
