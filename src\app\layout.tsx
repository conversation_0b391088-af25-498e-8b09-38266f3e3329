import type { Metadata } from "next";

import "./globals.css";
import Navbar from "@/components/navbar";
import ReactQueryProvider from "@/components/react-query-provider";
import { AuthProvider } from "@/contexts/user";
import { Toaster } from "sonner";

import { NextIntlClientProvider } from 'next-intl'
import { getLocale } from 'next-intl/server'

export const metadata: Metadata = {
  title: "Ayafeed",
  description: "同人展会信息聚合平台",
};

export default async function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  const locale = await getLocale()
  return (
    <html lang={locale}>
      <body className="antialiased">
        <NextIntlClientProvider>
          <ReactQueryProvider>
            <AuthProvider>
              <Navbar />
              {children}
              <Toaster
                position="top-center"
                richColors
                closeButton={false}
                duration={3000}
                expand={true}
                visibleToasts={5}
                toastOptions={{
                  style: {
                    textAlign: 'center',
                    justifyContent: 'center',
                    alignItems: 'center',
                    display: 'flex'
                  }
                }}
              />
            </AuthProvider>
          </ReactQueryProvider>
        </NextIntlClientProvider>
      </body>
    </html>
  );
}
