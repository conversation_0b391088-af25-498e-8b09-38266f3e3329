'use client';

import { useState } from 'react';
import { useZodForm } from '@/hooks/useZodForm';
import { MultilingualEventInputSchema } from '@/schemas/event';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import VenueSelector from '@/components/admin/VenueSelector';

export default function FormTestPage() {
  const [submitResult, setSubmitResult] = useState<string>('');
  
  const form = useZodForm({
    schema: MultilingualEventInputSchema,
    defaultValues: {
      name_en: '',
      name_ja: '',
      name_zh: '',
      date_en: '',
      date_ja: '',
      date_zh: '',
      venue_id: '',
      image_url: '',
      url: '',
    },
  });

  const { register, handleSubmit, formState: { errors }, setValue, watch } = form;
  const venueId = watch('venue_id');

  const onSubmit = (data: any) => {
    console.log('表单提交成功！', data);
    setSubmitResult(`✅ 表单提交成功！数据: ${JSON.stringify(data, null, 2)}`);
  };

  const onError = (errors: any) => {
    console.log('表单验证失败：', errors);
    setSubmitResult(`❌ 表单验证失败: ${JSON.stringify(errors, null, 2)}`);
  };

  return (
    <div className="container mx-auto py-8 max-w-4xl">
      <Card>
        <CardHeader>
          <CardTitle>Admin Events 表单测试</CardTitle>
          <p className="text-sm text-muted-foreground">
            测试修复后的表单提交功能
          </p>
        </CardHeader>
        <CardContent>
          <form onSubmit={handleSubmit(onSubmit, onError)} className="space-y-6">
            {/* 英文名称 */}
            <div>
              <Label htmlFor="name_en">英文名称 *</Label>
              <Input
                id="name_en"
                {...register('name_en')}
                placeholder="Event Name in English"
              />
              {errors.name_en && (
                <p className="text-sm text-destructive mt-1">{errors.name_en.message}</p>
              )}
            </div>

            {/* 日文名称 */}
            <div>
              <Label htmlFor="name_ja">日文名称 *</Label>
              <Input
                id="name_ja"
                {...register('name_ja')}
                placeholder="イベント名（日本語）"
              />
              {errors.name_ja && (
                <p className="text-sm text-destructive mt-1">{errors.name_ja.message}</p>
              )}
            </div>

            {/* 中文名称 */}
            <div>
              <Label htmlFor="name_zh">中文名称 *</Label>
              <Input
                id="name_zh"
                {...register('name_zh')}
                placeholder="活动名称（中文）"
              />
              {errors.name_zh && (
                <p className="text-sm text-destructive mt-1">{errors.name_zh.message}</p>
              )}
            </div>

            {/* 英文日期 */}
            <div>
              <Label htmlFor="date_en">英文日期 *</Label>
              <Input
                id="date_en"
                {...register('date_en')}
                placeholder="March 15, 2025 (Sat) 10:00 - 18:00"
              />
              {errors.date_en && (
                <p className="text-sm text-destructive mt-1">{errors.date_en.message}</p>
              )}
            </div>

            {/* 日文日期 */}
            <div>
              <Label htmlFor="date_ja">日文日期 *</Label>
              <Input
                id="date_ja"
                {...register('date_ja')}
                placeholder="2025年3月15日(土) 10:00 - 18:00"
              />
              {errors.date_ja && (
                <p className="text-sm text-destructive mt-1">{errors.date_ja.message}</p>
              )}
            </div>

            {/* 中文日期 */}
            <div>
              <Label htmlFor="date_zh">中文日期 *</Label>
              <Input
                id="date_zh"
                {...register('date_zh')}
                placeholder="2025年3月15日(周六) 10:00 - 18:00"
              />
              {errors.date_zh && (
                <p className="text-sm text-destructive mt-1">{errors.date_zh.message}</p>
              )}
            </div>

            {/* 场馆选择 */}
            <div>
              <Label>场馆 *</Label>
              <VenueSelector
                value={venueId}
                onValueChange={(value) => setValue('venue_id', value)}
                placeholder="选择展会场馆..."
              />
              {errors.venue_id && (
                <p className="text-sm text-destructive mt-1">{errors.venue_id.message}</p>
              )}
            </div>

            {/* 图片URL */}
            <div>
              <Label htmlFor="image_url">图片URL</Label>
              <Input
                id="image_url"
                {...register('image_url')}
                placeholder="https://example.com/image.jpg"
              />
              {errors.image_url && (
                <p className="text-sm text-destructive mt-1">{errors.image_url.message}</p>
              )}
            </div>

            {/* 官网URL */}
            <div>
              <Label htmlFor="url">官网URL</Label>
              <Input
                id="url"
                {...register('url')}
                placeholder="https://event-website.com"
              />
              {errors.url && (
                <p className="text-sm text-destructive mt-1">{errors.url.message}</p>
              )}
            </div>

            {/* 提交按钮 */}
            <div className="flex gap-4">
              <Button type="submit" className="flex-1">
                🚀 测试提交
              </Button>
              <Button 
                type="button" 
                variant="outline" 
                onClick={() => {
                  form.reset();
                  setSubmitResult('');
                }}
              >
                重置表单
              </Button>
            </div>
          </form>

          {/* 提交结果显示 */}
          {submitResult && (
            <div className="mt-6 p-4 bg-muted rounded-lg">
              <h3 className="font-medium mb-2">提交结果：</h3>
              <pre className="text-sm whitespace-pre-wrap">{submitResult}</pre>
            </div>
          )}

          {/* 调试信息 */}
          <div className="mt-6 p-4 bg-muted rounded-lg">
            <h3 className="font-medium mb-2">调试信息：</h3>
            <div className="text-sm space-y-2">
              <p><strong>当前 venue_id:</strong> {venueId || '未选择'}</p>
              <p><strong>表单错误:</strong> {Object.keys(errors).length > 0 ? JSON.stringify(errors, null, 2) : '无错误'}</p>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
