"use client";

// mock sonner toast 必须在其它导入之前
vi.mock("sonner", () => ({
  toast: {
    error: vi.fn(),
    success: vi.fn(),
  },
}));

import { useRouter } from "next/navigation";
import React from "react";
import type { Mock } from "vitest";
import { toast } from "sonner";

import AdminUsersPage from "../page";

import { request } from "@/lib/http";
import { showApiError } from "@/lib/show-error";
import {
  renderWithProviders,
  screen,
  waitFor,
  fireEvent,
} from "@test/test-utils";

// Mock next/navigation
vi.mock("next/navigation", () => ({
  useRouter: vi.fn(),
}));

vi.mock("@/lib/http", () => ({
  request: vi.fn(),
}));

// 新增：mock show-error 以便捕获 onError 调用
vi.mock("@/lib/show-error", () => ({
  showApiError: vi.fn(),
}));

const mockPush = vi.fn();
const mockUsers = [
  { id: "u1", username: "alice", role: "viewer" },
  { id: "u2", username: "bob", role: "editor" },
];

describe("AdminUsersPage", () => {
  beforeEach(() => {
    vi.resetAllMocks();
    (useRouter as unknown as Mock).mockReturnValue({ push: mockPush });
  });

  test("should show loading state and then display users", async () => {
    (request as Mock).mockResolvedValue(mockUsers);

    renderWithProviders(<AdminUsersPage />);

    expect(screen.getByText("加载中...")).toBeInTheDocument();

    await waitFor(() => {
      expect(screen.getByText("alice")).toBeInTheDocument();
      expect(screen.getByText("bob")).toBeInTheDocument();
    });
  });

  test("should navigate to new user page on button click", async () => {
    (request as Mock).mockResolvedValue([]);
    renderWithProviders(<AdminUsersPage />);

    await waitFor(() => fireEvent.click(screen.getByText("新增用户")));

    expect(mockPush).toHaveBeenCalledWith("/admin/users/new");
  });

  test("should call delete handler and refetch users", async () => {
    (request as Mock).mockResolvedValue(mockUsers);
    renderWithProviders(<AdminUsersPage />);

    await waitFor(() => expect(screen.getByText("alice")).toBeInTheDocument());

    window.confirm = vi.fn(() => true) as any;
    (request as Mock)
      .mockResolvedValueOnce({}) // DELETE 成功
      .mockResolvedValueOnce([mockUsers[1]]); // 重新获取列表

    const deleteBtn = screen.getAllByText("删除")[0];
    fireEvent.click(deleteBtn);

    expect(window.confirm).toHaveBeenCalledWith("确定删除该用户吗？");

    await waitFor(() => {
      expect(request).toHaveBeenCalledWith("/admin/users/u1", { method: "DELETE" });
    });

    // 等待重新获取列表完成
    await waitFor(() => {
      expect(request).toHaveBeenCalledWith("/admin/users");
    });

    // 验证 UI 更新
    await waitFor(() => {
      expect(screen.queryByText("alice")).not.toBeInTheDocument();
      expect(screen.getByText("bob")).toBeInTheDocument();
    });
  });

  test("should show toast on delete failure", async () => {
    (request as Mock).mockResolvedValue(mockUsers);
    renderWithProviders(<AdminUsersPage />);

    await waitFor(() => expect(screen.getByText("alice")).toBeInTheDocument());

    // 模拟删除失败
    window.confirm = vi.fn(() => true) as any;
    const error = { code: 500, message: "删除失败" };
    (request as Mock).mockRejectedValueOnce(error);

    const deleteBtn = screen.getAllByText("删除")[0];
    fireEvent.click(deleteBtn);

    await waitFor(() => {
      expect(showApiError).toHaveBeenCalledWith(error, "删除失败");
    });
  });
}); 