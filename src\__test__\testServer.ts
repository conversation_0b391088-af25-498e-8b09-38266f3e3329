// @ts-nocheck
 
import { http } from "msw"
import { setupServer } from "msw/node"

/**
 * MSW 服务器
 * 
 * 默认拦截 /auth/me 请求，返回访客用户，避免 AuthProvider 校验时触发 401 重定向，
 * 同时消除未匹配请求警告。
 * 其他接口可在具体测试文件内通过 `server.use()` 动态添加处理程序。
 */

// 模拟事件数据，匹配实际的后端 API 响应格式
const mockEventData = {
  id: "reitaisai-22",
  name: "Reitaisai 22",
  date: "May 3, 2025 (Sat) 10:30 – 15:30",
  date_sort: 20250503,
  image_url: "/images/events/reitaisai-22/thumb.jpg",
  venue_name: "Tokyo Big Sight",
  venue_address: "3-11-1 Ariake, Koto City, Tokyo",
  venue_lat: 35.6298,
  venue_lng: 139.793,
  url: "https://reitaisai.com/rts22/",
  created_at: "2025-07-25T06:14:26.675Z",
  updated_at: "2025-07-25T06:14:26.675Z"
}

const handlers = [
  http.get("http://127.0.0.1:8787/auth/me", (_req, res, ctx) => {
    return res(
      ctx.status(200),
      ctx.json({ id: "guest", username: "Guest", role: "viewer" })
    )
  }),
  // 添加事件详情的默认 handler
  http.get("http://127.0.0.1:8787/events/:id", (_req, res, ctx) => {
    return res(
      ctx.status(200),
      ctx.json(mockEventData)
    )
  }),
]

export const server = setupServer(...handlers)
export { http } 