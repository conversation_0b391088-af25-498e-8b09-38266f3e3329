/**
 * Venue集成测试
 * 测试新的venue关联结构的兼容性
 */

import { describe, it, expect } from 'vitest'
import { createVenueFromEvent, DEFAULT_VENUE } from '../types'
import type { Event } from '@/schemas/event'

describe('Venue Integration Tests', () => {
  describe('createVenueFromEvent', () => {
    it('应该处理null event', () => {
      const result = createVenueFromEvent(null)
      expect(result).toEqual(DEFAULT_VENUE)
    })

    it('应该处理新的venue关联结构', () => {
      const event: Event = {
        id: 'test-event',
        name: 'Test Event',
        date: '2025-05-03',
        date_sort: 20250503,
        venue_id: 'tokyo-big-sight',
        venue: {
          id: 'tokyo-big-sight',
          name: 'Tokyo Big Sight',
          address: '东京都江东区有明3-11-1',
          lat: 35.6298,
          lng: 139.7976,
        }
      }

      const result = createVenueFromEvent(event)
      expect(result).toEqual({
        id: 'tokyo-big-sight',
        name: 'Tokyo Big Sight',
        address: '东京都江东区有明3-11-1',
        lat: 35.6298,
        lng: 139.7976,
      })
    })

    it('应该兼容旧的venue字段结构', () => {
      const event: Event = {
        id: 'test-event',
        name: 'Test Event',
        date: '2025-05-03',
        date_sort: 20250503,
        venue_name: 'Tokyo Big Sight',
        venue_address: '东京都江东区有明3-11-1',
        venue_lat: 35.6298,
        venue_lng: 139.7976,
      }

      const result = createVenueFromEvent(event)
      expect(result).toEqual({
        id: 'test-event',
        name: 'Tokyo Big Sight',
        address: '东京都江东区有明3-11-1',
        lat: 35.6298,
        lng: 139.7976,
      })
    })

    it('应该优先使用新的venue结构', () => {
      const event: Event = {
        id: 'test-event',
        name: 'Test Event',
        date: '2025-05-03',
        date_sort: 20250503,
        // 新结构
        venue_id: 'tokyo-big-sight',
        venue: {
          id: 'tokyo-big-sight',
          name: 'New Venue Name',
          address: 'New Address',
          lat: 35.6298,
          lng: 139.7976,
        },
        // 旧结构（应该被忽略）
        venue_name: 'Old Venue Name',
        venue_address: 'Old Address',
        venue_lat: 35.0000,
        venue_lng: 139.0000,
      }

      const result = createVenueFromEvent(event)
      expect(result).toEqual({
        id: 'tokyo-big-sight',
        name: 'New Venue Name',
        address: 'New Address',
        lat: 35.6298,
        lng: 139.7976,
      })
    })

    it('应该处理不完整的venue信息', () => {
      const event: Event = {
        id: 'test-event',
        name: 'Test Event',
        date: '2025-05-03',
        date_sort: 20250503,
        venue_name: 'Some Venue',
        // 缺少lat/lng
      }

      const result = createVenueFromEvent(event)
      expect(result).toEqual(DEFAULT_VENUE)
    })

    it('应该处理venue信息缺失的情况', () => {
      const event: Event = {
        id: 'test-event',
        name: 'Test Event',
        date: '2025-05-03',
        date_sort: 20250503,
        venue_id: 'some-venue',
        venue: {
          id: 'some-venue',
          name: '',
          lat: 0,
          lng: 0,
        }
      }

      const result = createVenueFromEvent(event)
      expect(result).toEqual({
        id: 'some-venue',
        name: DEFAULT_VENUE.name, // 使用默认名称
        address: DEFAULT_VENUE.address, // 使用默认地址
        lat: DEFAULT_VENUE.lat, // 使用默认坐标
        lng: DEFAULT_VENUE.lng,
      })
    })
  })

  describe('Type Compatibility', () => {
    it('Event schema应该支持新的venue字段', () => {
      const event: Event = {
        id: 'test',
        name: 'Test',
        date: '2025-05-03',
        date_sort: 20250503,
        venue_id: 'venue-1',
        venue: {
          id: 'venue-1',
          name: 'Test Venue',
          lat: 35.6298,
          lng: 139.7976,
        }
      }

      // 应该能够正常编译，没有类型错误
      expect(event.venue_id).toBe('venue-1')
      expect(event.venue?.name).toBe('Test Venue')
    })

    it('Event schema应该兼容旧的venue字段', () => {
      const event: Event = {
        id: 'test',
        name: 'Test',
        date: '2025-05-03',
        date_sort: 20250503,
        venue_name: 'Test Venue',
        venue_lat: 35.6298,
        venue_lng: 139.7976,
      }

      // 应该能够正常编译，没有类型错误
      expect(event.venue_name).toBe('Test Venue')
      expect(event.venue_lat).toBe(35.6298)
    })
  })
})
