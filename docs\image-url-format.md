# Event封面图路径格式说明

## 📋 概述

Event表单中的"封面图路径"字段使用相对路径格式，系统会在显示时自动拼接完整的URL。

## 🔧 路径格式

### 推荐格式
```
/images/events/{event-id}/thumb.jpg
```

### 示例
```
/images/events/reitaisai-22/thumb.jpg
/images/events/comiket-105/poster.png
/images/events/summer-festival-2025/banner.webp
```

## 🌐 URL拼接逻辑

### 前端显示时的拼接
```typescript
// 在前端组件中
const fullImageUrl = `${process.env.NEXT_PUBLIC_CDN_BASE_URL}${event.image_url}`;

// 或者使用工具函数
const fullImageUrl = getImageUrl(event.image_url);
```

### 环境变量配置
```env
# .env.local
NEXT_PUBLIC_CDN_BASE_URL=https://cdn.ayafeed.com
```

### 最终生成的URL
```
输入: /images/events/reitaisai-22/thumb.jpg
输出: https://cdn.ayafeed.com/images/events/reitaisai-22/thumb.jpg
```

## 📁 目录结构建议

### 推荐的图片目录结构
```
images/
├── events/
│   ├── reitaisai-22/
│   │   ├── thumb.jpg          # 缩略图
│   │   ├── poster.jpg         # 海报
│   │   └── banner.jpg         # 横幅
│   ├── comiket-105/
│   │   ├── thumb.jpg
│   │   └── poster.png
│   └── summer-festival-2025/
│       ├── thumb.webp
│       └── hero.jpg
├── venues/
│   ├── tokyo-big-sight/
│   │   ├── exterior.jpg
│   │   └── interior.jpg
│   └── makuhari-messe/
│       └── hall.jpg
└── circles/
    ├── circle-a/
    │   └── logo.png
    └── circle-b/
        └── avatar.jpg
```

## 🎨 图片规格建议

### Event封面图
- **缩略图 (thumb)**: 400x300px, JPEG/WebP
- **海报 (poster)**: 800x1200px, JPEG/PNG
- **横幅 (banner)**: 1200x400px, JPEG/WebP

### 文件大小
- 缩略图: < 100KB
- 海报: < 500KB
- 横幅: < 300KB

## 💡 使用示例

### 在Event表单中
```
封面图路径: /images/events/reitaisai-22/thumb.jpg
```

### 在前端组件中显示
```tsx
function EventCard({ event }: { event: Event }) {
  const imageUrl = getImageUrl(event.image_url);
  
  return (
    <div className="event-card">
      <img 
        src={imageUrl} 
        alt={event.name_en}
        className="w-full h-48 object-cover"
      />
      <h3>{event.name_zh}</h3>
    </div>
  );
}
```

### 工具函数示例
```typescript
// src/lib/image-utils.ts
export function getImageUrl(relativePath?: string): string {
  if (!relativePath) {
    return '/images/placeholder/event-default.jpg';
  }
  
  const baseUrl = process.env.NEXT_PUBLIC_CDN_BASE_URL || '';
  return `${baseUrl}${relativePath}`;
}

export function getEventImageUrl(eventId: string, filename: string = 'thumb.jpg'): string {
  return `/images/events/${eventId}/${filename}`;
}
```

## 🔄 迁移现有数据

### 如果现有数据使用完整URL
```typescript
// 迁移脚本示例
function migrateImageUrls(events: Event[]) {
  return events.map(event => {
    if (event.image_url?.startsWith('https://')) {
      // 提取相对路径部分
      const url = new URL(event.image_url);
      event.image_url = url.pathname;
    }
    return event;
  });
}
```

## ✅ 验证清单

在使用封面图路径时，请确保：

- [ ] 路径以 `/` 开头
- [ ] 使用正确的文件扩展名 (.jpg, .png, .webp)
- [ ] 路径中不包含域名
- [ ] 文件确实存在于CDN中
- [ ] 图片尺寸符合规格要求
- [ ] 文件大小在合理范围内

## 🚨 常见错误

### ❌ 错误格式
```
https://cdn.ayafeed.com/images/events/event/thumb.jpg  # 包含域名
images/events/event/thumb.jpg                          # 缺少开头的 /
/images/events/event/thumb                             # 缺少文件扩展名
```

### ✅ 正确格式
```
/images/events/reitaisai-22/thumb.jpg
/images/events/comiket-105/poster.png
/images/events/summer-fest/banner.webp
```

## 🔗 相关文档

- [CDN配置文档](./cdn-setup.md)
- [图片优化指南](./image-optimization.md)
- [Event模块文档](./event-module-guide.md)
