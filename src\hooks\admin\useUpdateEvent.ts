import { useQueryClient } from "@tanstack/react-query";
import { toast } from "sonner";

import {
  usePatchAdminEventsId,
  type PatchAdminEventsIdRequestBody
} from "@/api/generated/ayafeedComponents";
import { queryKeys } from "@/constants/queryKeys";
import type { MultilingualEventInput } from "@/schemas/event";

/**
 * 将表单数据转换为 API 更新请求格式
 */
function transformEventInputToUpdateApiFormat(input: Partial<MultilingualEventInput>): PatchAdminEventsIdRequestBody {
  const result: PatchAdminEventsIdRequestBody = {};

  // 只包含有值的字段
  if (input.name_en !== undefined) result.name_en = input.name_en;
  if (input.name_ja !== undefined) result.name_ja = input.name_ja;
  if (input.name_zh !== undefined) result.name_zh = input.name_zh;
  if (input.date_en !== undefined) result.date_en = input.date_en;
  if (input.date_ja !== undefined) result.date_ja = input.date_ja;
  if (input.date_zh !== undefined) result.date_zh = input.date_zh;
  if (input.date_sort !== undefined) result.date_sort = input.date_sort;
  if (input.image_url !== undefined) result.image_url = input.image_url || null;
  if (input.venue_id !== undefined) result.venue_id = input.venue_id;
  if (input.url !== undefined) result.url = input.url || null;

  return result;
}

export function useUpdateEvent(id: string) {
  const qc = useQueryClient();

  // 使用生成的 API hook
  const updateEventMutation = usePatchAdminEventsId({
    onSuccess: () => {
      qc.invalidateQueries({ queryKey: queryKeys.adminEvents() });
      qc.invalidateQueries({ queryKey: queryKeys.adminEventDetail(id) });
      qc.invalidateQueries({ predicate: ({ queryKey }) => Array.isArray(queryKey) && queryKey[0] === "events" });
    },
  });

  return {
    ...updateEventMutation,
    mutate: (input: Partial<MultilingualEventInput>, options?: any) => {
      const apiPayload = transformEventInputToUpdateApiFormat(input);
      updateEventMutation.mutate(
        {
          pathParams: { id },
          body: apiPayload
        },
        {
          ...options,
          onSuccess: (data, variables, context) => {
            // 显示成功提示
            toast.success('展会更新成功');
            options?.onSuccess?.(data, variables, context);
          },
          onError: (error, variables, context) => {
            // 显示错误提示
            toast.error('展会更新失败');
            console.error('修改失败:', error);
            options?.onError?.(error, variables, context);
          }
        }
      );
    }
  };
}