---
id: next-project
title: 下一个项目方案
sidebar_label: 下一个项目
sidebar_position: 100
description: PaperBrief 项目方案 - 学术论文速读助手
keywords: [paperbrief, 项目方案, 学术论文, 速读助手]
---

# PaperBrief 项目方案（Vite SPA 版）

## 1. 项目简介
> **PaperBrief** 是一款学术论文速读助手，用户上传或粘贴 arXiv 链接即可在数秒内获得 200 字核心摘要、关键图表缩略图，并可进一步通过聊天问答深入了解论文细节。

## 2. 核心功能
1. **PDF 上传 / arXiv 链接解析**：自动下载 PDF 与元数据。
2. **渐进式摘要渲染**：先返回论文题目与作者，再逐步展示方法、结果、贡献三栏卡片。
3. **图表 OCR**：检测并裁剪论文中的图表，提取 caption 与文本。 
4. **RAG 问答**：根据向量检索 + BM25 Hybrid，为用户问题提供带引用的回答。
5. **收藏与分享**：支持保存摘要到本地 IndexedDB，并生成可分享链接。

## 3. 系统架构概览
```
        +-----------------------+
        |       Vite SPA        |
        |  React 19 + TSX +    |
        |  Tailwind + shadcn   |
        +----------+-----------+
                   |  SSE / REST
                   v
        +-----------------------+
        |  FastAPI Backend      |
        |  • Ingest Worker      |
        |  • Chat API (RAG)     |
        +----------+-----------+
                   |  gRPC / HTTP
                   v
        +-----------------------+
        |  Worker Pool (Celery) |
        |  • PDF 解析           |
        |  • 图表 OCR          |
        |  • Embedding 写库     |
        +----------+-----------+
                   |
                   v
        +-----------------------+
        |   Qdrant Vector DB    |
        +-----------------------+
```

### 前端
- Vite3 + React19 SPA。
- React Router v7 路由管理。
- `fetch` + SSE 流式获取后端推送。
- React Suspense + Skeleton 优化首屏交互时间。

### 后端
- **FastAPI** 提供 REST 与 SSE 接口。
- Celery + Redis Streams 并行处理 PDF、OCR、Embedding 写库。
- OpenAI GPT-4o / ada-002 嵌入模型。

### 数据管道
1. Downloader：下载 PDF 与 metadata。
2. Loader：基于 PyMuPDF 按章节切块。
3. Figure Extractor：detectron2 检测 + PaddleOCR 识别。
4. Embedding：OpenAI 生成 1536 维向量，写入 Qdrant。
5. KeyInfo Extractor：GPT Prompt-COT 输出 JSON（method/result/contribution）。

## 4. 技术栈
- **前端**：Vite, React19, TypeScript, TailwindCSS, shadcn/ui, React Router v7, react-query。
- **后端**：Python3.11, FastAPI, Celery, Redis, Qdrant, OpenAI SDK。
- **工具**：Langfuse（链路监控）, Grafana + Prometheus（性能指标）。

## 5. 关键技术要点
| 模块 | 难点 | 方案 |
|------|------|------|
| PDF Loader | 章节切分保持语义 | `RecursiveCharacterTextSplitter` + 标题正则 |
| 图表 OCR | 图表定位 + 文本识别 | detectron2 预训练 + PaddleOCR |
| 渐进渲染 | 后端任务分段完成 | SSE 推送阶段事件, 前端 Suspense |
| Hybrid 检索 | 精准召回专业术语 | 向量相似度 + BM25 打分加权 |

## 6. 开发里程碑
| 周期 | 里程碑 | 交付物 |
|------|--------|--------|
| Week 1 | 框架搭建 & PDF 下载 | Vite 项目初始化, FastAPI 基础 API |
| Week 2 | PDF 解析 + 嵌入写库 | Celery Worker & Qdrant 集成 |
| Week 3 | 图表 OCR + 关键信息提炼 | Figure Extractor, Prompt-COT 流程 |
| Week 4 | 前端渐进渲染 & Chat | SSE 流式 UI, RAG 问答模块 |
| Week 5 | 收藏/分享 + 指标监控 | IndexedDB, Langfuse 仪表盘 |
| Week 6 | 压测 & 部署 | Docker Compose, Netlify + Render 部署 |

## 7. 指标目标
- **首屏可交互时间 (TTI)** < 1500 ms。
- **摘要生成 P95** < 8 s。
- **Hybrid 召回率提升** ≥ 25%（相对纯向量）。
- **用户保存率** ≥ 40%。

## 8. SEO 与部署
- 目前定位作品集 Demo，采用 SPA 静态部署（Netlify）。
- 后续如需 SEO，可引入 `vite-plugin-prerender` 对 `/paper/[id]` 预渲染。

## 9. 未来扩展
- 多模态：支持手写笔记图片 OCR。
- Agent 模块：Researcher ➜ Writer ➜ Publisher。
- PromptLab 集成：A/B 测试不同提炼 Prompt，自动调优。

---

> 文档版本：v0.1 更新日期：2025-07-28