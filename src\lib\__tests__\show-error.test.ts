import { describe, test, expect, vi, beforeEach } from "vitest";

// 先 mock sonner，以便在导入 showApiError 之前覆盖依赖
vi.mock("sonner", () => ({
  toast: {
    error: vi.fn(),
  },
}));

// Mock ErrorHandlerService 让它抛出错误，回退到原有逻辑
vi.mock("@/services/errorHandler", () => ({
  ErrorHandlerService: {
    processApiError: vi.fn(() => {
      throw new Error("Mock error to trigger fallback");
    }),
  },
}));

import { ApiError } from "@/lib/http";
import { showApiError } from "@/lib/show-error";

import { toast } from "sonner";

describe("showApiError", () => {
  beforeEach(() => {
    vi.resetAllMocks();
  });

  test("handles ApiError instance", () => {
    const err = new ApiError(400, "Bad Request");
    showApiError(err);
    expect(toast.error).toHaveBeenCalledWith("Bad Request");
  });

  test("handles 401 unauthorized", () => {
    const err = new ApiError(401, "Unauthorized");
    showApiError(err);
    expect(toast.error).toHaveBeenCalledWith("请先登录");
  });

  test("handles 403 forbidden", () => {
    const err = new ApiError(403, "Forbidden");
    showApiError(err);
    expect(toast.error).toHaveBeenCalledWith("权限不足");
  });

  test("handles string error", () => {
    showApiError("Something wrong");
    expect(toast.error).toHaveBeenCalledWith("Something wrong");
  });

  test("handles object with message field", () => {
    showApiError({ message: "Oops" });
    expect(toast.error).toHaveBeenCalledWith("Oops");
  });

  test("handles object with code field mapping", () => {
    showApiError({ code: 10002, message: "Resource not found" });
    expect(toast.error).toHaveBeenCalledWith("资源不存在");
  });

  test("falls back to default message", () => {
    showApiError({});
    expect(toast.error).toHaveBeenCalledWith("Network error");
  });
}); 