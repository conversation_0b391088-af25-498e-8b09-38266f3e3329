import { promises as fs } from "fs";
import { join } from "path";

// 简易文件数据库：读写 JSON 数组
export async function readJson<T>(filename: string): Promise<T> {
  const filePath = join(process.cwd(), "src/data", filename);
  const data = await fs.readFile(filePath, "utf-8");
  return JSON.parse(data) as T;
}

export async function writeJson<T>(filename: string, data: T): Promise<void> {
  const filePath = join(process.cwd(), "src/data", filename);
  await fs.writeFile(filePath, JSON.stringify(data, null, 2), "utf-8");
} 