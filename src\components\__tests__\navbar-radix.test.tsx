import { describe, test, expect, vi } from 'vitest'
import { render, screen, fireEvent } from '@testing-library/react'
import { renderWithProviders } from '@/__test__/test-utils'
import Navbar from '../navbar'

// Mock the child components
vi.mock('../language-switcher', () => ({
  default: () => <div data-testid="language-switcher">Language Switcher</div>
}))

vi.mock('../user-menu', () => ({
  default: () => <div data-testid="user-menu">User Menu</div>
}))

// Mock Next.js Link
vi.mock('next/link', () => ({
  default: ({ children, href, ...props }: any) => (
    <a href={href} {...props}>
      {children}
    </a>
  )
}))

describe('Navbar (Radix Migration)', () => {
  test('renders logo and navigation items', () => {
    renderWithProviders(<Navbar />)

    // 检查 Logo
    expect(screen.getByText('Ayafeed')).toBeInTheDocument()

    // 检查桌面端导航链接
    expect(screen.getByText('首页')).toBeInTheDocument()
    expect(screen.getByText('展会')).toBeInTheDocument()
    expect(screen.getByText('社团')).toBeInTheDocument()
  })

  test('renders child components', () => {
    renderWithProviders(<Navbar />)

    // 检查子组件是否渲染
    expect(screen.getByTestId('language-switcher')).toBeInTheDocument()
    expect(screen.getByTestId('user-menu')).toBeInTheDocument()
  })

  test('renders mobile menu button', () => {
    renderWithProviders(<Navbar />)

    // 检查移动端菜单按钮
    const menuButton = screen.getByRole('button')
    expect(menuButton).toBeInTheDocument()
  })

  test('mobile menu button has correct accessibility', () => {
    renderWithProviders(<Navbar />)

    const menuButton = screen.getByRole('button')

    // 检查按钮可以被点击
    fireEvent.click(menuButton)

    // 验证按钮有正确的样式类
    expect(menuButton).toHaveClass('md:hidden')
  })

  test('navigation links have correct hrefs', () => {
    renderWithProviders(<Navbar />)

    // 检查链接的 href 属性
    expect(screen.getByText('首页').closest('a')).toHaveAttribute('href', '/')
    expect(screen.getByText('展会').closest('a')).toHaveAttribute('href', '/events')
    expect(screen.getByText('社团').closest('a')).toHaveAttribute('href', '/circles')
  })

  test('applies correct CSS classes for styling', () => {
    const { container } = renderWithProviders(<Navbar />)

    // 检查主导航容器的样式
    const nav = container.querySelector('nav')
    expect(nav).toHaveClass('sticky', 'top-0', 'z-50')
    expect(nav).toHaveClass('bg-slate-900/95')
  })

  test('logo has hover effects', () => {
    renderWithProviders(<Navbar />)

    const logo = screen.getByText('Ayafeed')
    expect(logo).toHaveClass('text-xl', 'font-bold', 'tracking-tight')
  })

  test('desktop navigation is hidden on mobile', () => {
    renderWithProviders(<Navbar />)

    // 检查桌面导航在移动端隐藏
    const desktopNav = screen.getByText('首页').closest('div')
    expect(desktopNav).toHaveClass('hidden', 'md:flex')
  })

  test('mobile menu button is hidden on desktop', () => {
    renderWithProviders(<Navbar />)

    const menuButton = screen.getByRole('button')
    expect(menuButton).toHaveClass('md:hidden')
  })
})
