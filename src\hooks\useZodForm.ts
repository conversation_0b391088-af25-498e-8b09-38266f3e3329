import { zodResolver } from "@hookform/resolvers/zod";
import { useForm, UseFormProps, FieldValues } from "react-hook-form";
import { z } from "zod";

/**
 * useZodForm
 * --------------------------------------------------
 * 封装 react-hook-form，自动接入 zodResolver。
 * 使用方式：
 *   const form = useZodForm(EventInputSchema)
 */
export function useZodForm<
  Schema extends z.ZodType<any, any, any>,
  TFieldValues extends FieldValues = z.infer<Schema>
>(schema: Schema, props?: UseFormProps<TFieldValues>) {
  return useForm<TFieldValues>({
    resolver: zodResolver(schema),
    ...props,
  })
} 