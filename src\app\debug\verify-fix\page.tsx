'use client'

import { useLocale } from 'next-intl';
import { useGetEvents } from '@/api/generated/ayafeedComponents';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';

/**
 * 验证语言切换修复是否有效
 */
export default function VerifyFixPage() {
  const locale = useLocale();
  
  // 测试 API 调用
  const { data, isLoading, queryKey, error } = useGetEvents(
    { queryParams: { page: '1', pageSize: '5' } },
    { staleTime: 60 * 1000 }
  );

  // 检查查询键是否包含语言信息
  const hasLocaleInQueryKey = queryKey?.some(key => {
    if (typeof key === 'object' && key !== null) {
      return 'locale' in key;
    }
    return false;
  });

  // 提取查询键中的语言信息
  const localeInQueryKey = queryKey?.find(key => {
    if (typeof key === 'object' && key !== null) {
      const obj = key as Record<string, unknown>;
      return 'locale' in obj ? obj.locale : null;
    }
    return null;
  }) as { locale?: string } | undefined;

  return (
    <div className="container mx-auto p-6 space-y-6">
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            语言切换修复验证
            {hasLocaleInQueryKey ? (
              <Badge variant="default" className="bg-green-500">✅ 修复成功</Badge>
            ) : (
              <Badge variant="destructive">❌ 修复失败</Badge>
            )}
          </CardTitle>
          <p className="text-sm text-muted-foreground">
            当前语言: <strong>{locale}</strong>
          </p>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <h3 className="font-semibold mb-2">检查结果:</h3>
              <div className="space-y-2">
                <div className="flex items-center gap-2">
                  <span>查询键包含语言信息:</span>
                  {hasLocaleInQueryKey ? (
                    <Badge variant="default" className="bg-green-500">是</Badge>
                  ) : (
                    <Badge variant="destructive">否</Badge>
                  )}
                </div>
                <div className="flex items-center gap-2">
                  <span>查询键中的语言:</span>
                  <Badge variant="outline">
                    {localeInQueryKey?.locale || '未找到'}
                  </Badge>
                </div>
                <div className="flex items-center gap-2">
                  <span>语言匹配:</span>
                  {localeInQueryKey?.locale === locale ? (
                    <Badge variant="default" className="bg-green-500">匹配</Badge>
                  ) : (
                    <Badge variant="destructive">不匹配</Badge>
                  )}
                </div>
              </div>
            </div>

            <div>
              <h3 className="font-semibold mb-2">API状态:</h3>
              <div className="space-y-2">
                <div className="flex items-center gap-2">
                  <span>加载状态:</span>
                  <Badge variant={isLoading ? "secondary" : "default"}>
                    {isLoading ? '加载中' : '已完成'}
                  </Badge>
                </div>
                <div className="flex items-center gap-2">
                  <span>错误状态:</span>
                  <Badge variant={error ? "destructive" : "default"}>
                    {error ? '有错误' : '正常'}
                  </Badge>
                </div>
                <div className="flex items-center gap-2">
                  <span>数据条数:</span>
                  <Badge variant="outline">
                    {data?.items?.length || 0}
                  </Badge>
                </div>
              </div>
            </div>
          </div>

          <div>
            <h3 className="font-semibold mb-2">完整查询键:</h3>
            <pre className="bg-muted p-3 rounded text-sm overflow-x-auto">
              {JSON.stringify(queryKey, null, 2)}
            </pre>
          </div>

          {error && (
            <div>
              <h3 className="font-semibold mb-2 text-red-600">错误信息:</h3>
              <pre className="bg-red-50 border border-red-200 p-3 rounded text-sm overflow-x-auto">
                {String(error)}
              </pre>
            </div>
          )}

          <div className="mt-6 p-4 bg-blue-50 border border-blue-200 rounded">
            <h3 className="font-semibold mb-2">修复说明:</h3>
            <ul className="text-sm space-y-1">
              <li>• 修改了 <code>useAyafeedContext</code> 函数，自动添加当前语言到查询参数</li>
              <li>• 更新了缓存管理逻辑，以适应新的查询键结构</li>
              <li>• 现在不同语言的数据会有独立的缓存键，避免相互覆盖</li>
              <li>• 语言切换时会自动触发新的API请求</li>
            </ul>
          </div>

          {hasLocaleInQueryKey && (
            <div className="mt-4 p-4 bg-green-50 border border-green-200 rounded">
              <h3 className="font-semibold mb-2 text-green-800">✅ 修复验证成功!</h3>
              <p className="text-sm text-green-700">
                查询键现在包含语言信息，语言切换功能应该正常工作。
                请在 React Query DevTools 中验证不同语言的查询是否有独立的缓存条目。
              </p>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
}
