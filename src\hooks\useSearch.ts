/**
 * 搜索相关Hook
 * 基于后端文档包的常用示例
 */

import { useQuery } from '@tanstack/react-query';
import { request } from '@/lib/http';
import { useLocalization } from './useLocalization';
import { createLocaleQueryKey } from '@/utils/localization';

interface SearchParams {
  q: string;
  type?: 'events' | 'circles' | 'all';
  page?: number;
  limit?: number;
}

interface SearchResult {
  id: string;
  type: 'event' | 'circle';
  title: string;
  description: string;
  url: string;
}

interface SearchResponse {
  results: SearchResult[];
  total: number;
  query: string;
}

// 搜索内容
export function useSearch(params: SearchParams) {
  const { locale } = useLocalization();
  
  return useQuery({
    queryKey: createLocaleQueryKey(['search', params], locale),
    queryFn: () => request<SearchResponse>('/search', { params }),
    enabled: !!params.q && params.q.length >= 2, // 至少2个字符才搜索
    staleTime: 2 * 60 * 1000, // 2分钟
  });
}

// 搜索建议
export function useSearchSuggestions(query: string) {
  const { locale } = useLocalization();
  
  return useQuery({
    queryKey: createLocaleQueryKey(['search', 'suggestions', query], locale),
    queryFn: () => request<string[]>('/search/suggestions', { 
      params: { q: query } 
    }),
    enabled: !!query && query.length >= 1,
    staleTime: 5 * 60 * 1000, // 5分钟
  });
}
