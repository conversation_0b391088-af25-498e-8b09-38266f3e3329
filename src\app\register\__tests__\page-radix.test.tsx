import { describe, test, expect, vi, beforeEach } from 'vitest'
import { render, screen, fireEvent, waitFor } from '@testing-library/react'
import userEvent from '@testing-library/user-event'
import { useRouter } from 'next/navigation'
import { toast } from 'sonner'
import RegisterPage from '../page'
import { useAuth } from '@/contexts/user'

// Mock dependencies
vi.mock('next/navigation', () => ({
  useRouter: vi.fn(),
}))

vi.mock('sonner', () => ({
  toast: {
    success: vi.fn(),
  },
}))

vi.mock('@/contexts/user', () => ({
  useAuth: vi.fn(),
}))

vi.mock('@/lib/show-error', () => ({
  showApiError: vi.fn(),
}))

// Mock next/link
vi.mock('next/link', () => ({
  default: ({ children, href, ...props }: any) => (
    <a href={href} {...props}>
      {children}
    </a>
  ),
}))

const mockPush = vi.fn()
const mockRegister = vi.fn()

describe('RegisterPage (Radix Migration)', () => {
  beforeEach(() => {
    vi.clearAllMocks()
    ;(useRouter as any).mockReturnValue({
      push: mockPush,
    })
    ;(useAuth as any).mockReturnValue({
      register: mockRegister,
    })
  })

  test('renders registration form with all fields', () => {
    render(<RegisterPage />)
    
    // 检查页面标题和描述
    expect(screen.getByText('注册新账户')).toBeInTheDocument()
    expect(screen.getByText('创建一个账户以继续')).toBeInTheDocument()
    
    // 检查表单字段
    expect(screen.getByLabelText('用户名')).toBeInTheDocument()
    expect(screen.getByLabelText('密码')).toBeInTheDocument()
    expect(screen.getByLabelText('确认密码')).toBeInTheDocument()
    
    // 检查提交按钮
    expect(screen.getByRole('button', { name: '创建账户' })).toBeInTheDocument()
    
    // 检查登录链接
    expect(screen.getByText('已经有账户了？')).toBeInTheDocument()
    expect(screen.getByRole('link', { name: '去登录' })).toHaveAttribute('href', '/login')
  })

  test('shows validation errors for empty fields', async () => {
    const user = userEvent.setup()
    render(<RegisterPage />)
    
    // 点击提交按钮而不填写任何字段
    const submitButton = screen.getByRole('button', { name: '创建账户' })
    await user.click(submitButton)
    
    // 检查验证错误
    await waitFor(() => {
      expect(screen.getByText('用户名至少需要3个字符')).toBeInTheDocument()
      expect(screen.getByText('密码至少需要6个字符')).toBeInTheDocument()
    })
  })

  test('shows validation error for short username', async () => {
    const user = userEvent.setup()
    render(<RegisterPage />)
    
    // 输入短用户名
    const usernameInput = screen.getByLabelText('用户名')
    await user.type(usernameInput, 'ab')
    
    // 点击提交
    const submitButton = screen.getByRole('button', { name: '创建账户' })
    await user.click(submitButton)
    
    await waitFor(() => {
      expect(screen.getByText('用户名至少需要3个字符')).toBeInTheDocument()
    })
  })

  test('shows validation error for short password', async () => {
    const user = userEvent.setup()
    render(<RegisterPage />)
    
    // 输入短密码
    const passwordInput = screen.getByLabelText('密码')
    await user.type(passwordInput, '12345')
    
    // 点击提交
    const submitButton = screen.getByRole('button', { name: '创建账户' })
    await user.click(submitButton)
    
    await waitFor(() => {
      expect(screen.getByText('密码至少需要6个字符')).toBeInTheDocument()
    })
  })

  test('shows validation error for password mismatch', async () => {
    const user = userEvent.setup()
    render(<RegisterPage />)
    
    // 输入不匹配的密码
    const usernameInput = screen.getByLabelText('用户名')
    const passwordInput = screen.getByLabelText('密码')
    const confirmPasswordInput = screen.getByLabelText('确认密码')
    
    await user.type(usernameInput, 'testuser')
    await user.type(passwordInput, 'password123')
    await user.type(confirmPasswordInput, 'password456')
    
    // 点击提交
    const submitButton = screen.getByRole('button', { name: '创建账户' })
    await user.click(submitButton)
    
    await waitFor(() => {
      expect(screen.getByText('两次输入的密码不一致')).toBeInTheDocument()
    })
  })

  test('submits form with valid data', async () => {
    const user = userEvent.setup()
    mockRegister.mockResolvedValue({})
    
    render(<RegisterPage />)
    
    // 填写有效数据
    const usernameInput = screen.getByLabelText('用户名')
    const passwordInput = screen.getByLabelText('密码')
    const confirmPasswordInput = screen.getByLabelText('确认密码')
    
    await user.type(usernameInput, 'testuser')
    await user.type(passwordInput, 'password123')
    await user.type(confirmPasswordInput, 'password123')
    
    // 提交表单
    const submitButton = screen.getByRole('button', { name: '创建账户' })
    await user.click(submitButton)
    
    // 验证注册函数被调用
    await waitFor(() => {
      expect(mockRegister).toHaveBeenCalledWith({
        username: 'testuser',
        password: 'password123',
      })
    })
    
    // 验证成功提示和跳转
    expect(toast.success).toHaveBeenCalledWith('注册成功！', {
      description: '现在您可以使用新账户登录了。',
    })
    expect(mockPush).toHaveBeenCalledWith('/login')
  })

  test('shows loading state during submission', async () => {
    const user = userEvent.setup()
    // 模拟延迟的注册请求
    mockRegister.mockImplementation(() => new Promise(resolve => setTimeout(resolve, 100)))
    
    render(<RegisterPage />)
    
    // 填写有效数据
    const usernameInput = screen.getByLabelText('用户名')
    const passwordInput = screen.getByLabelText('密码')
    const confirmPasswordInput = screen.getByLabelText('确认密码')
    
    await user.type(usernameInput, 'testuser')
    await user.type(passwordInput, 'password123')
    await user.type(confirmPasswordInput, 'password123')
    
    // 提交表单
    const submitButton = screen.getByRole('button', { name: '创建账户' })
    await user.click(submitButton)
    
    // 检查加载状态
    expect(screen.getByRole('button', { name: '注册中...' })).toBeInTheDocument()
    expect(screen.getByRole('button', { name: '注册中...' })).toBeDisabled()
    
    // 检查输入框也被禁用
    expect(usernameInput).toBeDisabled()
    expect(passwordInput).toBeDisabled()
    expect(confirmPasswordInput).toBeDisabled()
  })

  test('handles registration error', async () => {
    const user = userEvent.setup()
    const error = new Error('Registration failed')
    mockRegister.mockRejectedValue(error)
    
    const { showApiError } = await import('@/lib/show-error')
    
    render(<RegisterPage />)
    
    // 填写有效数据
    const usernameInput = screen.getByLabelText('用户名')
    const passwordInput = screen.getByLabelText('密码')
    const confirmPasswordInput = screen.getByLabelText('确认密码')
    
    await user.type(usernameInput, 'testuser')
    await user.type(passwordInput, 'password123')
    await user.type(confirmPasswordInput, 'password123')
    
    // 提交表单
    const submitButton = screen.getByRole('button', { name: '创建账户' })
    await user.click(submitButton)
    
    // 验证错误处理
    await waitFor(() => {
      expect(showApiError).toHaveBeenCalledWith(error, '注册失败')
    })
  })

  test('form inputs have correct attributes', () => {
    render(<RegisterPage />)
    
    const usernameInput = screen.getByLabelText('用户名')
    const passwordInput = screen.getByLabelText('密码')
    const confirmPasswordInput = screen.getByLabelText('确认密码')
    
    // 检查输入框类型
    expect(usernameInput).toHaveAttribute('type', 'text')
    expect(passwordInput).toHaveAttribute('type', 'password')
    expect(confirmPasswordInput).toHaveAttribute('type', 'password')
    
    // 检查占位符
    expect(usernameInput).toHaveAttribute('placeholder', 'Username')
    expect(passwordInput).toHaveAttribute('placeholder', 'Password')
    expect(confirmPasswordInput).toHaveAttribute('placeholder', 'Confirm Password')
    
    // 检查 ID
    expect(usernameInput).toHaveAttribute('id', 'username')
    expect(passwordInput).toHaveAttribute('id', 'password')
    expect(confirmPasswordInput).toHaveAttribute('id', 'confirmPassword')
  })

  test('component renders without crashing', () => {
    const { container } = render(<RegisterPage />)
    expect(container.firstChild).toBeInTheDocument()
  })

  test('card has correct styling classes', () => {
    const { container } = render(<RegisterPage />)

    // 检查主容器
    const mainContainer = container.querySelector('.flex.min-h-screen')
    expect(mainContainer).toHaveClass('flex', 'min-h-screen', 'items-center', 'justify-center', 'bg-background')

    // 检查卡片
    const card = container.querySelector('.w-full.max-w-sm')
    expect(card).toHaveClass('w-full', 'max-w-sm')
  })

  test('form has correct structure', () => {
    const { container } = render(<RegisterPage />)

    const form = container.querySelector('form')
    expect(form).toHaveClass('grid', 'gap-4')

    // 检查表单字段容器
    const fieldContainers = container.querySelectorAll('.grid.gap-2')
    expect(fieldContainers).toHaveLength(3) // 用户名、密码、确认密码

    fieldContainers.forEach(container => {
      expect(container).toHaveClass('grid', 'gap-2')
    })
  })
})
