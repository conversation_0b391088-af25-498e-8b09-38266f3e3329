/**
 * 错误码映射和处理工具
 * 基于后端文档包的错误处理规范
 */

import { ErrorType } from '@/types/errors';

// 扩展现有的错误码映射，添加错误类型分类
export const ERROR_CODE_MAP = {
  // 认证相关 (20xxx)
  20001: { type: ErrorType.AUTHENTICATION_ERROR, message: '未登录' },
  20002: { type: ErrorType.AUTHORIZATION_ERROR, message: '权限不足' },
  
  // 验证相关 (10xxx)
  10001: { type: ErrorType.VALIDATION_ERROR, message: '参数缺失或无效' },
  10002: { type: ErrorType.NOT_FOUND_ERROR, message: '资源不存在' },
  10003: { type: ErrorType.VALIDATION_ERROR, message: '唯一键冲突' },
  
  // 字段相关 (30xxx)
  30001: { type: ErrorType.VALIDATION_ERROR, message: 'fields 参数为空' },
  30002: { type: ErrorType.VALIDATION_ERROR, message: '请求了未知字段' },
  
  // 用户相关 (40xxx)
  40001: { type: ErrorType.VALIDATION_ERROR, message: '用户注册失败' },
  40002: { type: ErrorType.NOT_FOUND_ERROR, message: '用户不存在' },
  40003: { type: ErrorType.AUTHENTICATION_ERROR, message: '密码错误' },
  
  // 业务相关 (50xxx, 60xxx)
  50001: { type: ErrorType.NOT_FOUND_ERROR, message: '参展记录不存在' },
  50002: { type: ErrorType.VALIDATION_ERROR, message: 'booth_id 必填' },
  60001: { type: ErrorType.NOT_FOUND_ERROR, message: '社团不存在' },
  60002: { type: ErrorType.NOT_FOUND_ERROR, message: '作者不存在' },
  60003: { type: ErrorType.NOT_FOUND_ERROR, message: '展会不存在' },
} as const;

export function getErrorInfo(code: number) {
  return ERROR_CODE_MAP[code as keyof typeof ERROR_CODE_MAP] || {
    type: ErrorType.UNKNOWN_ERROR,
    message: '未知错误'
  };
}

// HTTP状态码映射
export const HTTP_STATUS_MAP = {
  400: { type: ErrorType.VALIDATION_ERROR, message: '请求参数错误' },
  401: { type: ErrorType.AUTHENTICATION_ERROR, message: '登录已过期，请重新登录' },
  403: { type: ErrorType.AUTHORIZATION_ERROR, message: '权限不足，无法执行此操作' },
  404: { type: ErrorType.NOT_FOUND_ERROR, message: '请求的资源不存在' },
  422: { type: ErrorType.VALIDATION_ERROR, message: '数据验证失败' },
  500: { type: ErrorType.SERVER_ERROR, message: '服务器内部错误，请稍后重试' },
  502: { type: ErrorType.SERVER_ERROR, message: '服务器网关错误' },
  503: { type: ErrorType.SERVER_ERROR, message: '服务暂时不可用' },
} as const;

export function getHttpStatusInfo(status: number) {
  return HTTP_STATUS_MAP[status as keyof typeof HTTP_STATUS_MAP] || {
    type: ErrorType.UNKNOWN_ERROR,
    message: `HTTP错误 ${status}`
  };
}
