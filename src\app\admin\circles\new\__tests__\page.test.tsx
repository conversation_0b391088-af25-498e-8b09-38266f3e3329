"use client";

import {
  renderWithProviders,
  screen,
  fireEvent,
  waitFor,
} from "@test/test-utils";

import { useRouter } from "next/navigation";
import React from "react";
import type { Mock } from "vitest";

import NewCirclePage from "../page";

// ------- mocks -------
const mutateMock = vi.fn();

vi.mock("next/navigation", () => ({
  useRouter: vi.fn(),
}));

const createState = { isPending: false };

vi.mock("@/hooks/admin/useCreateCircle", () => ({
  useCreateCircle: () => ({
    mutate: mutateMock,
    get isPending() {
      return createState.isPending;
    },
  }),
}));

const mockPush = vi.fn();

// spy showApiError（import 顺序已确定，可直接 spyOn）
import * as showErrorModule from "@/lib/show-error";
const showErrorMock = vi
  .spyOn(showErrorModule, "showApiError")
  .mockImplementation(() => {});

describe("NewCirclePage", () => {
  beforeEach(() => {
    vi.resetAllMocks();
    (useRouter as unknown as Mock).mockReturnValue({ push: mockPush });
  });

  test("should show validation errors when required fields are empty", async () => {
    renderWithProviders(<NewCirclePage />);

    fireEvent.click(screen.getByRole("button", { name: "保存" }));

    await waitFor(() => {
      expect(screen.getByText("名称必填")).toBeInTheDocument();
      expect(screen.getByText("请选择分类")).toBeInTheDocument();
    });

    expect(mutateMock).not.toHaveBeenCalled();
  });

  test("should submit form and navigate on success", async () => {
    // mock mutate to immediately call onSuccess
    mutateMock.mockImplementation((_payload, opts) => {
      opts?.onSuccess?.();
    });

    renderWithProviders(<NewCirclePage />);

    // 填写表单
    fireEvent.change(screen.getByLabelText("名称 *"), { target: { value: "新社团" } });
    fireEvent.change(screen.getByLabelText("分类 *"), { target: { value: "comic" } });

    fireEvent.click(screen.getByRole("button", { name: "保存" }));

    await waitFor(() => {
      expect(mutateMock).toHaveBeenCalledWith(
        {
          name: "新社团",
          author: "",
          category: "comic",
        },
        expect.any(Object)
      );
    });

    expect(mockPush).toHaveBeenCalledWith("/admin/circles");
  });

  test("should show loading state when mutation is pending", () => {
    createState.isPending = true;

    renderWithProviders(<NewCirclePage />);

    const button = screen.getByRole("button", { name: "保存中..." });
    expect(button).toBeDisabled();

    // 恢复默认状态，避免影响其他用例
    createState.isPending = false;
  });

  test("should call showApiError on mutation failure", async () => {
    mutateMock.mockImplementation((_payload, opts) => {
      opts?.onError?.({ message: "网络错误" });
    });

    renderWithProviders(<NewCirclePage />);

    fireEvent.change(screen.getByLabelText("名称 *"), {
      target: { value: "社团 A" },
    });
    fireEvent.change(screen.getByLabelText("分类 *"), {
      target: { value: "comic" },
    });

    fireEvent.click(screen.getByRole("button", { name: "保存" }));

    await waitFor(() => {
      expect(showErrorMock).toHaveBeenCalledWith({
        message: "网络错误",
      });
    });
  });
}); 