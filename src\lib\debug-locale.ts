/**
 * 调试语言功能的工具函数
 */

import { getCurrentLocale, setLocaleToCookie, type Locale } from './locale-utils'

export function debugLocaleState() {
  console.log('=== 语言状态调试 ===')
  console.log('getCurrentLocale():', getCurrentLocale())
  console.log('document.cookie:', typeof document !== 'undefined' ? document.cookie : 'N/A')
  
  if (typeof document !== 'undefined') {
    const cookieLocale = document.cookie
      .split('; ')
      .find(row => row.startsWith('locale='))
      ?.split('=')[1]
    console.log('从 cookie 解析的语言:', cookieLocale)
  }
}

export function testLocaleChange(newLocale: Locale) {
  console.log('=== 测试语言切换 ===')
  console.log('切换前 getCurrentLocale():', getCurrentLocale())
  
  setLocaleToCookie(newLocale)
  
  console.log('切换后 getCurrentLocale():', getCurrentLocale())
  console.log('新的 document.cookie:', typeof document !== 'undefined' ? document.cookie : 'N/A')
}

// 在全局对象上暴露调试函数，方便在浏览器控制台中使用
if (typeof window !== 'undefined') {
  (window as any).debugLocale = {
    debugLocaleState,
    testLocaleChange,
    getCurrentLocale,
    setLocaleToCookie
  }
}
