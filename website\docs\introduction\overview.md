---
id: overview
title: 项目概述
sidebar_label: 概述
sidebar_position: 1
description: Ayafeed 同人展会信息聚合平台的产品需求文档和项目概述
keywords: [ayafeed, 同人展会, 产品需求, 项目概述]
---

# Ayafeed Web 产品需求文档 (PRD) v0.1

## 1. 文档信息
| 版本 | 日期 | 作者 | 说明 |
|------|------|------|------|
| 0.1  | 2025-07-11 | AI Draft | 首版草稿，待团队评审 |

> 本文档覆盖 Ayafeed Web（前端）在 **Alpha → Beta** 阶段的核心需求。后续更新请追加版本记录并描述变更。

---

## 2. 背景与目标
Ayafeed 致力于提供同人展会、社团、作者等多维数据的高质量聚合与可视化体验。目前后端 API 已具备基础数据管理能力（参见 `docs/docs_backend`），但前端仍以静态 JSON + Mock 数据为主，缺乏真实交互与用户功能，无法支撑公开发布。

本期目标：
1. **最小可用版本 (MVP)**：上线可供访客浏览的 Web 站点，支持展会列表/详情、社团详情、Feed 流等核心功能。
2. **后台管理**：提供管理员增删改查界面并接入后端 Admin API，实现数据闭环。
3. **认证与角色控制**：引入登录、登出与基于角色的路由守卫。

业务指标（验收时评估）：
- 首屏 LCP ≤ 2.5 s（移动 4G 网络）
- 页面错误率（JS error + API error） < 1 %
- 管理员可在 2 min 内完成一次「新增展会并同步前端展示」

---

## 3. 术语与角色
| 名称 | 说明 |
|------|------|
| **Circle (社团)** | 参与同人活动并发布作品的团队或个人 |
| **Event (展会)** | 同人即卖会、Comic Market、博丽神社例大祭等 |
| **Feed** | 首页信息流，聚合最新展会、社团动态 |
| **Admin** | 拥有后台管理权限的系统用户 |
| **Visitor** | 未登录访客，仅能查看公开数据 |
| **Member** | 登录用户，可收藏/订阅社团与展会（规划中） |

---

## 4. 需求概览
| ID | 需求 | 用户价值 | 优先级 (MoSCoW) | 依赖 |
|----|------|----------|-----------------|-------|
| R1 | 展会列表 & 详情 | 浏览近期/历史展会信息 | Must | 后端 `/events` API |
| R2 | 社团详情页 | 了解社团信息与参展历史 | Must | R1, `/circles/:id` API |
| R3 | 首页 Feed | 快速获取最新活动与社团动态 | Should | R1, R2 |
| R4 | 搜索 & 筛选 | 快速检索展会/社团 | Should | R1, R2 |
| R5 | 用户认证 | 登录、登出、角色判定 | Must | `/auth/*` API |
| R6 | 后台管理 UI | 增删改查展会/社团 | Must | R5, Admin API |
| R7 | 订阅功能 | 关注社团 / 展会更新 | Could | R5, R3 |
| R8 | 国际化 & 主题切换 | 多语言 + 深色模式 | Could | - |

---

## 5. 功能详情

### R1 展会列表 & 详情
**User Story**
> 作为访客，我希望在活动页面查看所有展会及时间地点，以决定是否参加。

**交互流程**
1. 用户访问 `/events` → 请求 `GET /events` → 列表展示。
2. 点击某展会卡片 → 跳转 `/events/[id]` → 请求 `GET /events/:id` → 展示详情、平面图、参展社团列表。

**业务规则**
- 列表默认以 `date_sort` 降序排序。
- 支持 `month` 分组展示（见设计稿）。
- 详情页需展示：名称、日期、会场、官方链接、场馆地图 (Leaflet)。

**埋点**
| Event | 参数 |
|-------|------|
| event_click | `event_id` |
| event_share | `event_id`, `channel` |

**验收标准**
- 正常加载 1000 条展会数据，页面无明显卡顿。
- 详情页地图可拖拽、缩放，Marker 显示正确坐标。

---

### R2 社团详情页
**User Story**
> 作为访客，我想查看某个社团的基本信息与历届参展记录，方便收藏。

**业务规则**
- 访问 `/circles/[id]`，需展示：社团名、作者、社交链接、参展历史表格。
- 参展历史通过 `GET /circles/:id/appearances` 拉取，支持分页。

**验收标准**
- 列表分页切换 < 200 ms。
- 外部链接使用 `rel="noopener noreferrer"`。 

---

### R3 首页 Feed
**User Story**
> 作为访客/登录用户，我想在首页无限滚动浏览最新动态，节约点击成本。

**业务规则**
- 默认按 `publish_time` 降序。
- 当滚动到距离底部 300 px 时自动拉取下一页。
- 失败重试 & 骨架屏。

**验收标准**
- 50 条/页场景下首屏渲染时间 < 1 s。

---

### R4 搜索 & 筛选
**User Story**
> 作为访客，我可以在搜索框输入关键字并通过标签快速筛选，以便定位目标社团、展会。

**业务规则**
- 支持关键词、分类、多选标签过滤。
- 结果页 URL 保留查询参数，支持分享并回显。

---

### R5 用户认证
- 登录 `/login`、注册 `/register`，Token 写入 Cookie + React Context 写入用户。
- 退出登录清除本地状态并重定向 `/`。
- `RoleGuard` 组件包装需要角色权限的路由，如 Admin。

---

### R6 后台管理 UI
- 路由 `/admin/*`（`layout.tsx` 已存在）。
- 支持 **增 / 改 / 删 / 列表**，表单使用 shadcn/ui 组件，字段与后端 Admin API 对齐。
- 失败弹 Toast，成功刷新列表。

---

### R7 订阅功能 (延伸)
- 登录用户可在社团/展会页点击「订阅」。
- 后端返回 `status: subscribed`。
- 在「我的订阅」页面(`/me/subscriptions`) 展示列表并支持取消订阅。

---

## 6. 非功能需求
| 分类 | 指标 |
|------|------|
| 性能 | LCP ≤ 2.5 s；CLS ≤ 0.1；Timely Data Fetch；代码拆分 & 懒加载 |
| 兼容性 | 桌面 Chrome/Edge/Firefox 最新 2 个版本；移动 Safari/Chrome 最新版本 |
| 安全 | HTTPS；Content-Security-Policy；JWT 失效策略；XSS/CSRF 保护 |
| 可访问性 | 遵循 WCAG 2.1 AA；键盘可达；ARIA 标签 |
| 运营 | Google Analytics / Plausible 接入；Sentry 错误监控 |
| 可维护性 | ESLint 无报错；单元测试覆盖率 ≥ 70 % |

---

## 7. 里程碑 & 计划 (与 `docs/TODO.md` 对应)
| 阶段 | 时间 (人天) | 目标 |
|------|------------|------|
| Phase 1 | 4–7 | Admin Logic + Integration + Auth Flow |
| Phase 2 | 7.5–11 | Feed、Circle Detail、Search |
| Phase 3 | 8–12 | Subscription、Perf、i18n、Testing |

> 以上为预估开发人天，以 1 人月 22 天计，可根据资源投入弹性调整。

---

## 8. 监控 & 指标
- **业务**：PV、独立访客、平均停留时长、订阅转化率。
- **技术**：API 响应时间、前端报错率、Web Vitals（LCP/FID/CLS）。
- 数据看板：Grafana Dashboard #201；告警：Sentry + 钉钉群。

---

## 9. Out of Scope
- 离线缓存 / PWA
- 社交登录（Google、Twitter）
- 支付相关功能

---

## 10. 附录
- 高保真原型：Figma https://figma.com/file/xxx
- Backend PRD：`docs/docs_backend/prd/overview.md`
- API 参考：`docs/docs_backend/api/*`

---

> 如有疑问或新增需求，请在 Issue 中@产品经理并更新本文档。 