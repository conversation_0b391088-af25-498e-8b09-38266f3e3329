import { render, screen, waitFor } from '@testing-library/react'
import { describe, it, expect, vi, beforeEach } from 'vitest'
import { QueryClient, QueryClientProvider } from '@tanstack/react-query'
import EventDetailPage from '../page'

// Mock next-intl
const mockLocale = vi.fn(() => 'ja')
const mockTranslations = vi.fn((key: string) => {
  const translations: Record<string, string> = {
    'EventHeader.date': '日付',
    'EventHeader.venue': '会場',
    'EventHeader.address': '住所',
    'EventHeader.officialWebsite': '公式サイト',
    'EventHeader.defaultVenueName': '会場',
    'EventHeader.imageAlt': 'イベントポスター',
    'EventHeader.mapLoading': '地図を読み込み中...',
    'EventHeader.mapError': '地図の読み込みに失敗しました'
  }
  return translations[key] || key
})

vi.mock('next-intl', () => ({
  useLocale: () => mockLocale(),
  useTranslations: () => mockTranslations,
}))

// Mock next/navigation
vi.mock('next/navigation', () => ({
  useParams: () => ({ id: 'test-event' }),
}))

// Mock the API hooks
vi.mock('@/api/generated/ayafeedComponents', () => ({
  useGetEventsId: vi.fn(() => ({
    data: {
      id: 'test-event',
      name: 'Test Event',
      date: '2025-01-01',
      date_sort: 20250101,
    }
  })),
  useGetEventsIdCircles: vi.fn(() => ({
    data: []
  })),
  useGetAppearances: vi.fn(() => ({
    data: []
  })),
}))

// Mock other dependencies
vi.mock('@/hooks', () => ({
  useDebounce: (value: string) => value,
}))

vi.mock('@/lib/logger', () => ({
  log: vi.fn(),
}))

function createTestQueryClient() {
  return new QueryClient({
    defaultOptions: {
      queries: { retry: false },
      mutations: { retry: false },
    },
  })
}

function TestWrapper({ children }: { children: React.ReactNode }) {
  const queryClient = createTestQueryClient()
  return (
    <QueryClientProvider client={queryClient}>
      {children}
    </QueryClientProvider>
  )
}

describe('EventDetailPage Locale Switching', () => {
  beforeEach(() => {
    vi.clearAllMocks()
  })

  it('renders event detail page', () => {
    render(
      <TestWrapper>
        <EventDetailPage />
      </TestWrapper>
    )

    // 验证页面基本结构渲染正确
    expect(screen.getByText("Test Event")).toBeInTheDocument()
  })

  it('responds to locale changes', async () => {
    const { rerender } = render(
      <TestWrapper>
        <EventDetailPage />
      </TestWrapper>
    )

    // Change locale
    mockLocale.mockReturnValue('en')
    
    rerender(
      <TestWrapper>
        <EventDetailPage />
      </TestWrapper>
    )

    // The component should handle locale changes
    await waitFor(() => {
      expect(screen.getByText("Test Event")).toBeInTheDocument()
    })
  })
})
