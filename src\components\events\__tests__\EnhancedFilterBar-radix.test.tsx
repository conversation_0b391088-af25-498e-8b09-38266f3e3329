import { describe, test, expect, vi, beforeEach } from 'vitest'
import { render, screen, fireEvent } from '@testing-library/react'
import { renderWithProviders } from '@/__test__/test-utils'
import EnhancedFilterBar from '../EnhancedFilterBar'

// Mock props
const mockProps = {
  keyword: '',
  setKeyword: vi.fn(),
  categories: [],
  setCategories: vi.fn(),
  toggleCategory: vi.fn(),
  categoryOptions: [
    { id: 'tech', label: '科技' },
    { id: 'art', label: '艺术' },
    { id: 'music', label: '音乐' }
  ],
  viewMode: 'grid' as const,
  setViewMode: vi.fn(),
  sortBy: 'name',
  setSortBy: vi.fn(),
  total: 42
}

describe('EnhancedFilterBar (Radix Migration)', () => {
  beforeEach(() => {
    vi.clearAllMocks()
  })

  test('renders search input with placeholder', () => {
    renderWithProviders(<EnhancedFilterBar {...mockProps} />)
    
    const searchInput = screen.getByPlaceholderText('搜索摊位号、社团名称或作者...')
    expect(searchInput).toBeInTheDocument()
    expect(searchInput).toHaveValue('')
  })

  test('renders search input with current keyword value', () => {
    const propsWithKeyword = { ...mockProps, keyword: 'test search' }
    renderWithProviders(<EnhancedFilterBar {...propsWithKeyword} />)
    
    const searchInput = screen.getByPlaceholderText('搜索摊位号、社团名称或作者...')
    expect(searchInput).toHaveValue('test search')
  })

  test('calls setKeyword when search input changes', () => {
    renderWithProviders(<EnhancedFilterBar {...mockProps} />)
    
    const searchInput = screen.getByPlaceholderText('搜索摊位号、社团名称或作者...')
    fireEvent.change(searchInput, { target: { value: 'new search' } })
    
    expect(mockProps.setKeyword).toHaveBeenCalledWith('new search')
  })

  test('renders sort dropdown with current sort option', () => {
    renderWithProviders(<EnhancedFilterBar {...mockProps} />)
    
    // 检查排序按钮显示当前选项
    expect(screen.getByText('按名称排序')).toBeInTheDocument()
  })

  test('renders view mode buttons', () => {
    renderWithProviders(<EnhancedFilterBar {...mockProps} />)

    // 检查视图模式按钮组 - 使用更准确的选择器
    const viewModeButtons = screen.getAllByRole('button').filter(button => {
      const svg = button.querySelector('svg')
      return svg && (
        svg.classList.contains('lucide-grid3x3') ||
        svg.classList.contains('lucide-grid-3x3') ||
        svg.classList.contains('lucide-list') ||
        svg.classList.contains('lucide-map')
      )
    })

    expect(viewModeButtons).toHaveLength(3)
  })

  test('calls setViewMode when view mode button is clicked', () => {
    renderWithProviders(<EnhancedFilterBar {...mockProps} />)

    // 找到 list 视图按钮并点击
    const listButton = screen.getAllByRole('button').find(button =>
      button.querySelector('svg')?.classList.contains('lucide-list')
    )

    expect(listButton).toBeDefined()
    if (listButton) {
      fireEvent.click(listButton)
      expect(mockProps.setViewMode).toHaveBeenCalledWith('list')
    }
  })

  test('renders category filter badges', () => {
    renderWithProviders(<EnhancedFilterBar {...mockProps} />)
    
    // 检查分类标签
    expect(screen.getByText('科技')).toBeInTheDocument()
    expect(screen.getByText('艺术')).toBeInTheDocument()
    expect(screen.getByText('音乐')).toBeInTheDocument()
  })

  test('calls toggleCategory when category badge is clicked', () => {
    renderWithProviders(<EnhancedFilterBar {...mockProps} />)
    
    const techBadge = screen.getByText('科技')
    fireEvent.click(techBadge)
    
    expect(mockProps.toggleCategory).toHaveBeenCalledWith('tech')
  })

  test('shows clear filter button when categories are selected', () => {
    const propsWithCategories = { ...mockProps, categories: ['tech', 'art'] }
    renderWithProviders(<EnhancedFilterBar {...propsWithCategories} />)
    
    expect(screen.getByText('清除筛选')).toBeInTheDocument()
  })

  test('hides clear filter button when no categories are selected', () => {
    renderWithProviders(<EnhancedFilterBar {...mockProps} />)
    
    expect(screen.queryByText('清除筛选')).not.toBeInTheDocument()
  })

  test('calls setCategories when clear filter button is clicked', () => {
    const propsWithCategories = { ...mockProps, categories: ['tech', 'art'] }
    renderWithProviders(<EnhancedFilterBar {...propsWithCategories} />)
    
    const clearButton = screen.getByText('清除筛选')
    fireEvent.click(clearButton)
    
    expect(mockProps.setCategories).toHaveBeenCalledWith([])
  })

  test('displays correct total count', () => {
    renderWithProviders(<EnhancedFilterBar {...mockProps} />)

    // 使用更灵活的文本匹配，因为文本可能被分割在多个元素中
    expect(screen.getByText(/找到/)).toBeInTheDocument()
    expect(screen.getByText('42')).toBeInTheDocument()
    expect(screen.getByText(/个参展商/)).toBeInTheDocument()
  })

  test('shows filter count when categories are selected', () => {
    const propsWithCategories = { ...mockProps, categories: ['tech', 'art'] }
    renderWithProviders(<EnhancedFilterBar {...propsWithCategories} />)
    
    expect(screen.getByText('(已筛选 2 个分类)')).toBeInTheDocument()
  })

  test('renders reset all button', () => {
    renderWithProviders(<EnhancedFilterBar {...mockProps} />)
    
    expect(screen.getByText('重置所有')).toBeInTheDocument()
  })

  test('calls reset functions when reset all button is clicked', () => {
    renderWithProviders(<EnhancedFilterBar {...mockProps} />)
    
    const resetButton = screen.getByText('重置所有')
    fireEvent.click(resetButton)
    
    expect(mockProps.setKeyword).toHaveBeenCalledWith('')
    expect(mockProps.setCategories).toHaveBeenCalledWith([])
  })

  test('renders filter section title', () => {
    renderWithProviders(<EnhancedFilterBar {...mockProps} />)
    
    expect(screen.getByText('分类筛选')).toBeInTheDocument()
  })

  test('applies correct styling to selected category badges', () => {
    const propsWithCategories = { ...mockProps, categories: ['tech'] }
    renderWithProviders(<EnhancedFilterBar {...propsWithCategories} />)
    
    const techBadge = screen.getByText('科技')
    expect(techBadge).toHaveClass('active')
  })

  test('component renders without crashing', () => {
    const { container } = renderWithProviders(<EnhancedFilterBar {...mockProps} />)
    expect(container.firstChild).toBeInTheDocument()
  })

  test('handles empty category options gracefully', () => {
    const propsWithEmptyCategories = { ...mockProps, categoryOptions: [] }
    renderWithProviders(<EnhancedFilterBar {...propsWithEmptyCategories} />)
    
    // 应该仍然渲染其他部分
    expect(screen.getByText('分类筛选')).toBeInTheDocument()
    expect(screen.getByPlaceholderText('搜索摊位号、社团名称或作者...')).toBeInTheDocument()
  })

  test('search input has correct styling classes', () => {
    renderWithProviders(<EnhancedFilterBar {...mockProps} />)
    
    const searchInput = screen.getByPlaceholderText('搜索摊位号、社团名称或作者...')
    expect(searchInput).toHaveClass('pl-10', 'search-input-enhanced')
  })
})
