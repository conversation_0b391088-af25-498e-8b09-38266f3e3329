{"compilerOptions": {"target": "esnext", "module": "esnext", "moduleResolution": "bundler", "esModuleInterop": true, "skipLibCheck": true, "noEmit": true, "lib": ["esnext", "DOM"], "types": ["@cloudflare/workers-types", "@types/node"], "jsx": "react-jsx", "jsxImportSource": "hono/jsx", "baseUrl": ".", "paths": {"@/*": ["src/*"], "@/modules/*": ["src/modules/*"]}, "composite": true, "tsBuildInfoFile": "tsconfig.root.tsbuildinfo", "allowJs": true, "resolveJsonModule": true, "isolatedModules": true}, "files": [], "include": [], "references": [{"path": "./tsconfig.src.json"}, {"path": "./scripts/tsconfig.scripts.json"}, {"path": "./tsconfig.tests.json"}]}