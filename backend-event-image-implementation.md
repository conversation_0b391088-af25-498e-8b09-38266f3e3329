# Event 图片管理后端实施方案

## 📋 概述

本文档详细说明支持 Event 图片管理功能的后端实施方案，包括 API 增强、数据迁移和性能优化。

## 🎯 技术选择

### API 策略
- **职责分离**: Events API 专注事件数据，图片 API 专注图片数据
- **查询方式**: 前端通过图片 API 单独查询不同变体
- **兼容性**: 保持现有 `image_url` 字段向后兼容

### 数据策略
- **渐进式迁移**: 双轨制运行，逐步切换到新系统
- **变体管理**: 使用现有图片管理系统的 variant 功能
- **关联方式**: `resource_type='event'`, `resource_id=eventId`, `image_type='poster'`

## 🔧 API 增强方案

### 1. 图片查询 API 增强

#### 现有 API 分析
```
GET /images/{category}/{resourceId}
```

#### 需要增强的功能
- **变体过滤**: 支持 `variant` 查询参数
- **图片类型过滤**: 支持 `imageType` 查询参数
- **批量查询**: 支持多个 resourceId 批量查询

#### 增强后的 API 设计

**单个查询**:
```
GET /images/event/{eventId}?imageType=poster&variant=large

Response:
{
  "code": 200,
  "message": "success",
  "data": {
    "images": [
      {
        "id": "img-123",
        "variant": "large",
        "file_path": "/images/events/event-123/poster_large.jpg",
        "width": 1200,
        "height": 900,
        "file_size": 245760
      }
    ]
  }
}
```

**批量查询**:
```
GET /images/batch?events=event1,event2,event3&variant=medium&imageType=poster

Response:
{
  "code": 200,
  "message": "success", 
  "data": {
    "event1": {
      "id": "img-456",
      "variant": "medium",
      "file_path": "/images/events/event1/poster_medium.jpg"
    },
    "event2": {
      "id": "img-789", 
      "variant": "medium",
      "file_path": "/images/events/event2/poster_medium.jpg"
    },
    "event3": null
  }
}
```

### 2. 图片上传 API 确认

#### 现有上传 API
```
POST /admin/images/upload
```

#### 参数确认
- `category`: 'event'
- `resourceId`: eventId
- `imageType`: 'poster'
- `variant`: 'original' | 'large' | 'medium'
- `groupId`: 同一组图片的标识符

#### 上传流程
1. 前端生成 `groupId`
2. 依次上传三个变体，使用相同的 `groupId`
3. 每次上传返回图片信息

## 🗄️ 数据库设计

### 现有表结构确认

#### images 表
```sql
CREATE TABLE images (
  id TEXT PRIMARY KEY,
  group_id TEXT NOT NULL,           -- 同一组图片标识
  resource_type TEXT NOT NULL,      -- 'event'
  resource_id TEXT NOT NULL,        -- eventId
  image_type TEXT NOT NULL,         -- 'poster'
  variant TEXT NOT NULL,            -- 'original', 'large', 'medium'
  file_path TEXT NOT NULL,
  file_size INTEGER,
  width INTEGER,
  height INTEGER,
  format TEXT,
  created_at TEXT NOT NULL,
  updated_at TEXT NOT NULL
);
```

#### events 表
```sql
CREATE TABLE events (
  id TEXT PRIMARY KEY,
  -- ... 其他字段
  image_url TEXT,                   -- 保留用于向后兼容
  -- ... 其他字段
);
```

### 索引优化

#### 新增索引
```sql
-- 优化图片查询性能
CREATE INDEX idx_images_resource_lookup 
ON images(resource_type, resource_id, image_type, variant);

-- 优化批量查询性能  
CREATE INDEX idx_images_resource_variant
ON images(resource_type, resource_id, variant);

-- 优化按组查询
CREATE INDEX idx_images_group_variant
ON images(group_id, variant);
```

## 🔄 实施步骤

### 阶段1: API 增强 (Week 1)

#### 1.1 图片查询 API 增强
- [ ] 修改 `/images/{category}/{resourceId}` 支持 variant 过滤
- [ ] 添加 imageType 查询参数支持
- [ ] 优化查询性能和错误处理

#### 1.2 批量查询 API 开发
- [ ] 创建 `/images/batch` 端点
- [ ] 实现多 eventId 批量查询
- [ ] 添加适当的缓存策略

#### 1.3 现有上传 API 验证
- [ ] 确认上传 API 支持所需参数
- [ ] 测试 groupId 关联功能
- [ ] 验证文件存储路径生成

### 阶段2: 性能优化 (Week 2)

#### 2.1 数据库优化
- [ ] 添加必要的索引
- [ ] 优化查询语句性能
- [ ] 添加查询结果缓存

#### 2.2 API 性能优化
- [ ] 实现响应缓存
- [ ] 优化批量查询逻辑
- [ ] 添加请求频率限制

### 阶段3: 数据迁移 (Week 3-4)

#### 3.1 迁移策略设计
- [ ] 分析现有 image_url 数据
- [ ] 设计迁移脚本
- [ ] 制定回滚方案

#### 3.2 迁移脚本开发
- [ ] 批量下载现有图片
- [ ] 生成图片变体
- [ ] 导入到图片管理系统

#### 3.3 数据验证
- [ ] 验证迁移数据完整性
- [ ] 测试新旧系统兼容性
- [ ] 性能测试

## 🔧 技术实现细节

### 图片查询服务增强

#### Controller 层修改
```typescript
// 在现有 getImagesByResource 方法中添加过滤逻辑
export async function getImagesByResource(c: Context) {
  const { category, resourceId } = c.req.param();
  const variant = c.req.query('variant');
  const imageType = c.req.query('imageType');
  
  // 构建查询条件
  const filters = {
    resource_type: category,
    resource_id: resourceId,
    ...(variant && { variant }),
    ...(imageType && { image_type: imageType })
  };
  
  // 执行查询
  const images = await repo.findByFilters(filters);
  return jsonSuccess(c, '查询成功', { images });
}
```

#### Repository 层增强
```typescript
// 添加按过滤条件查询方法
async findByFilters(filters: ImageFilters): Promise<Image[]> {
  let query = this.db.prepare(`
    SELECT * FROM images 
    WHERE resource_type = ? AND resource_id = ?
  `);
  
  const params = [filters.resource_type, filters.resource_id];
  
  if (filters.variant) {
    query = this.db.prepare(`${query.source} AND variant = ?`);
    params.push(filters.variant);
  }
  
  if (filters.image_type) {
    query = this.db.prepare(`${query.source} AND image_type = ?`);
    params.push(filters.image_type);
  }
  
  return query.bind(...params).all();
}
```

### 批量查询实现

#### 新增 Controller
```typescript
export async function getBatchImages(c: Context) {
  const eventsParam = c.req.query('events');
  const variant = c.req.query('variant');
  const imageType = c.req.query('imageType') || 'poster';
  
  if (!eventsParam) {
    return jsonError(c, 40001, 'events 参数必填', 400);
  }
  
  const eventIds = eventsParam.split(',');
  const result = await repo.findBatchByEvents(eventIds, variant, imageType);
  
  return jsonSuccess(c, '批量查询成功', result);
}
```

#### Repository 批量查询
```typescript
async findBatchByEvents(
  eventIds: string[], 
  variant?: string, 
  imageType?: string
): Promise<Record<string, Image | null>> {
  const placeholders = eventIds.map(() => '?').join(',');
  let query = `
    SELECT * FROM images 
    WHERE resource_type = 'event' 
    AND resource_id IN (${placeholders})
  `;
  
  const params = [...eventIds];
  
  if (variant) {
    query += ' AND variant = ?';
    params.push(variant);
  }
  
  if (imageType) {
    query += ' AND image_type = ?';
    params.push(imageType);
  }
  
  const images = await this.db.prepare(query).bind(...params).all();
  
  // 组织返回结果
  const result: Record<string, Image | null> = {};
  eventIds.forEach(eventId => {
    const image = images.find(img => img.resource_id === eventId);
    result[eventId] = image || null;
  });
  
  return result;
}
```

## 🔄 数据迁移方案

### 迁移脚本设计

#### 1. 数据分析脚本
```sql
-- 分析现有 image_url 数据
SELECT 
  COUNT(*) as total_events,
  COUNT(image_url) as events_with_images,
  COUNT(CASE WHEN image_url LIKE 'http%' THEN 1 END) as external_urls,
  COUNT(CASE WHEN image_url LIKE '/%' THEN 1 END) as relative_paths
FROM events;
```

#### 2. 迁移执行脚本
```typescript
async function migrateEventImages() {
  // 1. 获取所有有图片的 events
  const eventsWithImages = await db.prepare(`
    SELECT id, image_url FROM events 
    WHERE image_url IS NOT NULL AND image_url != ''
  `).all();
  
  for (const event of eventsWithImages) {
    try {
      // 2. 下载原图
      const imageBuffer = await downloadImage(event.image_url);
      
      // 3. 生成三个变体
      const variants = await generateImageVariants(imageBuffer);
      
      // 4. 上传到 R2 并保存到数据库
      const groupId = uuidv4();
      for (const [variant, buffer] of Object.entries(variants)) {
        await uploadAndSaveImage(event.id, variant, buffer, groupId);
      }
      
      console.log(`✅ 迁移完成: ${event.id}`);
    } catch (error) {
      console.error(`❌ 迁移失败: ${event.id}`, error);
    }
  }
}
```

### 回滚方案
- 保留原始 `image_url` 数据
- 迁移过程中记录操作日志
- 提供回滚脚本删除迁移的图片数据

## 🚀 部署和监控

### 环境配置
- 确保 R2 存储桶配置正确
- 验证数据库连接和权限
- 配置适当的缓存策略

### 错误处理
- 图片不存在时的优雅降级
- 网络错误的重试机制
- 详细的错误日志记录

## 🔧 安全考虑

### API 安全
- **身份验证**: 上传 API 需要管理员权限
- **请求验证**: 严格验证所有输入参数
- **频率限制**: 防止 API 滥用

### 数据安全
- **文件验证**: 验证上传文件的真实性
- **路径安全**: 防止路径遍历攻击
- **访问控制**: 合理的文件访问权限

### 存储安全
- **R2 配置**: 适当的存储桶权限设置
- **备份策略**: 重要图片数据的备份
- **清理机制**: 定期清理无用的图片文件

## 📚 相关文档

- [前端实施方案](./frontend-event-image-implementation.md)
- [图片管理系统架构](./image-management-system.md)
- [数据迁移指南](./data-migration-guide.md)
- [API 文档](./api-documentation.md)
