/**
 * 语言相关的工具函数
 * 简化版本，避免循环依赖
 */

export const SUPPORTED_LOCALES = ['zh', 'ja', 'en'] as const;
export type Locale = typeof SUPPORTED_LOCALES[number];

/**
 * 获取当前语言设置
 * 简化版本，避免循环依赖
 */
export function getCurrentLocale(): Locale {
  // 在浏览器环境中，从URL或localStorage获取语言
  if (typeof window !== 'undefined') {
    // 从localStorage获取
    const stored = localStorage.getItem('locale');
    if (stored && SUPPORTED_LOCALES.includes(stored as Locale)) {
      return stored as Locale;
    }

    // 从浏览器语言获取
    const browserLang = navigator.language.split('-')[0];
    if (SUPPORTED_LOCALES.includes(browserLang as Locale)) {
      return browserLang as Locale;
    }
  }

  // 默认返回中文
  return 'zh';
}

/**
 * 验证语言代码是否有效
 */
export function isValidLocale(locale: string): locale is Locale {
  return SUPPORTED_LOCALES.includes(locale as Locale);
}

/**
 * 设置语言到Cookie
 */
export function setLocaleToCookie(locale: Locale): void {
  if (!isValidLocale(locale)) {
    console.warn(`Invalid locale: ${locale}`);
    return;
  }

  // 设置Cookie（365天过期）
  if (typeof document !== 'undefined') {
    const maxAge = 365 * 24 * 60 * 60; // 365 days in seconds
    document.cookie = `locale=${locale}; path=/; max-age=${maxAge}; samesite=lax`;
  }

  // 同时设置到localStorage
  if (typeof window !== 'undefined') {
    localStorage.setItem('locale', locale);
  }
}