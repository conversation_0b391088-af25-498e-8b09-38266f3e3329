import type { D1Database } from '@cloudflare/workers-types';
import { v4 as uuidv4 } from 'uuid';

/**
 * Bookmark 数据访问仓库
 */
export interface BookmarkRepository {
  /**
   * 切换收藏状态，返回最新 isBookmarked 状态
   */
  toggle(userId: string, circleId: string): Promise<boolean>;
}

/**
 * 工厂：根据不同数据库返回实现
 */
export function createBookmarkRepository(db: D1Database): BookmarkRepository {
  return {
    async toggle(userId, circleId) {
      // 检查是否已存在记录
      const existing = await db
        .prepare('SELECT id FROM bookmarks WHERE user_id = ? AND circle_id = ?')
        .bind(userId, circleId)
        .first<{ id: string }>();

      if (existing) {
        // 已收藏 -> 取消
        await db
          .prepare('DELETE FROM bookmarks WHERE user_id = ? AND circle_id = ?')
          .bind(userId, circleId)
          .run();
        return false;
      }

      // 未收藏 -> 新增
      const id = uuidv4();
      await db
        .prepare(
          'INSERT INTO bookmarks (id, user_id, circle_id) VALUES (?, ?, ?)'
        )
        .bind(id, userId, circleId)
        .run();
      return true;
    },
  };
}
