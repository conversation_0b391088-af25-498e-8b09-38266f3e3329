"use client"

import { skipToken, useQueryClient } from "@tanstack/react-query"
import { useParams } from "next/navigation"
import { useEffect, useState, useMemo } from "react"
import { useLocale } from "next-intl"

import {
  useGetEventsId,
  useGetEventsIdCircles,
  useGetAppearances
} from "@/api/generated/ayafeedComponents"
import EnhancedEventHeader from "@/components/events/EnhancedEventHeader"
import EventDetailTabs from "@/components/events/EventDetailTabs"
import { EventHeaderSkeleton } from "@/components/events/EnhancedSkeleton"
import { useDebounce } from "@/hooks"
import { log } from "@/lib/logger"
import {
  transformEventDataWithLocale,
  transformCirclesData,
  filterCircles,
  extractCategoryOptions
} from "./utils"

/**
 * 事件详情页面组件
 *
 * 功能：
 * - 显示事件基本信息
 * - 展示参与社团列表
 * - 支持社团搜索和分类筛选
 * - 支持多语言切换
 */
export default function EventDetailPage() {
  const { id } = useParams<{ id: string }>()
  const locale = useLocale()
  const queryClient = useQueryClient()

  // 搜索和筛选状态
  const [keyword, setKeyword] = useState("")
  const [categories, setCategories] = useState<string[]>([])
  const debouncedKeyword = useDebounce(keyword, 300)

  // 获取事件基本信息
  const {
    data: eventData,
    isLoading: isEventLoading,
    error: eventError
  } = useGetEventsId(
    id ? { pathParams: { id } } : skipToken,
    {
      select: (data) => data ? transformEventDataWithLocale(data, locale) : null,
      staleTime: 5 * 60 * 1000, // 5分钟缓存
    }
  )

  // 获取社团列表
  const {
    data: circlesData,
    isLoading: isCirclesLoading
  } = useGetEventsIdCircles(
    id ? { pathParams: { id } } : skipToken,
    {
      enabled: !!id,
      staleTime: 5 * 60 * 1000,
    }
  )

  // 获取参展记录（用于获取 booth_id）
  const {
    data: appearancesData,
    isLoading: isAppearancesLoading
  } = useGetAppearances(
    id ? {
      queryParams: { event_id: id, page: "1", pageSize: "800" }
    } : skipToken,
    {
      select: (data) => data?.items ?? [],
      enabled: !!id,
      staleTime: 5 * 60 * 1000,
    }
  )

  // 数据处理和计算
  const circles = useMemo(() => {
    if (!circlesData || !appearancesData) return []
    return transformCirclesData(circlesData, appearancesData)
  }, [circlesData, appearancesData])

  const categoryOptions = useMemo(() => {
    return extractCategoryOptions(circles)
  }, [circles])

  const filteredCircles = useMemo(() => {
    return filterCircles(circles, debouncedKeyword, categories)
  }, [circles, debouncedKeyword, categories])

  // const total = filteredCircles.length // 移到 EventDetailTabs 组件内部处理

  // 调试用输出
  useEffect(() => {
    if (process.env.NODE_ENV === "development") {
      log("eventData", eventData)
    }
  }, [eventData])

  // 监听语言变化，重新获取数据
  useEffect(() => {
    if (id) {
      queryClient.invalidateQueries({
        predicate: (query) => {
          const queryKey = query.queryKey
          return queryKey.some(key =>
            typeof key === 'string' && key === id ||
            (typeof key === 'object' && key !== null && 'id' in key && (key as any).id === id)
          )
        }
      })
    }
  }, [locale, id, queryClient])

  // 事件处理函数
  const toggleCategory = (categoryId: string) => {
    setCategories((prev) =>
      prev.includes(categoryId) ? prev.filter((c) => c !== categoryId) : [...prev, categoryId]
    )
  }

  // 加载状态
  const isLoading = isEventLoading || isCirclesLoading || isAppearancesLoading

  // 错误处理
  if (eventError) {
    return (
      <div className="min-h-screen bg-background text-foreground flex items-center justify-center">
        <div className="text-center">
          <h1 className="text-2xl font-bold text-red-600 mb-2">加载失败</h1>
          <p className="text-gray-600">无法加载事件信息，请稍后重试</p>
        </div>
      </div>
    )
  }

  // 如果事件数据加载中，显示骨架屏
  if (isEventLoading && !eventData) {
    return (
      <div className="min-h-screen bg-background text-foreground">
        <EventHeaderSkeleton />
        <main className="max-w-7xl mx-auto px-4 py-10">
          <div className="space-y-6">
            <div className="skeleton h-10 w-full rounded-lg" />
            <div className="grid grid-cols-[repeat(auto-fill,minmax(220px,1fr))] gap-4">
              {Array.from({ length: 12 }).map((_, i) => (
                <div key={i} className="skeleton h-32 rounded-lg" />
              ))}
            </div>
          </div>
        </main>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-background text-foreground">
      {/* 增强版页面头部 */}
      <EnhancedEventHeader
        event={eventData ?? null}
        circlesCount={circles.length}
      />

      {/* 主体内容 - 使用标签页布局 */}
      <main className="max-w-7xl mx-auto px-4 py-10">
        <EventDetailTabs
          event={eventData ?? null}
          circles={circles}
          filteredCircles={filteredCircles}
          keyword={keyword}
          setKeyword={setKeyword}
          categories={categories}
          setCategories={setCategories}
          toggleCategory={toggleCategory}
          categoryOptions={categoryOptions}
          isLoading={isLoading}
        />
      </main>
    </div>
  )
}
