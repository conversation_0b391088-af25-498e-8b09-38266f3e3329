import { VirtuosoGrid } from "react-virtuoso";
import Link from "next/link";
import Image from "next/image";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import CirclesEmptyState from "./CirclesEmptyState";

interface Circle {
  id: string;
  name: string;
  category?: string | null;
  urls?: string | null;
  created_at: string;
  updated_at: string;
}

interface CirclesListProps {
  circles: Circle[];
  isLoading?: boolean;
  isLoadingMore?: boolean;
  hasMore?: boolean;
  searchKeyword?: string;
  onLoadMore?: () => void;
  onResetFilters?: () => void;
  total?: number;
}

export default function CirclesList({
  circles,
  isLoading,
  isLoadingMore,
  hasMore,
  searchKeyword,
  onLoadMore,
  onResetFilters,
  total
}: CirclesListProps) {
  if (isLoading) {
    return <CirclesListSkeleton />;
  }

  if (circles.length === 0 && !isLoading) {
    return (
      <CirclesEmptyState
        type="no-results"
        searchKeyword={searchKeyword}
        onReset={onResetFilters}
      />
    );
  }

  return (
    <div className="w-full">
      <VirtuosoGrid
        data={circles}
        style={{ height: "calc(100vh - 200px)" }}
        listClassName="w-full grid grid-cols-[repeat(auto-fill,minmax(220px,1fr))] gap-4"
        itemClassName="w-full"
        overscan={200}
        endReached={onLoadMore}
        itemContent={(index, circle) => (
          <CircleCard key={circle.id} circle={circle} />
        )}
        components={{
          Footer: () => (
            <InfiniteScrollFooter
              isLoadingMore={isLoadingMore}
              hasMore={hasMore}
              total={circles.length}
              totalAvailable={total}
            />
          )
        }}
      />
    </div>
  );
}

// 社团卡片组件
function CircleCard({ circle }: { circle: Circle }) {
  // 解析 URLs JSON
  let logoUrl = "/images/circles/placeholder.svg";
  let authorName = "";
  let twitterUrl = "";
  let webUrl = "";

  try {
    const urls = JSON.parse(circle.urls || "{}");
    logoUrl = urls.logo_url || logoUrl;
    authorName = urls.author || "";
    twitterUrl = urls.twitter_url || "";
    webUrl = urls.web_url || "";
  } catch {
    // 使用默认值
  }

  return (
    <Link
      href={`/circles/${circle.id}`}
      className="h-full focus:outline-none focus:ring-2 focus:ring-primary focus:ring-offset-2 rounded-xl"
      aria-label={`查看 ${circle.name} 的详细信息${authorName ? `，作者：${authorName}` : ""}`}
    >
      <Card className="h-full cursor-pointer transition-all duration-200 hover:shadow-md hover:-translate-y-0.5 group border border-border/50 hover:border-primary/30 bg-card">
        <CardHeader className="pb-3 px-4 pt-4">
          <div className="flex items-start gap-3">
            <div className="flex-shrink-0">
              <Image
                src={logoUrl}
                alt={`${circle.name} 的 logo`}
                width={40}
                height={40}
                className="w-10 h-10 rounded-lg object-cover ring-1 ring-border group-hover:ring-primary/40 transition-all duration-200"
                onError={(e) => {
                  (e.target as HTMLImageElement).src = "/images/circles/placeholder.svg";
                }}
              />
            </div>
            <div className="flex-1 min-w-0 space-y-1">
              <CardTitle className="text-sm font-semibold leading-tight group-hover:text-primary transition-colors duration-200 line-clamp-2">
                {circle.name}
              </CardTitle>
              {authorName && (
                <p className="text-xs text-muted-foreground line-clamp-1">
                  {authorName}
                </p>
              )}
            </div>
          </div>
        </CardHeader>
        <CardContent className="px-4 pb-4 pt-0">
          <div className="flex flex-wrap gap-1.5" role="list" aria-label="社团标签">
            {circle.category && (
              <Badge
                variant="secondary"
                className="text-xs px-2 py-0.5 rounded-md bg-primary/10 text-primary border-0 font-medium"
                role="listitem"
              >
                {circle.category}
              </Badge>
            )}
            {twitterUrl && (
              <Badge
                variant="outline"
                className="text-xs px-2 py-0.5 rounded-md border-border/60 hover:border-primary/40 transition-colors font-medium"
                role="listitem"
              >
                Twitter
              </Badge>
            )}
            {webUrl && (
              <Badge
                variant="outline"
                className="text-xs px-2 py-0.5 rounded-md border-border/60 hover:border-primary/40 transition-colors font-medium"
                role="listitem"
              >
                Web
              </Badge>
            )}
          </div>
        </CardContent>
      </Card>
    </Link>
  );
}

// 无限滚动底部组件
function InfiniteScrollFooter({
  isLoadingMore,
  hasMore,
  total,
  totalAvailable
}: {
  isLoadingMore?: boolean;
  hasMore?: boolean;
  total: number;
  totalAvailable?: number;
}) {
  if (isLoadingMore) {
    return (
      <div className="col-span-full flex justify-center py-8">
        <div className="flex items-center gap-3 text-muted-foreground">
          <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-primary" />
          <span className="text-sm">加载更多社团中...</span>
        </div>
      </div>
    );
  }

  if (!hasMore && total > 0) {
    return (
      <div className="col-span-full text-center py-8">
        <div className="text-muted-foreground space-y-1">
          <p className="text-sm">已显示全部社团</p>
          <p className="text-xs">
            共 {total} 个社团
            {totalAvailable && totalAvailable !== total && ` (筛选自 ${totalAvailable} 个)`}
          </p>
        </div>
      </div>
    );
  }

  return null;
}

// 骨架屏组件
function CirclesListSkeleton() {
  return (
    <div className="grid grid-cols-[repeat(auto-fill,minmax(220px,1fr))] gap-4">
      {Array.from({ length: 20 }).map((_, index) => (
        <Card key={index} className="h-full border border-border/50 bg-card">
          <CardHeader className="pb-3 px-4 pt-4">
            <div className="flex items-start gap-3">
              <div className="w-10 h-10 rounded-lg bg-muted animate-pulse flex-shrink-0" />
              <div className="flex-1 space-y-1">
                <div className="h-4 bg-muted rounded animate-pulse" />
                <div className="h-3 bg-muted rounded animate-pulse w-2/3" />
              </div>
            </div>
          </CardHeader>
          <CardContent className="px-4 pb-4 pt-0">
            <div className="flex gap-1.5">
              <div className="h-5 bg-muted rounded-md w-12 animate-pulse" />
              <div className="h-5 bg-muted rounded-md w-10 animate-pulse" />
            </div>
          </CardContent>
        </Card>
      ))}
    </div>
  );
}
