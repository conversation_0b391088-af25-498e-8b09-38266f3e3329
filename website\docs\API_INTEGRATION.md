# API集成指南

> 基于后端文档包的前端API集成最佳实践

## 概述

本项目采用现代化的API集成方案，结合了以下技术：

- **@openapi-codegen** - 基于OpenAPI规范自动生成React Query hooks
- **@tanstack/react-query** - 强大的服务器状态管理
- **统一错误处理** - 集成的错误处理和用户反馈机制
- **多语言支持** - 自动处理语言头和本地化内容
- **认证管理** - JWT token自动管理和刷新

## 核心架构

### 1. API代码生成

```bash
# 生成TypeScript类型定义
pnpm gen:api

# 生成React Query hooks
pnpm gen:rq

# 同步API（生成+检查变更）
pnpm sync:api
```

### 2. 统一错误处理

所有API调用都会经过统一的错误处理流程：

```typescript
// 自动错误处理
import { useEvents } from '@/hooks/useEvents';

function EventList() {
  const { data, error, isLoading } = useEvents();
  
  // 错误会自动显示Toast提示
  // 401错误会自动跳转到登录页
  // 网络错误会提示重试
  
  return <div>{/* 组件内容 */}</div>;
}
```

### 3. 多语言支持

API调用会自动添加语言头：

```typescript
// 自动添加 X-Locale 头
const { data } = useEvents(); // 会根据当前语言设置发送请求

// 手动处理多语言内容
import { useLocalization } from '@/hooks/useLocalization';

function EventCard({ event }) {
  const { getLocalizedText } = useLocalization();
  
  const title = getLocalizedText(event.name); // 自动选择合适的语言版本
  
  return <h3>{title}</h3>;
}
```

### 4. 认证管理

Token会自动管理和刷新：

```typescript
// 登录
import { useAuthStore } from '@/stores/auth';

function LoginForm() {
  const { login } = useAuthStore();
  
  const handleLogin = async (credentials) => {
    await login(credentials); // 自动存储token
    // 后续API调用会自动添加Authorization头
  };
}
```

## 使用示例

### 基础数据获取

```typescript
import { useEvents } from '@/hooks/useEvents';

function EventList() {
  const { 
    data: events, 
    isLoading, 
    error,
    refetch 
  } = useEvents({
    page: 1,
    pageSize: 10
  });

  if (isLoading) return <div>加载中...</div>;
  if (error) return <div>加载失败 <button onClick={refetch}>重试</button></div>;

  return (
    <div>
      {events?.items.map(event => (
        <EventCard key={event.id} event={event} />
      ))}
    </div>
  );
}
```

### 数据变更

```typescript
import { useCreateEvent, useUpdateEvent } from '@/hooks/useEvents';

function EventForm({ event }) {
  const createEvent = useCreateEvent();
  const updateEvent = useUpdateEvent();
  
  const handleSubmit = async (data) => {
    if (event?.id) {
      await updateEvent.mutateAsync({ id: event.id, data });
    } else {
      await createEvent.mutateAsync(data);
    }
    // 相关查询会自动刷新
  };
  
  return (
    <form onSubmit={handleSubmit}>
      {/* 表单内容 */}
    </form>
  );
}
```

### 搜索功能

```typescript
import { useSearch } from '@/hooks/useSearch';

function SearchPage() {
  const [query, setQuery] = useState('');
  
  const { data: results } = useSearch({
    q: query,
    type: 'events'
  });
  
  return (
    <div>
      <input 
        value={query}
        onChange={(e) => setQuery(e.target.value)}
        placeholder="搜索..."
      />
      
      {results?.results.map(result => (
        <SearchResult key={result.id} result={result} />
      ))}
    </div>
  );
}
```

## 最佳实践

### 1. 错误处理

```typescript
// ✅ 推荐：使用统一的错误处理
const { data, error } = useEvents();

// ❌ 避免：手动处理每个错误
try {
  const data = await fetch('/api/events');
} catch (error) {
  // 手动错误处理
}
```

### 2. 缓存策略

```typescript
// ✅ 推荐：利用React Query的缓存
const { data } = useEvents(); // 自动缓存5分钟

// ✅ 推荐：手动控制缓存
const { data, refetch } = useEvents();
const handleRefresh = () => refetch();
```

### 3. 权限控制

```typescript
// ✅ 推荐：使用权限组件
import { ConditionalRender } from '@/components/ConditionalRender';

<ConditionalRender permission="canManageEvents">
  <CreateEventButton />
</ConditionalRender>

// ✅ 推荐：使用权限Hook
import { usePermissions } from '@/hooks/usePermissions';

function AdminPanel() {
  const { canManageEvents } = usePermissions();
  
  if (!canManageEvents) return <div>无权限访问</div>;
  
  return <div>{/* 管理面板 */}</div>;
}
```

## 故障排除

### 常见问题

1. **API调用失败**
   - 检查环境变量 `NEXT_PUBLIC_API_URL` 是否正确设置
   - 确认后端服务是否正常运行

2. **类型错误**
   - 运行 `pnpm gen:api` 重新生成类型定义
   - 检查 `openapi.json` 文件是否是最新版本

3. **认证问题**
   - 检查token是否过期
   - 确认登录状态是否正常

4. **多语言问题**
   - 检查语言设置是否正确
   - 确认API响应是否包含多语言内容

### 调试工具

```typescript
// 开发环境调试
if (process.env.NODE_ENV === 'development') {
  // 查看认证状态
  (window as any).debugAuth = () => {
    console.log('Auth State:', useAuthStore.getState());
  };
  
  // 查看语言设置
  (window as any).debugI18n = () => {
    console.log('Current Locale:', i18nService.getCurrentLocale());
  };
}
```

## 更多资源

- [后端API文档](./api/README)
- [前端文档包](./frontend/README.md)
- [错误处理指南](./frontend/error-handling.md)
- [多语言集成指南](./frontend/i18n-integration.md)
