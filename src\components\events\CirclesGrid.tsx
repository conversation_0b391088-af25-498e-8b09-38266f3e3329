import { VirtuosoGrid } from "react-virtuoso"

import CircleTextCard from "@/components/circle-text-card"
import { CircleAppearance } from "@/schemas"

interface CirclesGridProps {
  data: CircleAppearance[]
}

export default function CirclesGrid({ data }: CirclesGridProps) {
  return (
    <div data-testid="circles-grid">
      <VirtuosoGrid
        data={data}
        style={{ height: "70vh" }}
        listClassName="w-full grid grid-cols-[repeat(auto-fill,minmax(220px,1fr))] gap-4"
        itemClassName="flex"
        overscan={400}
        itemContent={(index, circle) => (
          <CircleTextCard key={`${circle.circle_id}-${circle.booth_id}`} data={circle} />
        )}
      />
    </div>
  )
}