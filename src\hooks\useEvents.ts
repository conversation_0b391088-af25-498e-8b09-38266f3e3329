/**
 * 事件相关Hook
 * 基于后端文档包的常用示例
 */

import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { request } from '@/lib/http';
import { useLocalization } from './useLocalization';
import { createLocaleQueryKey } from '@/utils/localization';

interface Event {
  id: string;
  name: string;
  description: string;
  date: string;
  venue_name: string;
}

interface EventsResponse {
  items: Event[];
  total: number;
  page: number;
  pageSize: number;
}

// 获取事件列表
export function useEvents(params?: { page?: number; pageSize?: number }) {
  const { locale } = useLocalization();
  
  return useQuery({
    queryKey: createLocaleQueryKey(['events', params], locale),
    queryFn: () => request<EventsResponse>('/events', { params }),
    staleTime: 5 * 60 * 1000, // 5分钟
  });
}

// 获取事件详情
export function useEvent(id: string) {
  const { locale } = useLocalization();
  
  return useQuery({
    queryKey: createLocaleQueryKey(['events', id], locale),
    queryFn: () => request<Event>(`/events/${id}`),
    enabled: !!id,
    staleTime: 10 * 60 * 1000, // 10分钟
  });
}

// 创建事件（管理员功能）
export function useCreateEvent() {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: (eventData: Partial<Event>) => 
      request<Event>('/admin/events', {
        method: 'POST',
        body: JSON.stringify(eventData),
      }),
    onSuccess: () => {
      // 刷新事件列表
      queryClient.invalidateQueries({ queryKey: ['events'] });
    },
  });
}

// 更新事件
export function useUpdateEvent() {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: ({ id, data }: { id: string; data: Partial<Event> }) => 
      request<Event>(`/admin/events/${id}`, {
        method: 'PUT',
        body: JSON.stringify(data),
      }),
    onSuccess: (_, { id }) => {
      // 刷新相关查询
      queryClient.invalidateQueries({ queryKey: ['events'] });
      queryClient.invalidateQueries({ queryKey: ['events', id] });
    },
  });
}

// 删除事件
export function useDeleteEvent() {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: (id: string) => 
      request(`/admin/events/${id}`, {
        method: 'DELETE',
      }),
    onSuccess: () => {
      // 刷新事件列表
      queryClient.invalidateQueries({ queryKey: ['events'] });
    },
  });
}
