// src/hooks/useAdminSuccessToast.ts
import {
  UseMutationOptions,
  MutationFunction,
  useMutation,
} from "@tanstack/react-query";
import { toast } from "sonner";
import { showApiError } from "@/lib/show-error";

/**
 * useAdminSuccessToast
 *
 * 统一封装后台管理写操作（Create/Update/Delete）时的 mutation。
 * 功能：
 * 1. 当后端已返回 `X-Success-Message` 时，会由 request() 自动提示，此处无副作用。
 * 2. 当后端暂未返回该 Header 时，此 hook 会使用默认文案或传入文案进行提示，避免无反馈。
 * 3. 保留调用方原有 onSuccess 行为（如缓存失效、跳转）。
 *
 * 用法示例：
 * ```ts
 * const createCircle = useAdminSuccessToast(
 *   (payload) => request("/admin/circles", { method: "POST", body: JSON.stringify(payload) }),
 *   {
 *     onSuccess: () => qc.invalidateQueries({ queryKey: queryKeys.adminCircles() }),
 *   },
 *   "创建成功"
 * );
 * ```
 */
export function useAdminSuccessToast<TData = unknown, TError = unknown, TVariables = void, TContext = unknown>(
  mutationFn: MutationFunction<TData, TVariables>,
  options: UseMutationOptions<TData, TError, TVariables, TContext> = {},
  fallbackMessage = "操作成功"
) {
  const mergedOptions: UseMutationOptions<TData, TError, TVariables, TContext> = {
    ...options,
    mutationFn,
    onSuccess: (data, variables, context) => {
      // 直接显示成功提示，不使用防抖
      console.log('useAdminSuccessToast: 显示成功提示:', fallbackMessage);
      toast.success(fallbackMessage);
      options.onSuccess?.(data, variables, context);
    },
    onError: (error, variables, context) => {
      // 统一错误提示
      // 根据成功提示文案自动推导失败提示，若无匹配则使用通用“操作失败”
      const fallbackErrorMessage = fallbackMessage.endsWith("成功")
        ? fallbackMessage.replace(/成功$/, "失败")
        : "操作失败";

      showApiError(error, fallbackErrorMessage);
      // 保留调用方原有 onError 行为
      options.onError?.(error, variables, context);
    },
  };

  return useMutation<TData, TError, TVariables, TContext>(mergedOptions);
}

// 声明全局变量用于简单的时间间隔去重
declare global {
   
  interface Window {
    __lastSuccessToastTime?: number;
  }
} 