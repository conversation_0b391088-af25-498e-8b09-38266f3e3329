# CI/CD

## Pipeline 概览

| Stage | 任务 | 工具 |
|-------|------|------|
| lint  | ESLint + Markdownlint | GitHub Actions |
| test  | Vitest 单元 / 集成测试 | GitHub Actions |
| build | `pnpm build` 产出静态文件 | Vercel / Cloudflare Pages |
| deploy | 自动发布 Preview / Prod | Vercel 环境 |

### 关键脚本

- `.github/workflows/ci.yml`：主 CI 工作流，拉起 lint + test + build
- `.github/workflows/deploy.yml`：标签推送后触发生产部署
- `.husky/pre-commit`：本地 pre-commit 钩子，执行 `pnpm lint` 与 `pnpm sync:api`

> 需在仓库 Secrets 设置 `VERCEL_TOKEN`、`VERCEL_ORG_ID`、`VERCEL_PROJECT_ID`。 