# 富文本编辑器集成方案

## 概述

本文档说明了在同人展会信息聚合平台中引入富文本编辑器的设计方案，以及对应的 EventDetailTabs 标签页架构调整。

## 设计背景

### 用户需求分析

同人展会（同好即卖会）的用户具有以下特点和需求：
- **明确目标**：用户通常有明确的目标作者/社团，主要痛点是"找不到具体摊位位置"
- **信息获取**：需要详细的社团信息、作品介绍、参展详情
- **社交属性**：重视作者介绍、作品展示、社交媒体链接等
- **内容管理**：社团需要自主编辑和维护展位信息

### 技术决策

经过讨论，决定采用**三层级富文本编辑架构**：
1. **展会级别** - 展会整体介绍和参观指南
2. **场馆级别** - 场馆设施和规则说明  
3. **社团级别** - 社团详情和作品展示

## EventDetailTabs 标签页重新设计

### 当前标签页结构

现有的 4 个标签页：
- 概览 (Overview)
- 参展商 (Exhibitors) 
- 会场信息 (Venue)
- 交通住宿 (Travel)

### 新的标签页架构

#### 1. 摊位导航 (Booth Navigation)
**目标**：解决核心痛点"找不到想要的摊位"

**内容结构**：
- 结构化数据 + 富文本预览
- 摊位列表和智能搜索筛选
- 社团卡片显示富文本内容摘要
- 点击展开查看完整社团介绍
- 摊位地图定位和路线规划
- 个人收藏和参观计划功能



#### 2. 展会介绍 (Event Introduction)
**目标**：提供展会整体信息和参观指导

**富文本编辑器应用**：
- **展会详细介绍** - 背景、主题、特色
- **参观亮点推荐** - 重点社团、必看作品
- **特殊活动说明** - 签售会、比赛、抽奖等
- **参观指南** - 最佳路线、时间安排建议
- **注意事项** - 规则、禁止事项、安全提醒



#### 3. 会场信息 (Venue Information)
**目标**：提供详细的场馆信息和实用指南

**富文本编辑器应用**：
- **场馆介绍** - 布局说明
- **设施详情** - 洗手间、休息区、餐饮、ATM等，提供地图
- **交通指南** - 详细路线、停车、公共交通
- **场馆规则** - 入场须知、安全规定


#### 4. 活动&交流 (Activities & Communication)
**目标**：展示活动信息和促进社交交流

**富文本编辑器应用**：
- **活动详细介绍** - 论坛、比赛、表演等
- **时间安排** - 详细的活动时间表
- **参与方式** - 报名、规则、奖品等
- **交流信息** - 聚会、after party、社群等

## 社团详情页面设计

### 页面结构

从摊位导航点击进入，提供完整的社团信息展示：

#### 社团介绍部分
- **社团/作者介绍** - 背景、风格、创作理念

#### 本次参展信息
- **新作品预告** - 这次带来的新作品详情
- **商品清单** - 价格、数量、特殊说明

#### 联系和社交
- **社交媒体** - Twitter、个人网站、pixiv等链接
- **特殊说明** - 限量商品、预约方式等

## 富文本编辑器功能需求

### 基础功能
- 格式化文本（标题、段落、列表）
- 文本样式（粗体、斜体、下划线、颜色）
- 链接插入和管理
- 图片上传和展示
- 表格创建和编辑

### 高级功能
- 内容预览和发布
- 版本历史和回滚
- 协作编辑（如果需要）
- 内容模板和快速插入
- 移动端适配

### 安全和规范
- 内容过滤和审核
- XSS 防护
- 图片大小和格式限制
- 内容长度限制

## 用户权限设计

### 编辑权限分层
- **展会管理员** - 可编辑展会级和场馆级富文本
- **社团用户** - 可编辑自己的社团级富文本
- **登录用户** - 可编辑订阅社团列表
- **访客** - 只可查看

### 内容管理流程
1. **注册和认证** - 社团用户注册和身份验证
2. **内容编辑** - 使用富文本编辑器创建和编辑内容
3. **内容审核** - 管理员审核用户提交的内容
4. **发布和更新** - 内容发布和后续更新管理

## 技术实现考虑

### 数据存储
- 结构化数据存储在关系型数据库
- 富文本内容可考虑单独存储或使用 JSON 字段
- 图片等媒体文件使用对象存储

### 性能优化
- 富文本内容懒加载
- 图片压缩和 CDN 加速
- 搜索功能基于结构化数据，富文本内容作为补充

### 移动端适配
- 响应式富文本显示
- 移动端编辑器优化
- 触摸友好的交互设计

## 实施计划

### 第一阶段：基础架构
1. 设计和实现富文本编辑器组件
2. 调整 EventDetailTabs 标签页结构
3. 实现基础的内容管理功能

### 第二阶段：功能完善
1. 添加高级编辑功能
2. 实现用户权限和内容审核
3. 优化移动端体验

### 第三阶段：扩展功能
1. 添加协作编辑功能
2. 实现内容模板和快速插入
3. 集成更多社交和互动功能

## 总结

通过引入三层级富文本编辑器，我们可以为同人展会平台提供更丰富的内容展示能力，同时保持良好的搜索和导航体验。新的 EventDetailTabs 架构更贴合用户需求，将最重要的摊位导航功能放在首位，同时为展会、场馆和社团信息提供充分的展示空间。
