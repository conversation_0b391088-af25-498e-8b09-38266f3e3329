import { describe, test, expect, vi, beforeEach } from 'vitest'
import { render, screen, fireEvent, waitFor } from '@testing-library/react'
import userEvent from '@testing-library/user-event'
import { useRouter } from 'next/navigation'
import LoginPage from '../page'
import { useAuth } from '@/contexts/user'

// Mock dependencies
vi.mock('next/navigation', () => ({
  useRouter: vi.fn(),
  useSearchParams: vi.fn(() => ({
    get: vi.fn(() => null),
  })),
}))

vi.mock('@/contexts/user', () => ({
  useAuth: vi.fn(),
}))

vi.mock('@/lib/show-error', () => ({
  showApiError: vi.fn(),
}))

const mockPush = vi.fn()
const mockLogin = vi.fn()

describe('LoginPage (Radix Migration)', () => {
  beforeEach(() => {
    vi.clearAllMocks()
    ;(useRouter as any).mockReturnValue({
      push: mockPush,
    })
    ;(useAuth as any).mockReturnValue({
      login: mockLogin,
    })
  })

  test('renders login form with all fields', () => {
    render(<LoginPage />)

    // 检查页面标题和描述
    expect(screen.getByRole('heading', { name: '登录' })).toBeInTheDocument()
    expect(screen.getByText('请输入您的用户名和密码')).toBeInTheDocument()

    // 检查表单字段
    expect(screen.getByLabelText('用户名')).toBeInTheDocument()
    expect(screen.getByLabelText('密码')).toBeInTheDocument()

    // 检查提交按钮
    expect(screen.getByRole('button', { name: '登录' })).toBeInTheDocument()
  })

  test('shows validation errors for empty fields', async () => {
    const user = userEvent.setup()
    render(<LoginPage />)
    
    // 点击提交按钮而不填写任何字段
    const submitButton = screen.getByRole('button', { name: '登录' })
    await user.click(submitButton)
    
    // 检查验证错误
    await waitFor(() => {
      expect(screen.getByText('用户名至少需要3个字符')).toBeInTheDocument()
      expect(screen.getByText('密码至少需要6个字符')).toBeInTheDocument()
    })
  })

  test('shows validation error for short username', async () => {
    const user = userEvent.setup()
    render(<LoginPage />)
    
    // 输入短用户名
    const usernameInput = screen.getByLabelText('用户名')
    await user.type(usernameInput, 'ab')
    
    // 点击提交
    const submitButton = screen.getByRole('button', { name: '登录' })
    await user.click(submitButton)
    
    await waitFor(() => {
      expect(screen.getByText('用户名至少需要3个字符')).toBeInTheDocument()
    })
  })

  test('shows validation error for short password', async () => {
    const user = userEvent.setup()
    render(<LoginPage />)
    
    // 输入短密码
    const passwordInput = screen.getByLabelText('密码')
    await user.type(passwordInput, '12345')
    
    // 点击提交
    const submitButton = screen.getByRole('button', { name: '登录' })
    await user.click(submitButton)
    
    await waitFor(() => {
      expect(screen.getByText('密码至少需要6个字符')).toBeInTheDocument()
    })
  })

  test('submits form with valid data', async () => {
    const user = userEvent.setup()
    mockLogin.mockResolvedValue({})
    
    render(<LoginPage />)
    
    // 填写有效数据
    const usernameInput = screen.getByLabelText('用户名')
    const passwordInput = screen.getByLabelText('密码')
    
    await user.type(usernameInput, 'testuser')
    await user.type(passwordInput, 'password123')
    
    // 提交表单
    const submitButton = screen.getByRole('button', { name: '登录' })
    await user.click(submitButton)
    
    // 验证登录函数被调用
    await waitFor(() => {
      expect(mockLogin).toHaveBeenCalledWith({
        username: 'testuser',
        password: 'password123',
      })
    })
    
    // 验证跳转到首页（默认行为）
    expect(mockPush).toHaveBeenCalledWith('/')
  })

  test('shows loading state during submission', async () => {
    const user = userEvent.setup()
    // 模拟延迟的登录请求
    mockLogin.mockImplementation(() => new Promise(resolve => setTimeout(resolve, 100)))
    
    render(<LoginPage />)
    
    // 填写有效数据
    const usernameInput = screen.getByLabelText('用户名')
    const passwordInput = screen.getByLabelText('密码')
    
    await user.type(usernameInput, 'testuser')
    await user.type(passwordInput, 'password123')
    
    // 提交表单
    const submitButton = screen.getByRole('button', { name: '登录' })
    await user.click(submitButton)
    
    // 检查加载状态
    expect(screen.getByRole('button', { name: '登录中...' })).toBeInTheDocument()
    expect(screen.getByRole('button', { name: '登录中...' })).toBeDisabled()
    
    // 检查输入框也被禁用
    expect(usernameInput).toBeDisabled()
    expect(passwordInput).toBeDisabled()
  })

  test('handles login error', async () => {
    const user = userEvent.setup()
    const error = new Error('Login failed')
    mockLogin.mockRejectedValue(error)
    
    const { showApiError } = await import('@/lib/show-error')
    
    render(<LoginPage />)
    
    // 填写有效数据
    const usernameInput = screen.getByLabelText('用户名')
    const passwordInput = screen.getByLabelText('密码')
    
    await user.type(usernameInput, 'testuser')
    await user.type(passwordInput, 'password123')
    
    // 提交表单
    const submitButton = screen.getByRole('button', { name: '登录' })
    await user.click(submitButton)
    
    // 验证错误处理
    await waitFor(() => {
      expect(showApiError).toHaveBeenCalledWith(error, '登录失败')
    })
  })

  test('form inputs have correct attributes', () => {
    render(<LoginPage />)
    
    const usernameInput = screen.getByLabelText('用户名')
    const passwordInput = screen.getByLabelText('密码')
    
    // 检查输入框类型
    expect(usernameInput).toHaveAttribute('type', 'text')
    expect(passwordInput).toHaveAttribute('type', 'password')
    
    // 检查占位符
    expect(usernameInput).toHaveAttribute('placeholder', 'Username')
    expect(passwordInput).toHaveAttribute('placeholder', 'Password')
    
    // 检查 ID
    expect(usernameInput).toHaveAttribute('id', 'username')
    expect(passwordInput).toHaveAttribute('id', 'password')
  })

  test('component renders without crashing', () => {
    const { container } = render(<LoginPage />)
    expect(container.firstChild).toBeInTheDocument()
  })

  test('card has correct styling classes', () => {
    const { container } = render(<LoginPage />)

    // 检查主容器
    const mainContainer = container.querySelector('.flex.min-h-screen')
    expect(mainContainer).toHaveClass('flex', 'min-h-screen', 'items-center', 'justify-center', 'bg-background')

    // 检查卡片
    const card = container.querySelector('.w-full.max-w-sm')
    expect(card).toHaveClass('w-full', 'max-w-sm')
  })

  test('form has correct structure', () => {
    const { container } = render(<LoginPage />)

    const form = container.querySelector('form')
    expect(form).toHaveClass('grid', 'gap-4')

    // 检查表单字段容器
    const fieldContainers = container.querySelectorAll('.grid.gap-2')
    expect(fieldContainers).toHaveLength(2) // 用户名、密码

    fieldContainers.forEach(container => {
      expect(container).toHaveClass('grid', 'gap-2')
    })
  })

  test('form validation works correctly', async () => {
    const user = userEvent.setup()
    render(<LoginPage />)
    
    const usernameInput = screen.getByLabelText('用户名')
    const passwordInput = screen.getByLabelText('密码')
    const submitButton = screen.getByRole('button', { name: '登录' })
    
    // 测试用户名验证
    await user.type(usernameInput, 'ab')
    await user.click(submitButton)
    
    await waitFor(() => {
      expect(screen.getByText('用户名至少需要3个字符')).toBeInTheDocument()
    })
    
    // 清除并输入有效用户名
    await user.clear(usernameInput)
    await user.type(usernameInput, 'validuser')
    
    // 测试密码验证
    await user.type(passwordInput, '12345')
    await user.click(submitButton)
    
    await waitFor(() => {
      expect(screen.getByText('密码至少需要6个字符')).toBeInTheDocument()
    })
  })

  test('form submission prevents default behavior', async () => {
    const user = userEvent.setup()
    mockLogin.mockResolvedValue({})

    const { container } = render(<LoginPage />)

    const form = container.querySelector('form')!
    const submitSpy = vi.fn()
    form.addEventListener('submit', submitSpy)

    // 填写有效数据
    const usernameInput = screen.getByLabelText('用户名')
    const passwordInput = screen.getByLabelText('密码')

    await user.type(usernameInput, 'testuser')
    await user.type(passwordInput, 'password123')

    // 提交表单
    const submitButton = screen.getByRole('button', { name: '登录' })
    await user.click(submitButton)

    // 验证表单提交事件被触发
    await waitFor(() => {
      expect(submitSpy).toHaveBeenCalled()
    })
  })

  test('accessibility attributes are correct', () => {
    render(<LoginPage />)
    
    // 检查标签与输入框的关联
    const usernameLabel = screen.getByText('用户名')
    const passwordLabel = screen.getByText('密码')
    const usernameInput = screen.getByLabelText('用户名')
    const passwordInput = screen.getByLabelText('密码')
    
    expect(usernameLabel).toHaveAttribute('for', 'username')
    expect(passwordLabel).toHaveAttribute('for', 'password')
    expect(usernameInput).toHaveAttribute('id', 'username')
    expect(passwordInput).toHaveAttribute('id', 'password')
  })
})
