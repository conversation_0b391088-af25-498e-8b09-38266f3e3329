"use client"

import { toast } from "sonner";

import { ApiError } from "./http";
import { errorCodeMessages } from "@/constants/error-codes";
import { ErrorHandlerService } from "@/services/errorHandler";

/**
 * 根据错误对象统一 toast 提示
 * @param err 任意错误
 * @param fallback 当错误对象无 message 时的兜底提示
 */
export function showApiError(err: unknown, fallback = "Network error") {
  try {
    // 使用新的错误处理服务
    const processedError = ErrorHandlerService.processApiError(err);
    toast.error(processedError.message);
  } catch (processingError) {
    // 如果新的错误处理失败，回退到原有逻辑
    let message = fallback;
    const hasCustomFallback = fallback !== "Network error";

    if (err instanceof ApiError) {
      // 针对常见鉴权错误码提供更友好的中文提示
      if (err.code === 401) {
        message = "请先登录";
      } else if (err.code === 403) {
        message = "权限不足";
      } else {
        // 优先使用错误码映射，如果没有映射且有自定义 fallback，则使用 fallback
        const mappedMessage = errorCodeMessages[err.code];
        if (mappedMessage) {
          message = mappedMessage;
        } else if (hasCustomFallback) {
          message = fallback;
        } else {
          message = err.message;
        }
      }
    } else if (
      typeof err === "object" &&
      err !== null &&
      "code" in err &&
      typeof (err as any).code === "number" &&
      (err as any).code in errorCodeMessages
    ) {
      // 处理 request 函数抛出的业务错误对象 { code, message }
      message = errorCodeMessages[(err as any).code as number];
    } else if (!hasCustomFallback) {
      // 仅在未提供自定义 fallback 时才使用错误对象的 message 或字符串错误
      if (typeof err === "string") {
        message = err;
      } else if (
        typeof err === "object" &&
        err !== null &&
        "message" in err &&
        typeof (err as any).message === "string"
      ) {
        message = (err as any).message;
      }
    }

    toast.error(message);
  }
}