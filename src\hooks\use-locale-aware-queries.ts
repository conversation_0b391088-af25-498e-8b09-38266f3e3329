/**
 * 语言感知的查询 Hooks
 * 
 * 提供正确的查询键结构，确保多语言缓存正常工作
 */

import { useQuery, useInfiniteQuery } from '@tanstack/react-query';
import { useLocale } from 'next-intl';
import type { Locale } from '@/lib/locale-utils';
import { localeApi } from '@/lib/api-client';

/**
 * 创建语言感知的查询键
 * 确保查询键包含语言信息，便于缓存管理
 */
export function createLocaleQueryKey(
  baseKey: string | string[], 
  locale: Locale, 
  params?: Record<string, unknown>
) {
  const base = Array.isArray(baseKey) ? baseKey : [baseKey];
  
  return [
    ...base,
    { locale, ...params }
  ];
}

/**
 * 语言感知的事件查询
 * 最佳实践：查询键包含语言 + 请求头也包含语言
 */
export function useEventsQuery(params?: {
  page?: number;
  limit?: number;
  category?: string;
}) {
  const locale = useLocale() as Locale;

  return useQuery({
    // ✅ 查询键包含语言信息，确保缓存隔离
    queryKey: createLocaleQueryKey('events', locale, params),
    queryFn: async () => {
      // ✅ 使用统一的 API 客户端，自动处理语言头部
      const response = await localeApi.getEvents(locale, params);
      return response.data;
    },
    staleTime: 5 * 60 * 1000, // 5分钟内认为数据是新鲜的
    gcTime: 30 * 60 * 1000,   // 30分钟后垃圾回收
  });
}

/**
 * 语言感知的社团查询
 */
export function useCirclesQuery(params?: {
  page?: number;
  limit?: number;
  tags?: string[];
}) {
  const locale = useLocale() as Locale;
  
  return useQuery({
    queryKey: createLocaleQueryKey('circles', locale, params),
    queryFn: async () => {
      const searchParams = new URLSearchParams({
        locale,
        ...Object.fromEntries(
          Object.entries(params || {}).map(([k, v]) => [k, String(v)])
        )
      });
      
      const response = await fetch(`/api/circles?${searchParams}`);
      if (!response.ok) throw new Error('Failed to fetch circles');
      return response.json();
    },
    staleTime: 5 * 60 * 1000,
    gcTime: 30 * 60 * 1000,
  });
}

/**
 * 语言感知的搜索查询
 */
export function useSearchQuery(query: string, type?: 'events' | 'circles' | 'all') {
  const locale = useLocale() as Locale;
  
  return useQuery({
    queryKey: createLocaleQueryKey('search', locale, { query, type }),
    queryFn: async () => {
      const searchParams = new URLSearchParams({
        q: query,
        locale,
        ...(type && { type })
      });
      
      const response = await fetch(`/api/search?${searchParams}`);
      if (!response.ok) throw new Error('Failed to search');
      return response.json();
    },
    enabled: query.length > 0, // 只有当有搜索词时才执行查询
    staleTime: 2 * 60 * 1000,  // 搜索结果2分钟内有效
    gcTime: 10 * 60 * 1000,    // 10分钟后垃圾回收
  });
}

/**
 * 语言感知的无限滚动查询
 */
export function useInfiniteEventsQuery(params?: {
  limit?: number;
  category?: string;
}) {
  const locale = useLocale() as Locale;
  
  return useInfiniteQuery({
    queryKey: createLocaleQueryKey('events', locale, { ...params, infinite: true }),
    queryFn: async ({ pageParam = 1 }) => {
      const searchParams = new URLSearchParams({
        locale,
        page: String(pageParam),
        limit: String(params?.limit || 20),
        ...(params?.category && { category: params.category })
      });
      
      const response = await fetch(`/api/events?${searchParams}`);
      if (!response.ok) throw new Error('Failed to fetch events');
      return response.json();
    },
    getNextPageParam: (lastPage, allPages) => {
      // 假设 API 返回 hasMore 字段
      return lastPage.hasMore ? allPages.length + 1 : undefined;
    },
    initialPageParam: 1,
    staleTime: 5 * 60 * 1000,
    gcTime: 30 * 60 * 1000,
  });
}

/**
 * 语言无关的查询（用户信息等）
 * 这些查询不会被语言切换影响
 */
export function useUserQuery() {
  return useQuery({
    queryKey: ['user'], // 注意：不包含 locale
    queryFn: async () => {
      const response = await fetch('/api/user');
      if (!response.ok) throw new Error('Failed to fetch user');
      return response.json();
    },
    staleTime: 10 * 60 * 1000, // 用户信息10分钟内有效
    gcTime: 60 * 60 * 1000,    // 1小时后垃圾回收
  });
}

/**
 * 系统配置查询（语言无关）
 */
export function useSystemConfigQuery() {
  return useQuery({
    queryKey: ['system', 'config'], // 语言无关
    queryFn: async () => {
      const response = await fetch('/api/system/config');
      if (!response.ok) throw new Error('Failed to fetch config');
      return response.json();
    },
    staleTime: 30 * 60 * 1000, // 系统配置30分钟内有效
    gcTime: 2 * 60 * 60 * 1000, // 2小时后垃圾回收
  });
}

/**
 * 预取下一页数据的工具函数
 */
export function usePrefetchNextPage() {
  const locale = useLocale() as Locale;
  
  return {
    prefetchEvents: (page: number, params?: Record<string, unknown>) => {
      // 这里可以实现预取逻辑
      console.log(`预取事件数据: 页面 ${page}, 语言 ${locale}`, params);
    },
    prefetchCircles: (page: number, params?: Record<string, unknown>) => {
      console.log(`预取社团数据: 页面 ${page}, 语言 ${locale}`, params);
    }
  };
}

/**
 * 查询键工厂函数
 * 提供一致的查询键创建方式
 */
export const queryKeys = {
  // 语言相关的查询键
  events: (locale: Locale, params?: Record<string, unknown>) => 
    createLocaleQueryKey('events', locale, params),
  
  circles: (locale: Locale, params?: Record<string, unknown>) => 
    createLocaleQueryKey('circles', locale, params),
  
  search: (locale: Locale, query: string, type?: string) => 
    createLocaleQueryKey('search', locale, { query, type }),
  
  feed: (locale: Locale, params?: Record<string, unknown>) => 
    createLocaleQueryKey('feed', locale, params),
  
  // 语言无关的查询键
  user: () => ['user'],
  
  systemConfig: () => ['system', 'config'],
  
  notifications: (userId: string) => ['notifications', userId],
} as const;

/**
 * 开发环境下的查询键调试工具
 */
export function debugQueryKeys() {
  if (process.env.NODE_ENV !== 'development') return;
  
  console.group('🔑 查询键示例');
  console.log('事件查询:', queryKeys.events('zh', { page: 1 }));
  console.log('社团查询:', queryKeys.circles('ja', { tags: ['anime'] }));
  console.log('搜索查询:', queryKeys.search('en', 'comiket', 'events'));
  console.log('用户查询:', queryKeys.user());
  console.groupEnd();
}
