"use client";

import Link from "next/link";
import { useRouter } from "next/navigation";
import React, { useState } from "react";
import { useLocale } from "next-intl";

import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { useAdminEvents } from "@/hooks/admin/useAdminEvents";
import { useDeleteEvent } from "@/hooks/admin/useDeleteEvent";

export default function AdminEventsPage() {
  const router = useRouter();
  const locale = useLocale();

  // 搜索和分页状态
  const [keyword, setKeyword] = useState("");
  const [currentPage, setCurrentPage] = useState(1);
  const pageSize = 20;

  const {
    data: events = [],
    isLoading,
    pagination
  } = useAdminEvents({
    keyword: keyword.trim() || undefined,
    page: String(currentPage),
    pageSize: String(pageSize)
  });

  const deleteEvent = useDeleteEvent();

  function handleDelete(id: string) {
    if (!confirm("确定删除该展会吗？")) return;
    deleteEvent.mutate(id, {
      onSuccess: () => {
        // 如果删除后当前页没有数据且不是第一页，回到上一页
        if (events.length === 1 && currentPage > 1) {
          setCurrentPage(currentPage - 1);
        }
      }
    });
  }

  function handleSearch(e: React.FormEvent) {
    e.preventDefault();
    setCurrentPage(1); // 搜索时重置到第一页
  }

  return (
    <div className="space-y-6">
      {/* 页面标题和操作 */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-2xl font-bold">展会管理</h1>
          <p className="text-muted-foreground text-sm mt-1">
            当前语言: {locale === 'zh' ? '中文' : locale === 'ja' ? '日本語' : 'English'}
          </p>
        </div>
        <Button onClick={() => { router.push("/admin/events/new"); }}>
          新增展会
        </Button>
      </div>

      {/* 搜索栏 */}
      <form onSubmit={handleSearch} className="flex gap-2">
        <Input
          type="text"
          placeholder="搜索展会名称..."
          value={keyword}
          onChange={(e) => setKeyword(e.target.value)}
          className="max-w-md"
        />
        <Button type="submit" variant="outline">
          搜索
        </Button>
        {keyword && (
          <Button
            type="button"
            variant="ghost"
            onClick={() => {
              setKeyword("");
              setCurrentPage(1);
            }}
          >
            清除
          </Button>
        )}
      </form>

      {/* 数据统计 */}
      {pagination && (
        <div className="text-sm text-muted-foreground">
          共 {pagination.total} 条记录，第 {pagination.page} 页，每页 {pagination.pageSize} 条
        </div>
      )}

      {/* 数据表格 */}
      {isLoading ? (
        <div className="flex justify-center py-8">
          <p>加载中...</p>
        </div>
      ) : events.length === 0 ? (
        <div className="text-center py-8 text-muted-foreground">
          {keyword ? "未找到匹配的展会" : "暂无展会数据"}
        </div>
      ) : (
        <div className="border rounded-lg overflow-hidden">
          <table className="w-full text-sm">
            <thead>
              <tr className="bg-muted">
                <th className="border-b px-4 py-3 text-left font-medium">ID</th>
                <th className="border-b px-4 py-3 text-left font-medium">名称</th>
                <th className="border-b px-4 py-3 text-left font-medium">日期</th>
                <th className="border-b px-4 py-3 text-left font-medium">会场</th>
                <th className="border-b px-4 py-3 text-left font-medium">地址</th>
                <th className="border-b px-4 py-3 text-left font-medium">官网</th>
                <th className="border-b px-4 py-3 text-center font-medium w-32">操作</th>
              </tr>
            </thead>
            <tbody>
              {events.map((evt, index) => (
                <tr key={evt.id} className={index % 2 === 0 ? "bg-background" : "bg-muted/20"}>
                  <td className="border-b px-4 py-3">
                    <code className="text-xs bg-muted px-1 py-0.5 rounded">
                      {evt.id}
                    </code>
                  </td>
                  <td className="border-b px-4 py-3 font-medium">
                    {evt.name}
                  </td>
                  <td className="border-b px-4 py-3">
                    {evt.date}
                  </td>
                  <td className="border-b px-4 py-3">
                    {evt.venue_name}
                  </td>
                  <td className="border-b px-4 py-3 text-muted-foreground">
                    {evt.venue_address || '-'}
                  </td>
                  <td className="border-b px-4 py-3">
                    {evt.url ? (
                      <a
                        href={evt.url}
                        target="_blank"
                        rel="noopener noreferrer"
                        className="text-primary hover:underline text-xs"
                      >
                        查看
                      </a>
                    ) : (
                      <span className="text-muted-foreground">-</span>
                    )}
                  </td>
                  <td className="border-b px-4 py-3">
                    <div className="flex justify-center gap-2">
                      <Link
                        className="text-primary hover:underline text-xs"
                        href={`/admin/events/${evt.id}/edit`}
                      >
                        编辑
                      </Link>
                      <button
                        className="text-destructive hover:underline text-xs"
                        onClick={() => handleDelete(evt.id)}
                        disabled={deleteEvent.isPending}
                      >
                        删除
                      </button>
                    </div>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      )}

      {/* 分页控件 */}
      {pagination && pagination.total > pageSize && (
        <div className="flex justify-center gap-2">
          <Button
            variant="outline"
            size="sm"
            disabled={currentPage <= 1}
            onClick={() => setCurrentPage(currentPage - 1)}
          >
            上一页
          </Button>
          <span className="flex items-center px-3 text-sm">
            第 {currentPage} 页 / 共 {Math.ceil(pagination.total / pageSize)} 页
          </span>
          <Button
            variant="outline"
            size="sm"
            disabled={currentPage >= Math.ceil(pagination.total / pageSize)}
            onClick={() => setCurrentPage(currentPage + 1)}
          >
            下一页
          </Button>
        </div>
      )}
    </div>
  );
} 