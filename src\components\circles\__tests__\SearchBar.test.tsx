import { render, screen, fireEvent } from "@testing-library/react";
import { describe, test, expect, vi } from "vitest";
import SearchBar from "../SearchBar";

describe("SearchBar", () => {
  const mockOnSearchChange = vi.fn();
  const mockOnReset = vi.fn();

  beforeEach(() => {
    vi.clearAllMocks();
  });

  test("renders search input with placeholder", () => {
    render(
      <SearchBar
        searchKeyword=""
        onSearchChange={mockOnSearchChange}
        onReset={mockOnReset}
      />
    );

    expect(screen.getByPlaceholderText("搜索社团名称或分类...")).toBeInTheDocument();
  });

  test("displays search keyword value", () => {
    render(
      <SearchBar
        searchKeyword="test keyword"
        onSearchChange={mockOnSearchChange}
        onReset={mockOnReset}
      />
    );

    expect(screen.getByDisplayValue("test keyword")).toBeInTheDocument();
  });

  test("calls onSearchChange when input changes", () => {
    render(
      <SearchBar
        searchKeyword=""
        onSearchChange={mockOnSearchChange}
        onReset={mockOnReset}
      />
    );

    const input = screen.getByPlaceholderText("搜索社团名称或分类...");
    fireEvent.change(input, { target: { value: "new search" } });

    expect(mockOnSearchChange).toHaveBeenCalledWith("new search");
  });

  test("shows clear button when searchKeyword is not empty", () => {
    render(
      <SearchBar
        searchKeyword="test"
        onSearchChange={mockOnSearchChange}
        onReset={mockOnReset}
      />
    );

    expect(screen.getByLabelText("清除搜索")).toBeInTheDocument();
  });

  test("hides clear button when searchKeyword is empty", () => {
    render(
      <SearchBar
        searchKeyword=""
        onSearchChange={mockOnSearchChange}
        onReset={mockOnReset}
      />
    );

    expect(screen.queryByLabelText("清除搜索")).not.toBeInTheDocument();
  });

  test("calls onReset when clear button is clicked", () => {
    render(
      <SearchBar
        searchKeyword="test"
        onSearchChange={mockOnSearchChange}
        onReset={mockOnReset}
      />
    );

    const clearButton = screen.getByLabelText("清除搜索");
    fireEvent.click(clearButton);

    expect(mockOnReset).toHaveBeenCalled();
  });

  test("disables input when isLoading is true", () => {
    render(
      <SearchBar
        searchKeyword=""
        onSearchChange={mockOnSearchChange}
        onReset={mockOnReset}
        isLoading={true}
      />
    );

    expect(screen.getByPlaceholderText("搜索社团名称或分类...")).toBeDisabled();
  });
});
