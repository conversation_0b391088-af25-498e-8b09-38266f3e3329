import { randomBytes as nodeRandomBytes } from 'node:crypto';

import type { D1Database } from '@cloudflare/workers-types';
import bcrypt from 'bcryptjs';

import { createAuthRepository } from './repository';

/**
 * Session cookie 名称常量
 */
export const SESSION_COOKIE_NAME = 'auth_session';

/**
 * 生成指定长度的随机十六进制字符串
 */
function generateId(length: number): string {
  // Runtime environment may expose either Web Crypto (`crypto.getRandomValues`) or Node.js crypto.
  if (typeof (globalThis as any).crypto?.getRandomValues === 'function') {
    const bytes = new Uint8Array(length);
    (globalThis as any).crypto.getRandomValues(bytes);
    return Array.from(bytes)
      .map((b) => b.toString(16).padStart(2, '0'))
      .join('');
  }

  // Fallback for Node.js test environment
  const bytes = nodeRandomBytes(length);
  return Array.from(bytes)
    .map((b) => b.toString(16).padStart(2, '0'))
    .join('');
}

/**
 * 用户聚合根结构
 */
export interface User {
  id: string;
  username: string;
  role: string;
}

// ---------------- 密码散列 ----------------
/**
 * 使用 bcrypt 对密码进行散列
 */
export async function hashPassword(password: string): Promise<string> {
  // 12 轮盐值是性能与安全的平衡
  return bcrypt.hash(password, 12);
}

/**
 * 校验用户输入的密码是否与散列值匹配
 */
export async function verifyPassword(
  hashedPassword: string,
  plainPassword: string
): Promise<boolean> {
  return bcrypt.compare(plainPassword, hashedPassword);
}

// ---------------- 用户与会话 ----------------
/**
 * 创建新用户
 */
export async function createUser(
  db: D1Database,
  username: string,
  password: string
): Promise<User> {
  const hashedPassword = await hashPassword(password);
  const userId = generateId(8);
  const repo = createAuthRepository(db);
  try {
    await repo.createUser(userId, username, 'viewer', hashedPassword);
  } catch (e: unknown) {
    // 唯一键冲突：用户名已存在
    if (e instanceof Error && e.message.includes('UNIQUE')) {
      throw new Error('Username already taken.');
    }
    throw e;
  }
  return { id: userId, username, role: 'viewer' };
}

/**
 * 为指定用户生成会话
 */
export async function createSession(
  db: D1Database,
  userId: string
): Promise<string> {
  const repo = createAuthRepository(db);
  const sessionId = generateId(20);
  const sessionExpiresIn = 1000 * 60 * 60 * 24 * 30; // 30 天
  const expiresAt = new Date(Date.now() + sessionExpiresIn);
  await repo.createSession(sessionId, userId, expiresAt);
  return sessionId;
}

/**
 * 校验会话并返回对应用户；过期会话会被同时清理
 */
export async function validateSession(
  db: D1Database,
  sessionId: string
): Promise<User | null> {
  const repo = createAuthRepository(db);
  await repo.cleanupExpiredSessions();
  const user = await repo.validateSession(sessionId);
  if (!user) return null;
  return { id: user.id, username: user.username, role: user.role };
}

/**
 * 使会话失效（退出登录）
 */
export async function invalidateSession(
  db: D1Database,
  sessionId: string
): Promise<void> {
  const repo = createAuthRepository(db);
  await repo.invalidateSession(sessionId);
}

/**
 * 校验用户名/密码并返回用户
 */
export async function verifyCredentials(
  db: D1Database,
  username: string,
  password: string
): Promise<User> {
  const repo = createAuthRepository(db);
  const record = await repo.findUserWithPasswordByUsername(username);
  if (!record) {
    throw new Error('USER_NOT_FOUND');
  }
  if (!(record as any).hashed_password) {
    throw new Error('PASSWORD_MISMATCH');
  }
  const ok = await verifyPassword((record as any).hashed_password, password);
  if (!ok) {
    throw new Error('PASSWORD_MISMATCH');
  }
  return { id: record.id, username: record.username, role: record.role };
}
