"use client";

import React, { memo } from "react";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { Search, X, Loader2 } from "lucide-react";

interface SearchBarProps {
  searchKeyword: string;
  onSearchChange: (value: string) => void;
  onReset: () => void;
  isLoading?: boolean;
  isSearching?: boolean;
}

const SearchBar = memo(function SearchBar({
  searchKeyword,
  onSearchChange,
  onReset,
  isLoading,
  isSearching,
}: SearchBarProps) {
  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    // 搜索逻辑已经通过防抖处理
  };

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    onSearchChange(e.target.value);
  };

  return (
    <section className="mb-6 sm:mb-8" aria-label="搜索">
      <form onSubmit={handleSubmit} className="flex gap-2">
        <div className="flex-1 relative">
          {isSearching ? (
            <Loader2 className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground h-4 w-4 animate-spin" />
          ) : (
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground h-4 w-4" />
          )}
          <Input
            placeholder="搜索社团名称或分类..."
            value={searchKeyword}
            onChange={handleInputChange}
            className="pl-10"
            disabled={isLoading}
          />
          {searchKeyword && !isSearching && (
            <Button
              type="button"
              variant="ghost"
              size="sm"
              className="absolute right-1 top-1/2 transform -translate-y-1/2 h-8 w-8 p-0"
              onClick={onReset}
              aria-label="清除搜索"
            >
              <X className="h-4 w-4" />
            </Button>
          )}
        </div>
      </form>
    </section>
  );
}, (prevProps, nextProps) => {
  // 自定义比较函数：只有关键属性变化时才重新渲染
  return (
    prevProps.searchKeyword === nextProps.searchKeyword &&
    prevProps.isSearching === nextProps.isSearching &&
    prevProps.onSearchChange === nextProps.onSearchChange &&
    prevProps.onReset === nextProps.onReset
    // 故意忽略 isLoading，因为搜索框不需要因为加载状态而重新渲染
  );
});

export default SearchBar;
