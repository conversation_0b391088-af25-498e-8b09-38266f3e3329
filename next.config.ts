import type { NextConfig } from "next";
import bundleAnalyzer from '@next/bundle-analyzer'
import createNextIntlPlugin from 'next-intl/plugin'

// 插件实例
const withBundleAnalyzer = bundleAnalyzer({
  enabled: process.env.ANALYZE === 'true',
})
const withNextIntl = createNextIntlPlugin()

const nextConfig: NextConfig = {
  /* config options here */
  images: {
    domains: ["localhost"],
  },
}

// 通过数组 reduce 链式应用插件，避免深层嵌套
const plugins = [withBundleAnalyzer, withNextIntl]

export default plugins.reduce((config, plugin) => plugin(config), nextConfig)
