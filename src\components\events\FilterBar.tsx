import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { cn } from "@/lib/utils"

interface FilterBarProps {
  keyword: string
  setKeyword: (keyword: string) => void
  categories: string[]
  setCategories: (categories: string[]) => void
  toggleCategory: (category: string) => void
  categoryOptions: { id: string; label: string }[]
}

export default function FilterBar({
  keyword,
  setKeyword,
  categories,
  setCategories,
  toggleCategory,
  categoryOptions,
}: FilterBarProps) {
  return (
    <section className="p-4 flex flex-col gap-3">
      <div className="flex flex-wrap gap-2 items-center">
        <Input
          placeholder="搜索摊位号 / 名称 / 作者"
          value={keyword}
          onChange={(e: React.ChangeEvent<HTMLInputElement>) =>
            { setKeyword(e.target.value) }
          }
          className="max-w-xs"
        />
        <Button
          variant="outline"
          size="sm"
          onClick={() => {
            setKeyword("")
            setCategories([])
          }}
        >
          重置
        </Button>
      </div>

      {/* 分类按钮组 */}
      <div className="flex flex-wrap gap-2">
        {categoryOptions.map((c) => (
          <Button
            key={c.id}
            variant={categories.includes(c.id) ? "default" : "outline"}
            size="sm"
            onClick={() => { toggleCategory(c.id); }}
            className={cn("uppercase")}
          >
            {c.label}
          </Button>
        ))}
      </div>
    </section>
  )
}
