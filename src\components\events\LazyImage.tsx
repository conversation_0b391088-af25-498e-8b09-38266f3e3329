'use client';

import React, { useState, useRef, useEffect, useCallback } from 'react';
import Image from 'next/image';
import { cn } from '@/lib/utils';
import { Skeleton } from '@/components/ui/skeleton';
import { ImageIcon } from 'lucide-react';

export interface LazyImageProps {
  /** 图片 URL */
  src: string;
  /** 图片 alt 文本 */
  alt: string;
  /** 图片宽度 */
  width: number;
  /** 图片高度 */
  height: number;
  /** 自定义类名 */
  className?: string;
  /** 占位符类名 */
  placeholderClassName?: string;
  /** 是否立即加载 */
  priority?: boolean;
  /** 懒加载根边距 */
  rootMargin?: string;
  /** 懒加载阈值 */
  threshold?: number;
  /** 加载完成回调 */
  onLoad?: () => void;
  /** 加载错误回调 */
  onError?: () => void;
  /** 点击回调 */
  onClick?: () => void;
}

/**
 * 懒加载图片组件
 * 使用 Intersection Observer 实现懒加载
 */
export function LazyImage({
  src,
  alt,
  width,
  height,
  className,
  placeholderClassName,
  priority = false,
  rootMargin = '50px',
  threshold = 0.1,
  onLoad,
  onError,
  onClick
}: LazyImageProps) {
  const [isInView, setIsInView] = useState(priority);
  const [isLoaded, setIsLoaded] = useState(false);
  const [hasError, setHasError] = useState(false);
  const imgRef = useRef<HTMLDivElement>(null);

  // Intersection Observer 设置
  useEffect(() => {
    if (priority || isInView) return;

    const observer = new IntersectionObserver(
      (entries) => {
        entries.forEach((entry) => {
          if (entry.isIntersecting) {
            setIsInView(true);
            observer.disconnect();
          }
        });
      },
      {
        rootMargin,
        threshold
      }
    );

    if (imgRef.current) {
      observer.observe(imgRef.current);
    }

    return () => observer.disconnect();
  }, [priority, isInView, rootMargin, threshold]);

  // 处理图片加载完成
  const handleLoad = useCallback(() => {
    setIsLoaded(true);
    onLoad?.();
  }, [onLoad]);

  // 处理图片加载错误
  const handleError = useCallback(() => {
    setHasError(true);
    onError?.();
  }, [onError]);

  return (
    <div
      ref={imgRef}
      className={cn('relative overflow-hidden', className)}
      style={{ width, height }}
      onClick={onClick}
    >
      {/* 占位符 */}
      {!isLoaded && !hasError && (
        <div className={cn(
          'absolute inset-0 flex items-center justify-center bg-muted',
          placeholderClassName
        )}>
          {isInView ? (
            <Skeleton className="w-full h-full" />
          ) : (
            <ImageIcon className="h-8 w-8 text-muted-foreground" />
          )}
        </div>
      )}

      {/* 错误状态 */}
      {hasError && (
        <div className={cn(
          'absolute inset-0 flex items-center justify-center bg-muted border border-dashed border-muted-foreground/25',
          placeholderClassName
        )}>
          <div className="text-center">
            <ImageIcon className="h-8 w-8 text-muted-foreground mx-auto mb-2" />
            <p className="text-xs text-muted-foreground">加载失败</p>
          </div>
        </div>
      )}

      {/* 实际图片 */}
      {isInView && !hasError && (
        <Image
          src={src}
          alt={alt}
          width={width}
          height={height}
          className={cn(
            'object-cover transition-opacity duration-300',
            isLoaded ? 'opacity-100' : 'opacity-0',
            onClick && 'cursor-pointer hover:opacity-90'
          )}
          onLoad={handleLoad}
          onError={handleError}
          priority={priority}
        />
      )}
    </div>
  );
}

/**
 * 懒加载图片网格组件
 */
export interface LazyImageGridProps {
  /** 图片列表 */
  images: Array<{
    src: string;
    alt: string;
    width?: number;
    height?: number;
  }>;
  /** 网格列数 */
  columns?: number;
  /** 图片宽度 */
  imageWidth?: number;
  /** 图片高度 */
  imageHeight?: number;
  /** 自定义类名 */
  className?: string;
  /** 图片类名 */
  imageClassName?: string;
  /** 懒加载根边距 */
  rootMargin?: string;
  /** 懒加载阈值 */
  threshold?: number;
  /** 图片点击回调 */
  onImageClick?: (image: any, index: number) => void;
}

export function LazyImageGrid({
  images,
  columns = 3,
  imageWidth = 300,
  imageHeight = 200,
  className,
  imageClassName,
  rootMargin = '50px',
  threshold = 0.1,
  onImageClick
}: LazyImageGridProps) {
  if (!images || images.length === 0) {
    return (
      <div className={cn('text-center py-8', className)}>
        <ImageIcon className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
        <p className="text-muted-foreground">暂无图片</p>
      </div>
    );
  }

  return (
    <div className={cn(`grid grid-cols-${columns} gap-4`, className)}>
      {images.map((image, index) => (
        <LazyImage
          key={index}
          src={image.src}
          alt={image.alt}
          width={image.width || imageWidth}
          height={image.height || imageHeight}
          className={cn('rounded-lg', imageClassName)}
          rootMargin={rootMargin}
          threshold={threshold}
          onClick={() => onImageClick?.(image, index)}
        />
      ))}
    </div>
  );
}

/**
 * 响应式懒加载图片组件
 * 根据容器大小自动调整图片尺寸
 */
export interface ResponsiveLazyImageProps extends Omit<LazyImageProps, 'width' | 'height'> {
  /** 宽高比 */
  aspectRatio?: number;
  /** 最大宽度 */
  maxWidth?: number;
  /** 最大高度 */
  maxHeight?: number;
}

export function ResponsiveLazyImage({
  aspectRatio = 4/3,
  maxWidth = 800,
  maxHeight = 600,
  className,
  ...props
}: ResponsiveLazyImageProps) {
  const [dimensions, setDimensions] = useState({ width: maxWidth, height: maxHeight });
  const containerRef = useRef<HTMLDivElement>(null);

  // 计算响应式尺寸
  useEffect(() => {
    const updateDimensions = () => {
      if (!containerRef.current) return;

      const containerWidth = containerRef.current.offsetWidth;
      const calculatedHeight = containerWidth / aspectRatio;

      const width = Math.min(containerWidth, maxWidth);
      const height = Math.min(calculatedHeight, maxHeight);

      setDimensions({ width, height });
    };

    updateDimensions();
    window.addEventListener('resize', updateDimensions);
    return () => window.removeEventListener('resize', updateDimensions);
  }, [aspectRatio, maxWidth, maxHeight]);

  return (
    <div ref={containerRef} className={cn('w-full', className)}>
      <LazyImage
        {...props}
        width={dimensions.width}
        height={dimensions.height}
      />
    </div>
  );
}
