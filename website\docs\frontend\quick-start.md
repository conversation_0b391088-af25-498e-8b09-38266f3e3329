---
id: quick-start
title: 5分钟快速开始
sidebar_label: 快速开始
sidebar_position: 1
description: 5分钟内完成Ayafeed前端开发环境搭建和API对接
keywords: [quick-start, 快速开始, API对接, 前端开发]
---

# 5分钟快速开始

> 🎯 **目标**: 5分钟内完成API对接，发出第一个请求

## 🚀 第一步：环境准备

### 安装依赖
```bash
# 核心依赖
npm install openapi-typescript-fetch

# 开发依赖（可选）
npm install -D openapi-typescript
```

### 获取OpenAPI规范
```bash
# 方式1：直接下载
curl -o openapi.json https://raw.githubusercontent.com/ayafeed/ayafeed-api/main/docs-site/static/openapi.json

# 方式2：从本地项目复制
cp path/to/ayafeed-api/docs-site/static/openapi.json ./
```

## ⚡ 第二步：生成API客户端

### 生成类型定义
```bash
npx openapi-typescript openapi.json -o src/types/api.ts
```

### 创建API客户端
```typescript
// src/lib/api.ts
import { Fetcher } from 'openapi-typescript-fetch';
import type { paths } from '@/types/api';

// 创建API客户端实例
export const api = Fetcher.for<paths>();

// 配置基础设置
api.configure({
  baseUrl: 'http://localhost:8787', // 开发环境
  init: {
    headers: {
      'Content-Type': 'application/json',
      'X-Locale': 'zh', // 默认中文
    },
  },
});

// 导出常用方法
export const GET = api.GET;
export const POST = api.POST;
export const PUT = api.PUT;
export const DELETE = api.DELETE;
```

## 🎯 第三步：发出第一个请求

### 获取事件列表
```typescript
// src/examples/events.ts
import { GET } from '@/lib/api';

async function getEvents() {
  try {
    const { data } = await GET('/events', {
      params: {
        query: {
          page: '1',
          pageSize: '10'
        }
      }
    });
    
    console.log('事件列表:', data.items);
    return data;
  } catch (error) {
    console.error('获取事件失败:', error);
  }
}

// 调用示例
getEvents();
```

### 搜索功能
```typescript
// src/examples/search.ts
import { GET } from '@/lib/api';

async function searchContent(keyword: string) {
  try {
    const { data } = await GET('/search', {
      params: {
        query: {
          q: keyword,
          type: 'all'
        }
      },
      headers: {
        'X-Locale': 'zh'
      }
    });
    
    console.log('搜索结果:', data);
    return data;
  } catch (error) {
    console.error('搜索失败:', error);
  }
}

// 调用示例
searchContent('Comiket');
```

## 🔐 第四步：添加认证（可选）

### 用户登录
```typescript
// src/examples/auth.ts
import { POST } from '@/lib/api';

async function login(username: string, password: string) {
  try {
    const { data } = await POST('/auth/login', {
      body: {
        username,
        password
      }
    });
    
    // 保存token
    localStorage.setItem('token', data.token);
    
    // 更新API客户端配置
    api.configure({
      init: {
        headers: {
          'Authorization': `Bearer ${data.token}`,
          'Content-Type': 'application/json',
        }
      }
    });
    
    return data;
  } catch (error) {
    console.error('登录失败:', error);
  }
}
```

## 🌐 第五步：多语言支持

### 动态语言切换
```typescript
// src/utils/locale.ts
import { api } from '@/lib/api';

export function setLocale(locale: 'zh' | 'ja' | 'en') {
  api.configure({
    init: {
      headers: {
        'X-Locale': locale,
        'Content-Type': 'application/json',
      }
    }
  });
}

// 使用示例
setLocale('ja'); // 切换到日文
const { data } = await GET('/events'); // 返回日文内容
```

## ✅ 验证集成

### 完整示例
```typescript
// src/examples/complete.ts
import { GET, POST, setLocale } from '@/lib/api';

async function completeExample() {
  // 1. 设置语言
  setLocale('zh');
  
  // 2. 获取事件列表
  const events = await GET('/events');
  console.log('事件数量:', events.data.total);
  
  // 3. 搜索内容
  const searchResults = await GET('/search', {
    params: { query: { q: '同人', type: 'circles' } }
  });
  console.log('搜索结果:', searchResults.data.length);
  
  // 4. 获取内容流
  const feed = await GET('/feed', {
    params: { query: { page: '1', limit: '5' } }
  });
  console.log('内容流:', feed.data.length);
  
  console.log('🎉 API集成成功！');
}

completeExample();
```

## 🎉 恭喜完成！

现在您已经成功集成了Ayafeed API！

### 下一步建议
1. 📖 阅读 [认证集成指南](./authentication.md) 了解完整的用户认证流程
2. 🌐 查看 [多语言集成指南](./i18n-integration.md) 实现完整的国际化
3. 🛠️ 参考 [常用示例](./common-examples.md) 获取更多代码示例
4. ⚠️ 学习 [错误处理](./error-handling.md) 构建健壮的应用

### 遇到问题？
- 查看 [常见问题](./faq.md)
- 提交 [GitHub Issue](https://github.com/ayafeed/ayafeed-api/issues)
- 查阅 [完整API文档](../api/)

---

**预计用时**: ⏱️ 5分钟  
**难度等级**: 🟢 简单
