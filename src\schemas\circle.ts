import { z } from "zod";

/**
 * 社团相关 TypeScript 类型定义（已移除 Zod 运行时校验）。
 * ----------------------------------------------
 * 若字段与 OpenAPI schema 发生变更，请在此同步更新。
 */

/**
 * 社团公共字段，任何场景可复用。
 */
export interface CircleBase {
  /** 社团唯一 ID */
  circle_id: string
  /** 社团显示名称 */
  circle_name: string
  /** 社团相关链接（后端以 JSON 字符串返回） */
  circle_urls: string
}

/**
 * 某次展会中的出摊信息（含摊位号、分类等）。
 * 对应接口：GET /events/{id}/appearances
 */
export interface CircleAppearance extends CircleBase {
  /** 摊位号 */
  booth_id: string
  /** 分类（music/comic 等） */
  category: string
  /** 作者名，可为空 */
  artist_name: string | null
  /** 作者 ID */
  artist_id: string
}

/**
 * /circles 全量列表项，只包含基础信息。
 */
export type CircleSummary = CircleBase

// 兼容旧别名：旧代码仍可能 import { CircleSchema }
export type Circle = CircleAppearance

// Circle 表单输入 Schema（新增 & 编辑复用）
export const CircleInputSchema = z.object({
  name: z.string().min(1, "名称必填"),
  author: z.string().optional(),
  category: z.string().min(1, "请选择分类"),
  // 以下字段仅编辑页使用，可选
  twitter: z.string().optional(),
  pixiv: z.string().optional(),
  web: z.string().optional(),
});

export type CircleInput = z.infer<typeof CircleInputSchema>;