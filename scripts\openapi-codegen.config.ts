import { defineConfig } from "@openapi-codegen/cli";
import {
  generateSchemaTypes,
  generateReactQueryComponents,
} from "@openapi-codegen/typescript";
export default defineConfig({
  ayafeed: {
    from: {
      relativePath: "./openapi.json",
      source: "file",
    },
    outputDir: "./src/api/generated",
    to: async (context) => {
      const filenamePrefix = "ayafeed";
      const { schemasFiles } = await generateSchemaTypes(context, {
        filenamePrefix,
      });
      await generateReactQueryComponents(context, {
        filenamePrefix,
        schemasFiles,
      });
    },
  },
});
