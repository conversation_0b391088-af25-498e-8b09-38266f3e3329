# 🧪 Ayafeed 前端测试指南

> 更新时间：2025-06-01

项目使用 **Vitest** 搭配 **@testing-library/react** 进行单元 / 集成测试，本文档介绍测试文件的组织规范、常用工具与最佳实践。

---

## 1. 目录与命名约定

| 类型 | 放置位置 | 说明 |
|------|----------|------|
| 组件 / 页面测试 | 与被测文件同级，`__tests__/xxx.test.tsx` | 就近原则，方便查阅与维护 |
| 通用工具函数测试 | `src/lib/__tests__/` | 纯函数或工具类 |
| Hooks 测试 | 与 Hook 同级 `__tests__/useXxx.test.ts` | 建议使用 `@testing-library/react-hooks`（内置于 Testing Library v14+） |

示例结构：

```text
src/app/admin/circles/
  page.tsx              # 列表页组件
  __tests__/
    page.test.tsx       # 列表页测试
  new/
    page.tsx
    __tests__/
      page.test.tsx     # 新建表单测试
```

> 💡 **为什么选择就近？** 在重构或移动组件时，相关测试可随文件一起移动，避免“孤儿测试”。

---

## 2. 运行测试

```bash
# 运行全部测试（watch 模式）
pnpm test

# 生成覆盖率报告
pnpm test -- --coverage
```

Vitest 配置位于 `vitest.config.ts`，已默认启用 `jsdom` 环境与全局 `expect` / `test` 。

---

## 3. 渲染辅助函数

`src/__test__/test-utils.tsx` 提供 `renderWithProviders`：

```tsx
import { renderWithProviders, screen } from "@test/test-utils";
import MyComponent from "@/components/my-component";

renderWithProviders(<MyComponent />);
```

该函数自动注入：
1. **React Query** 独立 `QueryClient`（避免测试间缓存污染）
2. **AuthProvider**（可通过 `vi.mock("@/contexts/user")` 控制登录态）

---

## 4. 常用 Mock 技巧

| 场景 | 写法示例 |
|------|---------|
| 路由跳转 | `vi.mock("next/navigation", () => ({ useRouter: () => ({ push: vi.fn() }) }))` |
| HTTP 请求 | `vi.mock("@/lib/http", () => ({ request: vi.fn() }))` |
| 图像 / 链接组件 | 参考 `CircleCard.test.tsx` 中对 `next/image`、`next/link` 的模拟 |

---

## 5. 覆盖率阈值

`vitest.config.ts` 中设置了基础阈值（行、函数、分支、语句均为 **80%**）。

> 当新增模块时，请确保覆盖率不下降；必要时补充关键路径测试。

---

## 6. 常见问题

1. **找不到路径别名 `@/...`**
   - 确保运行测试时 `tsconfig.json` 中的 `paths` 已匹配。
2. **React Query 警告 `Unhandled error`**
   - 使用 `renderWithProviders` 或在测试文件手动创建 `QueryClientProvider` 并禁用自动重试。
3. **`next/navigation` hooks 报错**
   - 在测试顶层通过 `vi.mock("next/navigation", …)` 提前 mock。

---

## 7. 进阶：E2E 测试（规划）

未来将接入 **Playwright** 进行端到端测试，覆盖登录、核心业务流程等。如已具备相关经验，欢迎贡献。

---

如有任何测试相关疑问或改进建议，请在 GitHub Issue 中反馈！ 