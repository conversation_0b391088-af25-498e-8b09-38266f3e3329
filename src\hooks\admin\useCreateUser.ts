import { useQueryClient } from "@tanstack/react-query";

import { queryKeys } from "@/constants/queryKeys";
import { useAdminSuccessToast } from "@/hooks/useAdminSuccessToast";
import { request } from "@/lib/http";
import type { UserRole } from "@/types/user";

interface NewUserInput {
  username: string;
  password: string;
  role: UserRole;
}

export function useCreateUser() {
  const qc = useQueryClient();
  return useAdminSuccessToast(
    (payload: NewUserInput) =>
      request("/admin/users", {
        method: "POST",
        body: JSON.stringify(payload),
      }),
    {
      onSuccess: () => {
        qc.invalidateQueries({ queryKey: queryKeys.adminUsers() });
      },
    },
    "创建成功"
  );
} 