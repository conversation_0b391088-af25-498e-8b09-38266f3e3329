import type { D1Database, R2Bucket } from '@cloudflare/workers-types';
import { v4 as uuidv4 } from 'uuid';

import type {
  Image,
  ImageUploadInput,
  ImageUploadResult,
  ImageDeleteResult,
  ImageListQuery,
} from './schema';
import { createImageRepository, type FindOptions } from './repository';

export interface ImageService {
  uploadImage(
    file: File,
    metadata: ImageUploadInput
  ): Promise<ImageUploadResult>;
  getImagesByResource(
    resourceType: string,
    resourceId: string,
    query: ImageListQuery
  ): Promise<{ images: Image[]; total: number }>;
  getBatchImages(
    eventIds: string[],
    variant?: string,
    imageType?: string
  ): Promise<Record<string, Image | null>>;
  deleteImages(paths: string[]): Promise<ImageDeleteResult>;
  generateImagePath(
    resourceType: string,
    resourceId: string,
    imageType: string,
    variant: string,
    format: string
  ): string;
}

export class ImageServiceImpl implements ImageService {
  private repository;

  constructor(
    private readonly db: D1Database,
    private readonly r2: R2Bucket
  ) {
    this.repository = createImageRepository(db);
  }

  async uploadImage(
    file: File,
    metadata: ImageUploadInput
  ): Promise<ImageUploadResult> {
    // 验证文件
    this.validateFile(file);

    // 如果没有提供 groupId，说明是新的图片组，需要删除旧的同类型图片
    if (!metadata.groupId) {
      await this.cleanupOldImages(
        metadata.category,
        metadata.resourceId,
        metadata.imageType
      );
    }

    // 提取文件信息
    const fileInfo = await this.extractFileInfo(file);

    // 生成文件路径
    const filePath = this.generateImagePath(
      metadata.category,
      metadata.resourceId,
      metadata.imageType,
      metadata.variant,
      fileInfo.format
    );

    // 上传到 R2
    await this.uploadToR2(filePath, file);

    // 保存到数据库
    const groupId = metadata.groupId || uuidv4();

    console.log('uploadImage Debug - saving to database:', {
      groupId,
      resourceType: metadata.category,
      resourceId: metadata.resourceId,
      imageType: metadata.imageType,
      variant: metadata.variant,
      filePath,
    });

    const image = await this.repository.create({
      groupId,
      resourceType: metadata.category,
      resourceId: metadata.resourceId,
      imageType: metadata.imageType,
      variant: metadata.variant,
      filePath,
      fileSize: fileInfo.size,
      width: fileInfo.width,
      height: fileInfo.height,
      format: fileInfo.format,
    });

    console.log('uploadImage Debug - saved image:', image);

    return {
      id: image.id,
      groupId: image.group_id,
      relativePath: image.file_path,
      variant: image.variant,
      metadata: {
        size: fileInfo.size,
        dimensions: {
          width: fileInfo.width,
          height: fileInfo.height,
        },
        format: fileInfo.format,
      },
    };
  }

  async getImagesByResource(
    resourceType: string,
    resourceId: string,
    query: ImageListQuery
  ): Promise<{ images: Image[]; total: number }> {
    console.log('getImagesByResource Debug - input parameters:', {
      resourceType,
      resourceId,
      query,
    });

    const page = parseInt(query.page || '1');
    const pageSize = parseInt(query.pageSize || '20');
    const offset = (page - 1) * pageSize;

    const options: FindOptions = {
      variant: query.variant,
      imageType: query.imageType,
      limit: pageSize,
      offset,
    };

    console.log('getImagesByResource Debug - query options:', options);

    const images = await this.repository.findByResource(
      resourceType,
      resourceId,
      options
    );

    console.log('getImagesByResource Debug - found images:', {
      count: images.length,
      images: images.map((img) => ({
        id: img.id,
        resource_type: img.resource_type,
        resource_id: img.resource_id,
        image_type: img.image_type,
        variant: img.variant,
      })),
    });

    // 获取总数（简化实现，实际应该单独查询）
    const allImages = await this.repository.findByResource(
      resourceType,
      resourceId,
      {
        variant: query.variant,
        imageType: query.imageType,
      }
    );

    return {
      images,
      total: allImages.length,
    };
  }

  async getBatchImages(
    eventIds: string[],
    variant?: string,
    imageType?: string
  ): Promise<Record<string, Image | null>> {
    return await this.repository.findBatchByEvents(
      eventIds,
      variant,
      imageType
    );
  }

  async deleteImages(paths: string[]): Promise<ImageDeleteResult> {
    const failedPaths: string[] = [];
    const successPaths: string[] = [];

    // 从 R2 删除文件
    for (const path of paths) {
      try {
        await this.deleteFromR2(path);
        successPaths.push(path);
      } catch (error) {
        console.error(`Failed to delete ${path} from R2:`, error);
        failedPaths.push(path);
      }
    }

    // 从数据库删除记录
    if (successPaths.length > 0) {
      await this.repository.deleteByPaths(successPaths);
    }

    return {
      deletedCount: successPaths.length,
      failedPaths,
    };
  }

  generateImagePath(
    resourceType: string,
    resourceId: string,
    imageType: string,
    variant: string,
    format: string
  ): string {
    const filename =
      variant === 'original'
        ? `${imageType}.${format}`
        : `${imageType}_${variant}.${format}`;

    return `/images/${resourceType}s/${resourceId}/${filename}`;
  }

  private validateFile(file: File): void {
    const maxSize = 5 * 1024 * 1024; // 5MB
    const allowedTypes = [
      'image/jpeg',
      'image/png',
      'image/webp',
      'image/svg+xml',
    ];
    const allowedExtensions = ['.jpg', '.jpeg', '.png', '.webp', '.svg'];

    // 首先检查 MIME 类型
    if (!allowedTypes.includes(file.type)) {
      const error = new Error(
        `不支持的文件类型: ${file.type}。支持的类型: ${allowedTypes.join(', ')}`
      );
      (error as any).isValidationError = true;
      throw error;
    }

    // 检查文件扩展名
    const fileName = file.name.toLowerCase();
    const hasValidExtension = allowedExtensions.some((ext) =>
      fileName.endsWith(ext)
    );

    if (!hasValidExtension) {
      const error = new Error(
        `不支持的文件扩展名。支持的扩展名: ${allowedExtensions.join(', ')}`
      );
      (error as any).isValidationError = true;
      throw error;
    }

    // 检查文件大小
    if (file.size > maxSize) {
      const error = new Error('文件大小超过限制（5MB）');
      (error as any).isValidationError = true;
      throw error;
    }

    // 检查文件大小最小值
    if (file.size < 100) {
      const error = new Error('文件大小过小，可能不是有效的图片文件');
      (error as any).isValidationError = true;
      throw error;
    }

    // 检查文件名长度
    if (file.name.length > 255) {
      const error = new Error('文件名过长（最大255字符）');
      (error as any).isValidationError = true;
      throw error;
    }

    // 检查文件名是否包含非法字符
    const invalidChars = /[<>:"/\\|?*\x00-\x1f]/;
    if (invalidChars.test(file.name)) {
      const error = new Error('文件名包含非法字符');
      (error as any).isValidationError = true;
      throw error;
    }
  }

  private async extractFileInfo(file: File): Promise<{
    size: number;
    width: number;
    height: number;
    format: string;
  }> {
    const format = file.type.split('/')[1] || 'unknown';

    try {
      // 尝试从文件中提取图片尺寸
      const dimensions = await this.getImageDimensions(file);

      return {
        size: file.size,
        width: dimensions.width,
        height: dimensions.height,
        format,
      };
    } catch (error) {
      console.warn('Failed to extract image dimensions:', error);

      return {
        size: file.size,
        width: 0,
        height: 0,
        format,
      };
    }
  }

  private async getImageDimensions(
    file: File
  ): Promise<{ width: number; height: number }> {
    // 在 Cloudflare Workers 环境中，我们无法直接解析图片尺寸
    // 这里返回默认值，实际应该由前端在上传时提供尺寸信息
    // 或者使用专门的图片处理库

    // 根据文件大小估算尺寸（临时方案）
    const estimatedSize = Math.sqrt(file.size / 3); // 粗略估算

    return {
      width: Math.round(estimatedSize),
      height: Math.round(estimatedSize),
    };
  }

  private async uploadToR2(path: string, file: File): Promise<void> {
    // 移除开头的斜杠，R2 key 不应该以斜杠开头
    const key = path.startsWith('/') ? path.slice(1) : path;

    await this.r2.put(key, file.stream(), {
      httpMetadata: {
        contentType: file.type,
      },
    });
  }

  private async deleteFromR2(path: string): Promise<void> {
    // 移除开头的斜杠
    const key = path.startsWith('/') ? path.slice(1) : path;
    await this.r2.delete(key);
  }

  /**
   * 清理旧的同类型图片
   * 在上传新图片时，删除同一资源的同一类型的所有旧图片
   */
  private async cleanupOldImages(
    resourceType: string,
    resourceId: string,
    imageType: string
  ): Promise<void> {
    try {
      // 查找同一资源的同一类型的所有图片
      const existingImages = await this.repository.findByResource(
        resourceType,
        resourceId,
        { imageType }
      );

      if (existingImages.length === 0) {
        return; // 没有旧图片需要删除
      }

      console.log(
        `Cleaning up ${existingImages.length} old images for ${resourceType}:${resourceId}:${imageType}`
      );

      // 提取文件路径
      const pathsToDelete = existingImages
        .map((img) => img.file_path)
        .filter(Boolean);

      if (pathsToDelete.length > 0) {
        // 删除文件和数据库记录
        await this.deleteImages(pathsToDelete);
        console.log(
          `Successfully cleaned up ${pathsToDelete.length} old images`
        );
      }
    } catch (error) {
      console.error('Failed to cleanup old images:', error);
      // 不抛出错误，避免影响新图片上传
    }
  }
}

export function createImageService(db: D1Database, r2: R2Bucket): ImageService {
  return new ImageServiceImpl(db, r2);
}
