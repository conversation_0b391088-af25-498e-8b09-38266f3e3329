/**
 * 图片模块测试页面
 * 用于测试图片功能和调试问题
 */

'use client';

import React, { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { ImageService } from '@/services/imageService';
import type { ImageInfo } from '@/types/image';

export default function ImageTestPage() {
  const [testResults, setTestResults] = useState<string[]>([]);
  const [isLoading, setIsLoading] = useState(false);

  const addResult = (message: string) => {
    setTestResults(prev => [...prev, `${new Date().toLocaleTimeString()}: ${message}`]);
  };

  // 测试URL生成
  const testUrlGeneration = () => {
    addResult('=== 测试URL生成 ===');
    
    // 测试正常路径
    const normalPath = '/images/events/test-event/poster.jpg';
    const normalUrl = ImageService.getUrl(normalPath);
    addResult(`正常路径: ${normalPath} -> ${normalUrl}`);

    // 测试缩略图路径
    const thumbUrl = ImageService.getThumbnailUrl(normalPath);
    addResult(`缩略图: ${normalPath} -> ${thumbUrl}`);

    // 测试空值
    const emptyUrl = ImageService.getUrl('');
    addResult(`空字符串: '' -> ${emptyUrl}`);

    // 测试undefined
    const undefinedUrl = ImageService.getUrl(undefined as any);
    addResult(`undefined -> ${undefinedUrl}`);

    // 测试null
    const nullUrl = ImageService.getUrl(null as any);
    addResult(`null -> ${nullUrl}`);
  };

  // 测试API调用
  const testApiCall = async () => {
    setIsLoading(true);
    addResult('=== 测试API调用 ===');

    try {
      // 首先测试直接的API调用
      addResult('测试直接API调用: /images/event/reitaisai-22');

      const directResponse = await fetch('http://localhost:8787/images/event/reitaisai-22');
      addResult(`直接API调用状态: ${directResponse.status} ${directResponse.statusText}`);

      if (directResponse.ok) {
        const directData = await directResponse.json();
        addResult(`直接API返回数据: ${JSON.stringify(directData, null, 2)}`);
      } else {
        const errorText = await directResponse.text();
        addResult(`直接API错误响应: ${errorText}`);
      }

      // 然后测试通过ImageService的调用
      addResult('测试通过ImageService的API调用...');
      const result = await ImageService.list({
        category: 'event',
        resourceId: 'reitaisai-22',
      });
      addResult(`ImageService调用成功: ${JSON.stringify(result, null, 2)}`);

      if (result.images && result.images.length > 0) {
        const firstImage = result.images[0];
        addResult(`第一张图片信息: ${JSON.stringify(firstImage, null, 2)}`);

        // 测试URL生成
        const imageUrl = ImageService.getUrl(firstImage.relativePath);
        const thumbnailUrl = ImageService.getThumbnailUrl(firstImage.relativePath);

        addResult(`生成的图片URL: ${imageUrl}`);
        addResult(`生成的缩略图URL: ${thumbnailUrl}`);

        // 测试URL是否可访问
        try {
          const urlTest = await fetch(imageUrl, { method: 'HEAD' });
          addResult(`图片URL可访问性: ${urlTest.status} ${urlTest.statusText}`);
        } catch (urlError) {
          addResult(`图片URL测试失败: ${urlError}`);
        }
      }
    } catch (error) {
      addResult(`API调用失败: ${error instanceof Error ? error.message : String(error)}`);
      console.error('API调用错误:', error);
    } finally {
      setIsLoading(false);
    }
  };

  // 测试文件验证
  const testFileValidation = () => {
    addResult('=== 测试文件验证 ===');

    // 创建测试文件
    const validFile = new File(['test'], 'test.jpg', { type: 'image/jpeg' });
    const invalidFile = new File(['test'], 'test.txt', { type: 'text/plain' });
    const largeFile = new File(['x'.repeat(11 * 1024 * 1024)], 'large.jpg', { type: 'image/jpeg' });

    const validResult = ImageService.validate(validFile);
    addResult(`有效文件 (test.jpg): ${JSON.stringify(validResult)}`);

    const invalidResult = ImageService.validate(invalidFile);
    addResult(`无效文件 (test.txt): ${JSON.stringify(invalidResult)}`);

    const largeResult = ImageService.validate(largeFile);
    addResult(`大文件 (11MB): ${JSON.stringify(largeResult)}`);
  };

  // 测试模拟图片数据
  const testMockImageData = () => {
    addResult('=== 测试模拟图片数据 ===');

    const mockImages: ImageInfo[] = [
      {
        id: 'test-1',
        relativePath: '/images/events/test-event/poster.jpg',
        variant: 'original',
        metadata: {
          size: 1024000,
          dimensions: { width: 800, height: 600 },
          format: 'jpeg',
        },
        createdAt: new Date().toISOString(),
      },
      {
        id: 'test-2',
        relativePath: '/images/events/test-event/banner.png',
        variant: 'large',
        metadata: {
          size: 2048000,
          dimensions: { width: 1200, height: 400 },
          format: 'png',
        },
        createdAt: new Date().toISOString(),
      },
    ];

    mockImages.forEach(image => {
      const url = ImageService.getUrl(image.relativePath);
      const thumbUrl = ImageService.getThumbnailUrl(image.relativePath);
      addResult(`图片 ${image.id}: ${image.relativePath}`);
      addResult(`  完整URL: ${url}`);
      addResult(`  缩略图URL: ${thumbUrl}`);
    });
  };

  const clearResults = () => {
    setTestResults([]);
  };

  return (
    <div className="container mx-auto py-8 space-y-6">
      <div>
        <h1 className="text-3xl font-bold">图片模块测试</h1>
        <p className="text-muted-foreground">
          测试图片模块的各项功能和调试问题
        </p>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        {/* 测试控制 */}
        <Card>
          <CardHeader>
            <CardTitle>测试控制</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <Button onClick={testUrlGeneration} className="w-full">
              测试URL生成
            </Button>
            <Button 
              onClick={testApiCall} 
              disabled={isLoading}
              className="w-full"
            >
              {isLoading ? '测试中...' : '测试API调用'}
            </Button>
            <Button onClick={testFileValidation} className="w-full">
              测试文件验证
            </Button>
            <Button onClick={testMockImageData} className="w-full">
              测试模拟数据
            </Button>
            <Button onClick={clearResults} variant="outline" className="w-full">
              清空结果
            </Button>
          </CardContent>
        </Card>

        {/* 环境信息 */}
        <Card>
          <CardHeader>
            <CardTitle>环境信息</CardTitle>
          </CardHeader>
          <CardContent className="space-y-2 text-sm">
            <div>
              <strong>API URL:</strong> {process.env.NEXT_PUBLIC_API_URL || '未设置'}
            </div>
            <div>
              <strong>CDN URL:</strong> {process.env.NEXT_PUBLIC_CDN_URL || '未设置'}
            </div>
            <div>
              <strong>环境:</strong> {process.env.NODE_ENV}
            </div>
            <div>
              <strong>当前时间:</strong> {new Date().toLocaleString()}
            </div>
          </CardContent>
        </Card>
      </div>

      {/* 测试结果 */}
      <Card>
        <CardHeader>
          <CardTitle>测试结果</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="bg-gray-100 p-4 rounded-lg max-h-96 overflow-y-auto">
            {testResults.length === 0 ? (
              <p className="text-gray-500">点击上方按钮开始测试...</p>
            ) : (
              <pre className="text-xs whitespace-pre-wrap">
                {testResults.join('\n')}
              </pre>
            )}
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
