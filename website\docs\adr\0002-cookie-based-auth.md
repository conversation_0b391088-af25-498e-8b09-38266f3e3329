# 2. 采用基于 Cookie 的认证方案

日期: 2025-07-21

## 状态

已实施 (2025-07-27)

## 背景

在实现用户认证系统时，我们需要选择一个合适的认证方案。主要考虑以下几个方面：

1. 项目时间紧迫，需要快速上线
2. 需要确保基本的安全性
3. 需要良好的用户体验
4. 需要考虑后续的扩展性

主要的可选方案有：

1. Cookie-based 认证
   - 使用 HttpOnly Cookie 存储会话信息
   - 后端完全控制会话管理
   - 依赖浏览器的 Cookie 机制

2. JWT-based 认证
   - Token 存储在前端
   - 无状态设计
   - 需要额外的刷新 Token 机制

3. OAuth/SSO 方案
   - 支持第三方登录
   - 统一的认证中心
   - 配置和维护成本较高

## 决策

我们决定采用基于 Cookie 的认证方案，主要考虑以下几点：

1. 实现简单，开发成本低
   - 使用浏览器原生的 Cookie 机制
   - 不需要额外的 Token 管理
   - 后端框架通常都有成熟的支持

2. 安全性有保障
   - HttpOnly Cookie 防止 XSS 攻击
   - 内置 CSRF 保护
   - 成熟的会话管理机制

3. 用户体验好
   - 浏览器原生支持
   - 自动携带认证信息
   - 无需前端存储处理

4. 便于调试和维护
   - 浏览器开发工具直接支持
   - 问题排查方便
   - 监控手段丰富

## 实现细节与安全考量

### Cookie 设置

- **SameSite**：默认 `Lax`，减少 CSRF 风险；当前端与 API 位于不同子域时需设为 `None` 且同时加 `Secure`。
- **Secure**：生产环境必须开启，仅允许 HTTPS 传输。
- **HttpOnly**：始终开启，防止 XSS 读取。
- **Path**：`/`，覆盖整个站点。
- **Expires / Max-Age**：建议 30 min idle / 7 d absolute，可按业务需求调整。
- **内容**：仅存储会话 ID 或签名 Token，避免存储用户敏感信息。

- 登录成功后 **重新生成会话 ID**，防止 Session Fixation。
- 若未来改用 Signed Cookie，可使用「会话版本号」或 **per-user salt**，确保登出后旧 Cookie 无效。

示例：

```http
Set-Cookie: session=base64url(HMAC(userId|exp)); Path=/; HttpOnly; Secure; SameSite=Lax; Max-Age=1800
```

### 服务端会话存储

- **单实例**：可直接存于内存或 Workers KV。
- **多实例 / 边缘节点**：推荐使用 D1 / KV / Redis 等中心化存储，或改用无状态签名 Cookie（Signed Cookie）减少一致性成本。
- **注销流程**：删除集中式会话记录或更新签名版本号实现吊销。
- 记录 `session_id ↔ user_id` 映射，便于审计与强制登出。
- 在 Cloudflare Workers 多实例场景下无法使用传统 **Sticky Session**。可考虑：
  - 在 Worker 进程内使用 **LRU 缓存 2–5 s**，结合集中式 KV/D1 减少读写
  - 利用 **Durable Objects** 进行会话分片，或采用 **Signed Cookie** 彻底去中心化

### CSRF 防护

- SameSite=Lax 提供第一层保护。
- 对所有写请求（POST/PUT/DELETE 等）再校验 `X-CSRF-Token` Header，Token 通过 Double Submit Cookie 方式下发。
- 明确 CSRF Token 的生命周期：可在每次响应中刷新，或设定短周期失效（如 30 min）。
- 若前端以 SPA 形式运行，也可评估 **SameSite=Strict + 自定义 Header** 方案（`credentials: include`），减少双提交实现成本。

### 扩展与迁移路径

- 当出现以下场景应评估引入 JWT 或 OAuth，或启用混合认证模式：
  1. 支持移动端原生应用
  2. 需要向第三方开放 API
  3. 会话一致性成为性能瓶颈
- Web 端可继续使用 Cookie，移动端/第三方使用 JWT，实现平滑过渡。

### Cloudflare Workers 注意事项

- 不同子域需在 `wrangler.toml` 中配置 `custom_domain` 并确保 `SameSite=None` + `Secure`。
- Workers 响应体大小受限 1 MiB，认证逻辑应保持轻量。
- Workers KV 读写约 10 ms，可在 Worker 内缓存 Session 结果 2–5 s，降低远程 KV 访问频率。

## 后果

### 积极影响

1. 开发效率
   - 快速实现基础认证功能
   - 减少自定义代码
   - 利用成熟的安全实践

2. 可维护性
   - 代码简洁清晰
   - 职责划分明确
   - 便于问题排查

3. 安全性
   - 默认安全配置
   - 防范常见攻击
   - 会话管理可靠

### 消极影响

1. 扩展限制
   - 跨域调用复杂
   - 移动端适配可能需要调整
   - 分布式部署需要额外配置

2. 性能影响
   - Cookie 会随请求发送
   - 需要服务端存储会话
   - 可能需要会话同步

3. 架构约束
   - 与浏览器耦合
   - 不适合纯 API 服务
   - 扩展认证方式需要改造

## 实施计划

1. Phase 1: 基础实现
   - 配置 Cookie 会话
   - 实现登录注册
   - 添加 CSRF 保护

2. Phase 2: 体验优化
   - 完善错误处理
   - 优化交互流程
   - 添加记住登录

3. Phase 3: 性能优化
   - 会话存储优化
   - 负载均衡支持
   - 监控告警

## 备选方案

如果后续需要支持更多场景，可以考虑：

1. 迁移到 JWT
   - 支持跨域调用
   - 适配移动端
   - 支持分布式部署

2. 引入 OAuth
   - 支持第三方登录
   - 统一认证中心
   - 多应用支持

## 实施总结 (2025-07-27)

### 完成情况

基于 Cookie 的认证系统已成功实施并投入使用，核心功能均已完成：

1. **基础认证功能**
   - ✅ 用户注册和登录页面
   - ✅ AuthProvider 和用户上下文管理
   - ✅ Cookie 会话存储和验证
   - ✅ 中间件路由保护（/admin 路由）

2. **安全实现**
   - ✅ HttpOnly Cookie 配置
   - ✅ 会话状态管理和本地存储同步
   - ✅ 登录状态检查和自动跳转
   - ✅ 登出功能和会话清理

3. **用户体验**
   - ✅ 表单验证（React Hook Form + Zod）
   - ✅ 错误处理和用户反馈
   - ✅ 登录后自动跳转到管理页面
   - ✅ 未授权访问的重定向处理

### 待完善功能

根据 TODO.md，以下后端安全功能仍需实施：

1. **高级安全功能**
   - [ ] CSRF (Double Submit Cookie) 防护
   - [ ] 会话的 Idle / Absolute 过期策略
   - [ ] Session Fixation 防护 (regenerateSessionId)
   - [ ] /auth/refresh 刷新端点

2. **生产环境优化**
   - [ ] Cookie 安全策略验证 (SameSite, Secure, HttpOnly)
   - [ ] 会话存储优化
   - [ ] 监控和审计功能

### 经验教训

1. **架构决策验证**
   - Cookie 认证方案确实简化了实现复杂度
   - 浏览器原生支持提供了良好的用户体验
   - 中间件模式有效保护了管理路由

2. **开发效率**
   - React Hook Form + Zod 的组合提供了优秀的表单体验
   - AuthProvider 模式简化了状态管理
   - TypeScript 类型安全有效防止了认证相关错误

3. **安全考量**
   - 基础的 Cookie 安全配置已到位
   - 需要在生产环境中完善高级安全功能
   - 会话管理策略需要根据实际使用情况调整

### 后续优化建议

1. **安全加固**
   - 优先实施 CSRF 防护
   - 完善会话过期和刷新机制
   - 添加安全审计日志

2. **性能优化**
   - 监控会话存储性能
   - 优化认证检查的频率
   - 考虑会话缓存策略

3. **用户体验**
   - 添加"记住我"功能
   - 优化登录流程的加载状态
   - 完善错误提示的国际化
