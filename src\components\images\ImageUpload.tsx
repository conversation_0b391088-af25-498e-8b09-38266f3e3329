/**
 * ImageUpload 图片上传组件
 * 支持拖拽上传、批量上传、进度显示和预览
 */

'use client';

import React, { useState } from 'react';
import { Upload, AlertCircle, CheckCircle, X } from 'lucide-react';
import { cn } from '@/lib/utils';
import { Button } from '@/components/ui/button';
import { FileUpload } from '@/components/ui/file-upload';
import { Progress } from '@/components/ui/progress';
import { useImageUpload, useBatchImageUpload } from '@/hooks/useImageUpload';
import { ImageService } from '@/services/imageService';
import type { ImageUploadProps, ImageInfo } from '@/types/image';

export function ImageUpload({
  category,
  resourceId,
  imageType,
  variant = 'original',
  groupId,
  multiple = false,
  accept = 'image/*',
  maxSize = 10 * 1024 * 1024, // 10MB
  onUploadSuccess,
  onUploadError,
  className,
  disabled = false,
}: ImageUploadProps) {
  const [selectedFiles, setSelectedFiles] = useState<File[]>([]);
  const [uploadProgress, setUploadProgress] = useState<Record<string, number>>({});
  const [uploadResults, setUploadResults] = useState<Record<string, ImageInfo | Error>>({});

  // 单个文件上传
  const singleUpload = useImageUpload({
    onSuccess: (data) => {
      console.log('Upload success:', data);
      setUploadResults(prev => ({ ...prev, [data.id]: data }));
      onUploadSuccess?.([data]);
    },
    onError: (error) => {
      console.error('Upload error:', error);
      onUploadError?.(error);
    },
    showToast: false, // 我们自己处理提示
  });

  // 批量上传
  const batchUpload = useBatchImageUpload({
    onProgress: (progress) => {
      // 更新整体进度
      console.log('Upload progress:', progress);
    },
    onSuccess: (data) => {
      data.forEach(image => {
        setUploadResults(prev => ({ ...prev, [image.id]: image }));
      });
      onUploadSuccess?.(data);
      setSelectedFiles([]);
    },
    onError: (error) => {
      console.error('Batch upload error:', error);
      onUploadError?.(error);
    },
    showToast: true,
  });

  // 处理文件选择
  const handleFilesChange = (files: File[]) => {
    setSelectedFiles(files);
    setUploadResults({});
    setUploadProgress({});
  };

  // 处理文件验证错误
  const handleFileError = (error: string) => {
    onUploadError?.(new Error(error));
  };

  // 开始上传
  const handleUpload = async () => {
    if (selectedFiles.length === 0) return;

    const metadata = {
      category,
      resourceId,
      imageType,
      variant,
      groupId,
    };

    if (multiple && selectedFiles.length > 1) {
      // 批量上传
      batchUpload.mutate({ files: selectedFiles, metadata });
    } else {
      // 单个上传
      const file = selectedFiles[0];
      singleUpload.mutate({ ...metadata, file });
    }
  };

  // 移除文件
  const removeFile = (index: number) => {
    const newFiles = selectedFiles.filter((_, i) => i !== index);
    setSelectedFiles(newFiles);
  };

  // 清空所有文件
  const clearFiles = () => {
    setSelectedFiles([]);
    setUploadResults({});
    setUploadProgress({});
  };

  const isUploading = singleUpload.isPending || batchUpload.isPending;
  const hasFiles = selectedFiles.length > 0;
  const hasResults = Object.keys(uploadResults).length > 0;

  return (
    <div className={cn('space-y-4', className)}>
      {/* 文件上传区域 */}
      <FileUpload
        accept={accept}
        multiple={multiple}
        maxSize={maxSize}
        onFilesChange={handleFilesChange}
        onError={handleFileError}
        disabled={disabled || isUploading}
      />

      {/* 上传控制 */}
      {hasFiles && (
        <div className="flex items-center justify-between">
          <div className="text-sm text-muted-foreground">
            已选择 {selectedFiles.length} 个文件
          </div>
          <div className="flex gap-2">
            <Button
              variant="outline"
              size="sm"
              onClick={clearFiles}
              disabled={isUploading}
            >
              清空
            </Button>
            <Button
              onClick={handleUpload}
              disabled={isUploading || !hasFiles}
              size="sm"
            >
              {isUploading ? (
                <>
                  <Upload className="h-4 w-4 mr-2 animate-spin" />
                  上传中...
                </>
              ) : (
                <>
                  <Upload className="h-4 w-4 mr-2" />
                  上传 {selectedFiles.length > 1 ? `${selectedFiles.length} 个文件` : '文件'}
                </>
              )}
            </Button>
          </div>
        </div>
      )}

      {/* 上传进度 */}
      {isUploading && (
        <div className="space-y-2">
          <div className="text-sm font-medium">上传中...</div>
          {multiple && selectedFiles.length > 1 ? (
            <Progress value={0} showValue />
          ) : (
            <Progress value={50} showValue />
          )}
        </div>
      )}

      {/* 上传结果 */}
      {hasResults && (
        <div className="space-y-2">
          <div className="text-sm font-medium">上传结果</div>
          <div className="space-y-2">
            {Object.entries(uploadResults).map(([id, result]) => (
              <div
                key={id}
                className={cn(
                  'flex items-center gap-3 p-3 rounded-lg border',
                  result instanceof Error
                    ? 'border-red-200 bg-red-50'
                    : 'border-green-200 bg-green-50'
                )}
              >
                {result instanceof Error ? (
                  <>
                    <AlertCircle className="h-5 w-5 text-red-500" />
                    <div className="flex-1">
                      <p className="text-sm font-medium text-red-700">上传失败</p>
                      <p className="text-xs text-red-600">{result.message}</p>
                    </div>
                  </>
                ) : (
                  <>
                    <CheckCircle className="h-5 w-5 text-green-500" />
                    <div className="flex-1">
                      <p className="text-sm font-medium text-green-700">上传成功</p>
                      <p className="text-xs text-green-600">
                        {result.relativePath} • {Math.round(result.metadata.size / 1024)}KB
                      </p>
                    </div>
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => {
                        setUploadResults(prev => {
                          const newResults = { ...prev };
                          delete newResults[id];
                          return newResults;
                        });
                      }}
                    >
                      <X className="h-4 w-4" />
                    </Button>
                  </>
                )}
              </div>
            ))}
          </div>
        </div>
      )}
    </div>
  );
}
