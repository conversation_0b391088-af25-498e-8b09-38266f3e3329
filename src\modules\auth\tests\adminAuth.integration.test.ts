import { describe, it, expect } from 'vitest';

import app from '@/app';

// @ts-ignore
const Request = globalThis.Request;

function createMockDB(role?: 'admin' | 'editor' | 'viewer') {
  return {
    prepare: (query: string) => {
      const upper = query.toUpperCase();

      // 提供公共实现
      const buildResponse = () => ({
        all: async () => {
          if (upper.includes('FROM AUTH_SESSION')) {
            return {
              results: role ? [{ id: 'u1', username: 'tester', role }] : [],
            };
          }
          if (upper.includes('FROM CIRCLES')) {
            return { results: [] };
          }
          return { results: [] };
        },
        first: async () => null,
        run: async () => ({ success: true }),
      });

      return {
        ...buildResponse(),
        bind: (..._args: any[]) => buildResponse(),
      };
    },
  };
}

function fetchWithEnv(url: string, env: any, headers?: HeadersInit) {
  const base = url.startsWith('http') ? url : `http://localhost${url}`;
  const req = new Request(base, { headers });
  return app.fetch(req, env);
}

describe('Admin auth & roleGuard', () => {
  it('should reject unauthenticated access', async () => {
    const res = await fetchWithEnv('/admin/circles', { DB: createMockDB() });
    expect(res.status).toBe(401);
  });

  it('should reject viewer role', async () => {
    const res = await fetchWithEnv(
      '/admin/circles',
      { DB: createMockDB('viewer') },
      {
        Cookie: 'auth_session=viewer_session',
      }
    );
    expect(res.status).toBe(403);
  });

  it('should allow admin role', async () => {
    const res = await fetchWithEnv(
      '/admin/circles',
      { DB: createMockDB('admin') },
      {
        Cookie: 'auth_session=admin_session',
      }
    );
    expect(res.status).toBe(200);
  });
});
