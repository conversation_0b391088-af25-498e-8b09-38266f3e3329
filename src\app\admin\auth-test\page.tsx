/**
 * 认证功能测试页面
 * 用于验证 Cookie-based 认证机制是否正常工作
 */

'use client';

import React, { useState, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { authService } from '@/services/auth';
import { useAuth } from '@/contexts/user';
import { useAuthStore } from '@/stores/auth';
import type { User } from '@/types/user';

export default function AuthTestPage() {
  const [testResults, setTestResults] = useState<string[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [credentials, setCredentials] = useState({ username: '', password: '' });
  
  const { user: contextUser, isLoading: contextLoading } = useAuth();
  const authStore = useAuthStore();

  const addResult = (message: string, type: 'info' | 'success' | 'error' = 'info') => {
    const timestamp = new Date().toLocaleTimeString();
    const prefix = type === 'success' ? '✅' : type === 'error' ? '❌' : 'ℹ️';
    setTestResults(prev => [...prev, `${timestamp} ${prefix} ${message}`]);
  };

  // 测试认证状态检查
  const testAuthStatus = async () => {
    addResult('=== 测试认证状态检查 ===');
    
    try {
      const isAuth = await authService.isAuthenticated();
      addResult(`认证状态: ${isAuth ? '已认证' : '未认证'}`, isAuth ? 'success' : 'error');
      
      if (isAuth) {
        const user = await authService.getCurrentUser();
        addResult(`当前用户: ${user.username} (${user.role})`, 'success');
      }
    } catch (error) {
      addResult(`认证状态检查失败: ${error}`, 'error');
    }
  };

  // 测试登录功能
  const testLogin = async () => {
    if (!credentials.username || !credentials.password) {
      addResult('请输入用户名和密码', 'error');
      return;
    }

    setIsLoading(true);
    addResult('=== 测试登录功能 ===');
    
    try {
      const result = await authService.login(credentials);
      addResult(`登录成功: ${result.user.username} (${result.user.role})`, 'success');
      
      // 测试登录后的认证状态
      const isAuth = await authService.isAuthenticated();
      addResult(`登录后认证状态: ${isAuth ? '已认证' : '未认证'}`, isAuth ? 'success' : 'error');
    } catch (error) {
      addResult(`登录失败: ${error}`, 'error');
    } finally {
      setIsLoading(false);
    }
  };

  // 测试权限检查
  const testPermissions = async () => {
    addResult('=== 测试权限检查 ===');
    
    try {
      const adminPerm = await authService.hasPermission(['admin']);
      addResult(`Admin 权限: ${adminPerm ? '有权限' : '无权限'}`, adminPerm ? 'success' : 'info');
      
      const editorPerm = await authService.hasPermission(['editor']);
      addResult(`Editor 权限: ${editorPerm ? '有权限' : '无权限'}`, editorPerm ? 'success' : 'info');
      
      const adminEditorPerm = await authService.hasPermission(['admin', 'editor']);
      addResult(`Admin/Editor 权限: ${adminEditorPerm ? '有权限' : '无权限'}`, adminEditorPerm ? 'success' : 'info');
    } catch (error) {
      addResult(`权限检查失败: ${error}`, 'error');
    }
  };

  // 测试登出功能
  const testLogout = async () => {
    addResult('=== 测试登出功能 ===');
    
    try {
      await authStore.logout();
      addResult('登出成功', 'success');
      
      // 测试登出后的认证状态
      const isAuth = await authService.isAuthenticated();
      addResult(`登出后认证状态: ${isAuth ? '已认证' : '未认证'}`, !isAuth ? 'success' : 'error');
    } catch (error) {
      addResult(`登出失败: ${error}`, 'error');
    }
  };

  // 测试图片删除权限
  const testImagePermissions = async () => {
    addResult('=== 测试图片管理权限 ===');
    
    try {
      // 模拟图片删除请求
      const response = await fetch('/admin/images', {
        method: 'DELETE',
        headers: {
          'Content-Type': 'application/json',
        },
        credentials: 'include',
        body: JSON.stringify({ relativePaths: ['/test/image.jpg'] }),
      });
      
      if (response.ok) {
        addResult('图片删除权限验证通过', 'success');
      } else if (response.status === 401) {
        addResult('图片删除权限验证失败: 未认证', 'error');
      } else if (response.status === 403) {
        addResult('图片删除权限验证失败: 权限不足', 'error');
      } else {
        addResult(`图片删除权限验证失败: ${response.status}`, 'error');
      }
    } catch (error) {
      addResult(`图片删除权限测试失败: ${error}`, 'error');
    }
  };

  // 清空测试结果
  const clearResults = () => {
    setTestResults([]);
  };

  // 页面加载时自动检查认证状态
  useEffect(() => {
    testAuthStatus();
  }, []);

  return (
    <div className="container mx-auto py-8 space-y-6">
      <div>
        <h1 className="text-3xl font-bold">认证功能测试</h1>
        <p className="text-muted-foreground">
          测试 Cookie-based 认证机制的各项功能
        </p>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        {/* 当前状态 */}
        <Card>
          <CardHeader>
            <CardTitle>当前认证状态</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div>
              <strong>Context 用户:</strong> {contextLoading ? '加载中...' : contextUser ? `${contextUser.username} (${contextUser.role})` : '未登录'}
            </div>
            <div>
              <strong>Store 用户:</strong> {authStore.user ? `${authStore.user.username} (${authStore.user.role})` : '未登录'}
            </div>
            <div>
              <strong>Store 认证状态:</strong> {authStore.isAuthenticated ? '已认证' : '未认证'}
            </div>
            <div>
              <strong>Store 加载状态:</strong> {authStore.isLoading ? '加载中' : '已完成'}
            </div>
          </CardContent>
        </Card>

        {/* 登录测试 */}
        <Card>
          <CardHeader>
            <CardTitle>登录测试</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div>
              <Label htmlFor="username">用户名</Label>
              <Input
                id="username"
                value={credentials.username}
                onChange={(e) => setCredentials(prev => ({ ...prev, username: e.target.value }))}
                placeholder="输入用户名"
              />
            </div>
            <div>
              <Label htmlFor="password">密码</Label>
              <Input
                id="password"
                type="password"
                value={credentials.password}
                onChange={(e) => setCredentials(prev => ({ ...prev, password: e.target.value }))}
                placeholder="输入密码"
              />
            </div>
            <Button onClick={testLogin} disabled={isLoading} className="w-full">
              {isLoading ? '登录中...' : '测试登录'}
            </Button>
          </CardContent>
        </Card>
      </div>

      {/* 测试控制 */}
      <Card>
        <CardHeader>
          <CardTitle>功能测试</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
            <Button onClick={testAuthStatus}>检查认证状态</Button>
            <Button onClick={testPermissions}>测试权限检查</Button>
            <Button onClick={testImagePermissions}>测试图片权限</Button>
            <Button onClick={testLogout} variant="destructive">测试登出</Button>
          </div>
          <Button onClick={clearResults} variant="outline" className="w-full">
            清空测试结果
          </Button>
        </CardContent>
      </Card>

      {/* 测试结果 */}
      <Card>
        <CardHeader>
          <CardTitle>测试结果</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="bg-gray-100 p-4 rounded-lg max-h-96 overflow-y-auto">
            {testResults.length === 0 ? (
              <p className="text-gray-500">点击上方按钮开始测试...</p>
            ) : (
              <pre className="text-xs whitespace-pre-wrap">
                {testResults.join('\n')}
              </pre>
            )}
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
