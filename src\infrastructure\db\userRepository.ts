import type { D1Database } from '@cloudflare/workers-types';

import type {
  UserRepository,
  CreateUserData,
  UpdateUserData,
} from '../../modules/user/repository';
import { User } from '../../modules/user/schema';

export class D1UserRepository implements UserRepository {
  constructor(private readonly db: D1Database) {}

  async list(): Promise<User[]> {
    const { results } = await this.db
      .prepare('SELECT id, username, role FROM auth_user ORDER BY username ASC')
      .all();
    return results as User[];
  }

  async findById(id: string): Promise<User | null> {
    const user = await this.db
      .prepare('SELECT id, username, role FROM auth_user WHERE id = ?')
      .bind(id)
      .first();
    return (user as User) ?? null;
  }

  async create(data: CreateUserData): Promise<User> {
    await this.db.batch([
      this.db
        .prepare('INSERT INTO auth_user (id, username, role) VALUES (?, ?, ?)')
        .bind(data.id, data.username, data.role),
      this.db
        .prepare(
          'INSERT INTO auth_key (id, user_id, hashed_password) VALUES (?, ?, ?)'
        )
        .bind(crypto.randomUUID(), data.id, data.hashedPassword),
    ]);

    return (await this.findById(data.id)) as User;
  }

  async update(id: string, changes: UpdateUserData): Promise<User | null> {
    const sets: string[] = [];
    const params: (string | undefined)[] = [];

    if (changes.username !== undefined) {
      sets.push('username = ?');
      params.push(changes.username);
    }
    if (changes.role !== undefined) {
      sets.push('role = ?');
      params.push(changes.role);
    }

    if (sets.length) {
      params.push(id);
      await this.db
        .prepare(`UPDATE auth_user SET ${sets.join(', ')} WHERE id = ?`)
        .bind(...params)
        .run();
    }

    return this.findById(id);
  }

  async updatePassword(id: string, hashedPassword: string): Promise<void> {
    await this.db
      .prepare('UPDATE auth_key SET hashed_password = ? WHERE user_id = ?')
      .bind(hashedPassword, id)
      .run();
  }

  async delete(id: string): Promise<void> {
    await this.db.batch([
      this.db.prepare('DELETE FROM auth_session WHERE user_id = ?').bind(id),
      this.db.prepare('DELETE FROM auth_key WHERE user_id = ?').bind(id),
      this.db.prepare('DELETE FROM auth_user WHERE id = ?').bind(id),
    ]);
  }
}
