// @ts-nocheck
import { expect, test, vi, beforeEach, afterEach } from "vitest"

import EventDetailPage from "@/app/events/[id]/page"
import { renderWithProviders, screen, within } from "@test/test-utils"
import { server, http } from "@test/testServer"

// Mock useParams 返回固定 id
vi.mock("next/navigation", () => {
  return { useParams: () => ({ id: "evt1" }) }
})

// Mock leaflet CSS
vi.mock("leaflet/dist/leaflet.css", () => ({}))
vi.mock("@tailwindcss/postcss", () => ({
  __esModule: true,
  default: () => ({ postcssPlugin: "tailwindcss", Once() {} }),
}))

// Mock react-leaflet 组件
vi.mock("react-leaflet", () => ({
  MapContainer: ({ children }: { children: React.ReactNode }) => (
    <div data-testid="map-container">{children}</div>
  ),
  TileLayer: () => <div data-testid="tile-layer" />,
  Marker: ({ children }: { children: React.ReactNode }) => (
    <div data-testid="map-marker">{children}</div>
  ),
  Popup: ({ children }: { children: React.ReactNode }) => (
    <div data-testid="map-popup">{children}</div>
  ),
}))

// Mock leaflet
vi.mock("leaflet", () => ({}))

// Mock VirtuosoGrid => 渲染静态列表
vi.mock("react-virtuoso", () => {
  return {
    VirtuosoGrid: ({ data, itemContent }: any) => (
      <div data-testid="virtuoso-grid">
        {data.map((item: any, index: number) => itemContent(index, item))}
      </div>
    ),
  }
})

// Mock utils functions
vi.mock("@/app/events/[id]/utils", () => {
  return {
    transformEventDataWithLocale: (event: any) => event,
    transformCirclesData: (circles: any[]) => circles,
    filterCircles: (circles: any[]) => circles, // 直接返回所有社团
    extractCategoryOptions: (circles: any[]) => ["music", "game"], // 返回测试用的分类选项
  }
})

// Mock 新的增强组件
vi.mock("@/components/events/EnhancedEventHeader", () => {
  return {
    __esModule: true,
    default: ({ event, circlesCount }: { event: any; circlesCount: number }) => (
      <div data-testid="enhanced-event-header">
        {event ? (
          <div>
            <h1>{event.name}</h1>
            <div>{event.date} • {event.venue_name}</div>
            <div>{event.venue_address}</div>
            <div>参展商: {circlesCount}+</div>
            <div data-testid="map-container">
              <div data-testid="tile-layer" />
              <div data-testid="map-marker">
                <div data-testid="map-popup">{event.venue_name}</div>
              </div>
            </div>
          </div>
        ) : (
          <div>No event</div>
        )}
      </div>
    ),
  }
})

vi.mock("@/components/events/EventDetailTabs", () => {
  return {
    __esModule: true,
    default: ({ filteredCircles }: { filteredCircles: any[] }) => (
      <div data-testid="event-detail-tabs">
        <div>参与社团 ({filteredCircles.length})</div>
        <div data-testid="circles-grid">
          {filteredCircles.map((circle: any, index: number) => (
            <div key={index} data-testid={`circle-${index}`}>
              {circle.circle_name}
            </div>
          ))}
        </div>
      </div>
    ),
  }
})

vi.mock("@/components/events/EnhancedSkeleton", () => {
  return {
    EventHeaderSkeleton: () => <div data-testid="event-header-skeleton">Header Skeleton</div>,
  }
})

const sampleEvent = {
  id: "reitaisai-22",
  name: "Reitaisai 22",
  date: "May 3, 2025 (Sat) 10:30 – 15:30",
  date_sort: 20250503,
  venue_name: "Tokyo Big Sight",
  venue_address: "3-11-1 Ariake, Koto City, Tokyo",
  venue_lat: 35.6298,
  venue_lng: 139.793,
  url: "https://reitaisai.com/rts22/",
  image_url: "/images/events/reitaisai-22/thumb.jpg",
} as any

const circles = [
  {
    id: "c1",
    circle_name: "幻想郷楽団",
    booth_id: "あ01a",
    artist_name: "ZUN",
    circle_urls: "{}",
    category: "music",
  },
  {
    id: "c2",
    circle_name: "地霊殿ファクトリー",
    booth_id: "あ02b",
    artist_name: "Riko",
    circle_urls: "{}",
    category: "game",
  },
]

// Mock API responses
vi.mock("@/api/generated/ayafeedComponents", () => {
  return {
    __esModule: true,
    useGetEventsId: () => ({
      data: sampleEvent,
      isLoading: false,
      error: null
    }),
    useGetEventsIdCircles: () => ({
      data: circles,
      isLoading: false,
      error: null
    }),
    useGetAppearances: () => ({
      data: circles.map(c => ({ circle_id: c.id, booth_id: c.booth_id })),
      isLoading: false
    }),
  }
})

beforeEach(() => {
  server.resetHandlers()
  server.use(
    http.get("http://127.0.0.1:8787/events/evt1", (_req, res, ctx) => {
      return res(ctx.status(200), ctx.json(sampleEvent))
    }),
    http.get("http://127.0.0.1:8787/events/evt1/appearances", (_req, res, ctx) => {
      return res(ctx.status(200), ctx.json(circles))
    })
  )
})

afterEach(() => {
  vi.clearAllMocks()
})

test("renders enhanced event detail page with tabs", async () => {
  const { unmount } = renderWithProviders(<EventDetailPage />)

  // 等待所有异步操作完成
  const heading = await screen.findByRole("heading", { name: /Reitaisai 22/ })
  await screen.findByText("参与社团 (2)")

  // 验证增强版事件头部渲染结果
  const enhancedHeader = screen.getByTestId("enhanced-event-header")
  expect(enhancedHeader).toBeInTheDocument()
  expect(within(enhancedHeader).getByText("May 3, 2025 (Sat) 10:30 – 15:30 • Tokyo Big Sight")).toBeInTheDocument()
  expect(within(enhancedHeader).getByText("3-11-1 Ariake, Koto City, Tokyo")).toBeInTheDocument()
  expect(within(enhancedHeader).getByText("参展商: 2+")).toBeInTheDocument()

  // 验证标签页组件渲染结果
  const detailTabs = screen.getByTestId("event-detail-tabs")
  expect(detailTabs).toBeInTheDocument()

  // 验证社团列表渲染结果
  expect(screen.getByText("幻想郷楽団")).toBeInTheDocument()
  expect(screen.getByText("地霊殿ファクトリー")).toBeInTheDocument()

  // 验证地图组件是否正确渲染
  const mapContainer = await screen.findByTestId("map-container")
  expect(mapContainer).toBeInTheDocument()
  expect(within(mapContainer).getByTestId("tile-layer")).toBeInTheDocument()
  expect(within(mapContainer).getByTestId("map-marker")).toBeInTheDocument()
  const popup = within(mapContainer).getByTestId("map-popup")
  expect(popup).toBeInTheDocument()
  expect(popup).toHaveTextContent("Tokyo Big Sight")

  // 清理组件
  unmount()
})

test("renders skeleton when event is loading", async () => {
  // Mock loading state
  vi.doMock("@/api/generated/ayafeedComponents", () => {
    return {
      __esModule: true,
      useGetEventsId: () => ({
        data: null,
        isLoading: true,
        error: null
      }),
      useGetEventsIdCircles: () => ({
        data: [],
        isLoading: true,
        error: null
      }),
      useGetAppearances: () => ({
        data: [],
        isLoading: true
      }),
    }
  })

  const { unmount } = renderWithProviders(<EventDetailPage />)

  // 在加载状态下，应该显示增强的事件头部（我们的Mock组件仍然会渲染内容）
  expect(screen.getByTestId("enhanced-event-header")).toBeInTheDocument()

  // 清理组件
  unmount()
})