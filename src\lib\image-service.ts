/**
 * 图片服务配置
 * 统一处理图片URL的生成和管理
 */

interface ImageServiceConfig {
  cdnBaseUrl: string;
  fallbackBaseUrl: string;
  enableLocalFallback: boolean;
}

class ImageService {
  private config: ImageServiceConfig;

  constructor() {
    this.config = {
      cdnBaseUrl: process.env.NEXT_PUBLIC_CDN_URL || '',
      fallbackBaseUrl: process.env.NEXT_PUBLIC_API_URL || '',
      enableLocalFallback: process.env.NODE_ENV === 'development',
    };
  }

  /**
   * 将相对路径转换为完整的图片URL
   * @param relativePath 后端返回的相对路径，如 /images/events/reitaisai-22/thumb.jpg
   * @returns 完整的图片URL
   */
  getImageUrl(relativePath: string | null | undefined): string {
    if (!relativePath) {
      return this.getPlaceholderUrl();
    }

    // 如果已经是完整URL，直接返回
    if (relativePath.startsWith('http://') || relativePath.startsWith('https://')) {
      return relativePath;
    }

    // 确保路径以 / 开头
    const normalizedPath = relativePath.startsWith('/') ? relativePath : `/${relativePath}`;

    // 生产环境优先使用CDN
    if (this.config.cdnBaseUrl && process.env.NODE_ENV === 'production') {
      return `${this.config.cdnBaseUrl}${normalizedPath}`;
    }

    // 开发环境或CDN不可用时使用API服务器
    return `${this.config.fallbackBaseUrl}${normalizedPath}`;
  }

  /**
   * 获取占位图片URL
   */
  getPlaceholderUrl(type: 'event' | 'circle' | 'default' = 'default'): string {
    const placeholders = {
      event: '/images/placeholders/event.svg',
      circle: '/images/placeholders/circle.svg',
      default: '/globe.svg',
    };
    return placeholders[type];
  }

  /**
   * 生成不同尺寸的图片URL
   * @param relativePath 原始相对路径
   * @param variant 图片变体 (thumb, medium, large, original)
   */
  getImageVariant(relativePath: string, variant: 'thumb' | 'medium' | 'large' | 'original' = 'original'): string {
    if (!relativePath) return this.getPlaceholderUrl();

    if (variant === 'original') {
      return this.getImageUrl(relativePath);
    }

    // 在文件扩展名前插入变体标识
    const lastDotIndex = relativePath.lastIndexOf('.');
    if (lastDotIndex === -1) {
      return this.getImageUrl(`${relativePath}_${variant}`);
    }

    const basePath = relativePath.substring(0, lastDotIndex);
    const extension = relativePath.substring(lastDotIndex);
    const variantPath = `${basePath}_${variant}${extension}`;
    
    return this.getImageUrl(variantPath);
  }

  /**
   * 检查图片URL是否可访问
   */
  async checkImageAvailability(url: string): Promise<boolean> {
    try {
      const response = await fetch(url, { method: 'HEAD' });
      return response.ok;
    } catch {
      return false;
    }
  }
}

// 导出单例实例
export const imageService = new ImageService();

// 导出便捷函数
export const getImageUrl = (relativePath: string | null | undefined) => 
  imageService.getImageUrl(relativePath);

export const getImageVariant = (relativePath: string, variant: 'thumb' | 'medium' | 'large' | 'original' = 'original') =>
  imageService.getImageVariant(relativePath, variant);

export const getPlaceholderUrl = (type: 'event' | 'circle' | 'default' = 'default') =>
  imageService.getPlaceholderUrl(type);
