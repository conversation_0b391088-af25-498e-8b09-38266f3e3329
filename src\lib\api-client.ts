/**
 * 多语言 API 客户端
 * 
 * 统一处理语言相关的 HTTP 请求，实现最佳实践
 */

import type { Locale } from './locale-utils';

/**
 * API 请求配置
 */
interface ApiRequestConfig extends RequestInit {
  locale?: Locale;
  params?: Record<string, string | number | boolean>;
}

/**
 * API 响应类型
 */
interface ApiResponse<T = any> {
  data: T;
  message?: string;
  locale?: Locale;
  timestamp?: string;
}

/**
 * 多语言 API 客户端类
 */
export class LocaleAwareApiClient {
  private baseUrl: string;
  private defaultLocale: Locale;

  constructor(baseUrl: string = '/api', defaultLocale: Locale = 'zh') {
    this.baseUrl = baseUrl;
    this.defaultLocale = defaultLocale;
  }

  /**
   * 构建请求 URL
   */
  private buildUrl(endpoint: string, params?: Record<string, string | number | boolean>): string {
    const url = new URL(endpoint, window.location.origin + this.baseUrl);
    
    if (params) {
      Object.entries(params).forEach(([key, value]) => {
        url.searchParams.set(key, String(value));
      });
    }
    
    return url.toString();
  }

  /**
   * 构建请求头
   */
  private buildHeaders(locale: Locale, customHeaders?: HeadersInit): Headers {
    const headers = new Headers(customHeaders);
    
    // 设置标准的语言相关头部
    headers.set('Accept-Language', locale);
    headers.set('Content-Language', locale);
    headers.set('X-Locale', locale); // 自定义头部，明确指定语言
    
    // 设置默认的 Content-Type
    if (!headers.has('Content-Type')) {
      headers.set('Content-Type', 'application/json');
    }
    
    return headers;
  }

  /**
   * 通用请求方法
   */
  private async request<T>(
    endpoint: string, 
    config: ApiRequestConfig = {}
  ): Promise<ApiResponse<T>> {
    const { locale = this.defaultLocale, params, ...fetchConfig } = config;
    
    const url = this.buildUrl(endpoint, params);
    const headers = this.buildHeaders(locale, fetchConfig.headers);
    
    console.log(`🌐 API 请求: ${fetchConfig.method || 'GET'} ${url} [${locale}]`);
    
    try {
      const response = await fetch(url, {
        ...fetchConfig,
        headers,
      });
      
      if (!response.ok) {
        throw new Error(`API 请求失败: ${response.status} ${response.statusText}`);
      }
      
      const data = await response.json();
      
      return {
        data,
        locale,
        timestamp: new Date().toISOString(),
      };
    } catch (error) {
      console.error(`❌ API 请求错误: ${url}`, error);
      throw error;
    }
  }

  /**
   * GET 请求
   */
  async get<T>(endpoint: string, config: Omit<ApiRequestConfig, 'method' | 'body'> = {}) {
    return this.request<T>(endpoint, { ...config, method: 'GET' });
  }

  /**
   * POST 请求
   */
  async post<T>(endpoint: string, data?: any, config: Omit<ApiRequestConfig, 'method'> = {}) {
    return this.request<T>(endpoint, {
      ...config,
      method: 'POST',
      body: data ? JSON.stringify(data) : undefined,
    });
  }

  /**
   * PUT 请求
   */
  async put<T>(endpoint: string, data?: any, config: Omit<ApiRequestConfig, 'method'> = {}) {
    return this.request<T>(endpoint, {
      ...config,
      method: 'PUT',
      body: data ? JSON.stringify(data) : undefined,
    });
  }

  /**
   * DELETE 请求
   */
  async delete<T>(endpoint: string, config: Omit<ApiRequestConfig, 'method' | 'body'> = {}) {
    return this.request<T>(endpoint, { ...config, method: 'DELETE' });
  }
}

/**
 * 默认 API 客户端实例
 */
export const apiClient = new LocaleAwareApiClient();

/**
 * 语言感知的 API 调用工具函数
 */
export const localeApi = {
  /**
   * 获取事件列表
   */
  getEvents: (locale: Locale, params?: { page?: number; limit?: number; category?: string }) =>
    apiClient.get('/events', { locale, params }),

  /**
   * 获取社团列表
   */
  getCircles: (locale: Locale, params?: { page?: number; limit?: number; tags?: string[] }) =>
    apiClient.get('/circles', { 
      locale, 
      params: params ? {
        ...params,
        tags: params.tags?.join(',')
      } : undefined
    }),

  /**
   * 搜索
   */
  search: (locale: Locale, query: string, type?: 'events' | 'circles' | 'all') =>
    apiClient.get('/search', { 
      locale, 
      params: { q: query, ...(type && { type }) }
    }),

  /**
   * 获取事件详情
   */
  getEvent: (locale: Locale, id: string) =>
    apiClient.get(`/events/${id}`, { locale }),

  /**
   * 获取社团详情
   */
  getCircle: (locale: Locale, id: string) =>
    apiClient.get(`/circles/${id}`, { locale }),

  /**
   * 获取 Feed 流
   */
  getFeed: (locale: Locale, params?: { page?: number; limit?: number }) =>
    apiClient.get('/feed', { locale, params }),
};

/**
 * 语言无关的 API 调用
 */
export const globalApi = {
  /**
   * 获取用户信息
   */
  getUser: () => apiClient.get('/user'),

  /**
   * 获取系统配置
   */
  getSystemConfig: () => apiClient.get('/system/config'),

  /**
   * 用户认证
   */
  login: (credentials: { username: string; password: string }) =>
    apiClient.post('/auth/login', credentials),

  /**
   * 用户注册
   */
  register: (userData: { username: string; email: string; password: string }) =>
    apiClient.post('/auth/register', userData),

  /**
   * 退出登录
   */
  logout: () => apiClient.post('/auth/logout'),
};

/**
 * API 错误处理工具
 */
export class ApiError extends Error {
  constructor(
    message: string,
    public status?: number,
    public locale?: Locale,
    public endpoint?: string
  ) {
    super(message);
    this.name = 'ApiError';
  }
}

/**
 * 创建带有默认语言的 API 客户端
 */
export function createLocaleApiClient(defaultLocale: Locale) {
  return new LocaleAwareApiClient('/api', defaultLocale);
}

/**
 * 开发环境下的 API 调试工具
 */
export function debugApiCalls() {
  if (process.env.NODE_ENV !== 'development') return;

  // 拦截 fetch 请求，记录语言相关信息
  const originalFetch = window.fetch;
  
  window.fetch = async (input, init) => {
    const url = typeof input === 'string' ? input : input.url;
    const headers = new Headers(init?.headers);
    const locale = headers.get('X-Locale') || headers.get('Accept-Language');
    
    console.group(`🔍 API 调试: ${init?.method || 'GET'} ${url}`);
    console.log('语言:', locale);
    console.log('请求头:', Object.fromEntries(headers.entries()));
    console.groupEnd();
    
    return originalFetch(input, init);
  };
  
  console.log('🔧 API 调试模式已启用');
}

/**
 * 类型定义导出
 */
export type { ApiRequestConfig, ApiResponse, Locale };
