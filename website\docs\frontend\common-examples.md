# 常用示例代码

> 📝 **目标**: 提供复制粘贴即用的代码示例，覆盖常见的业务场景

## 🎯 基础API调用

### 1. 获取数据列表
```typescript
// src/services/events.ts
import { api } from '@/lib/api';

// 获取事件列表
export async function getEvents(params?: {
  page?: string;
  pageSize?: string;
  locale?: string;
}) {
  const { data } = await api.GET('/events', {
    params: { query: params },
    headers: {
      'X-Locale': params?.locale || 'zh'
    }
  });
  
  return {
    items: data.items,
    total: data.total,
    page: data.page,
    pageSize: data.pageSize,
  };
}

// React Hook封装
import { useState, useEffect } from 'react';

export function useEvents(params?: Parameters<typeof getEvents>[0]) {
  const [data, setData] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  
  useEffect(() => {
    let cancelled = false;
    
    async function fetchData() {
      try {
        setLoading(true);
        setError(null);
        const result = await getEvents(params);
        
        if (!cancelled) {
          setData(result);
        }
      } catch (err) {
        if (!cancelled) {
          setError(err);
        }
      } finally {
        if (!cancelled) {
          setLoading(false);
        }
      }
    }
    
    fetchData();
    
    return () => {
      cancelled = true;
    };
  }, [JSON.stringify(params)]);
  
  return { data, loading, error, refetch: () => fetchData() };
}
```

### 2. 搜索功能
```typescript
// src/services/search.ts
import { api } from '@/lib/api';

export interface SearchParams {
  q: string;
  type?: 'all' | 'events' | 'circles';
  page?: string;
  limit?: string;
}

export async function searchContent(params: SearchParams) {
  const { data } = await api.GET('/search', {
    params: { query: params }
  });
  
  return data;
}

// React组件示例
import React, { useState, useCallback } from 'react';
import { useDebounce } from '@/hooks/useDebounce';

export function SearchBox() {
  const [query, setQuery] = useState('');
  const [results, setResults] = useState([]);
  const [loading, setLoading] = useState(false);
  
  const debouncedQuery = useDebounce(query, 300);
  
  const handleSearch = useCallback(async (searchQuery: string) => {
    if (!searchQuery.trim()) {
      setResults([]);
      return;
    }
    
    setLoading(true);
    try {
      const data = await searchContent({ q: searchQuery });
      setResults(data);
    } catch (error) {
      console.error('Search failed:', error);
      setResults([]);
    } finally {
      setLoading(false);
    }
  }, []);
  
  useEffect(() => {
    handleSearch(debouncedQuery);
  }, [debouncedQuery, handleSearch]);
  
  return (
    <div className="relative">
      <input
        type="text"
        value={query}
        onChange={(e) => setQuery(e.target.value)}
        placeholder="搜索事件或社团..."
        className="w-full px-4 py-2 border rounded-lg"
      />
      
      {loading && (
        <div className="absolute right-3 top-3">
          <div className="animate-spin h-4 w-4 border-2 border-blue-500 border-t-transparent rounded-full"></div>
        </div>
      )}
      
      {results.length > 0 && (
        <div className="absolute top-full left-0 right-0 bg-white border rounded-lg shadow-lg mt-1 max-h-60 overflow-y-auto">
          {results.map((item, index) => (
            <div key={index} className="px-4 py-2 hover:bg-gray-50 cursor-pointer">
              <div className="font-medium">{item.name}</div>
              <div className="text-sm text-gray-500">{item.type}</div>
            </div>
          ))}
        </div>
      )}
    </div>
  );
}
```

### 3. 分页组件
```typescript
// src/components/Pagination.tsx
import React from 'react';

interface PaginationProps {
  currentPage: number;
  totalPages: number;
  onPageChange: (page: number) => void;
  showInfo?: boolean;
  totalItems?: number;
  pageSize?: number;
}

export function Pagination({
  currentPage,
  totalPages,
  onPageChange,
  showInfo = true,
  totalItems,
  pageSize,
}: PaginationProps) {
  const getVisiblePages = () => {
    const delta = 2;
    const range = [];
    const rangeWithDots = [];
    
    for (let i = Math.max(2, currentPage - delta); 
         i <= Math.min(totalPages - 1, currentPage + delta); 
         i++) {
      range.push(i);
    }
    
    if (currentPage - delta > 2) {
      rangeWithDots.push(1, '...');
    } else {
      rangeWithDots.push(1);
    }
    
    rangeWithDots.push(...range);
    
    if (currentPage + delta < totalPages - 1) {
      rangeWithDots.push('...', totalPages);
    } else {
      rangeWithDots.push(totalPages);
    }
    
    return rangeWithDots;
  };
  
  if (totalPages <= 1) return null;
  
  return (
    <div className="flex items-center justify-between">
      {showInfo && totalItems && pageSize && (
        <div className="text-sm text-gray-700">
          显示 {Math.min((currentPage - 1) * pageSize + 1, totalItems)} 到{' '}
          {Math.min(currentPage * pageSize, totalItems)} 项，共 {totalItems} 项
        </div>
      )}
      
      <div className="flex items-center space-x-1">
        <button
          onClick={() => onPageChange(currentPage - 1)}
          disabled={currentPage <= 1}
          className="px-3 py-2 text-sm border rounded disabled:opacity-50 disabled:cursor-not-allowed hover:bg-gray-50"
        >
          上一页
        </button>
        
        {getVisiblePages().map((page, index) => (
          <React.Fragment key={index}>
            {page === '...' ? (
              <span className="px-3 py-2 text-sm">...</span>
            ) : (
              <button
                onClick={() => onPageChange(page as number)}
                className={`px-3 py-2 text-sm border rounded ${
                  currentPage === page
                    ? 'bg-blue-500 text-white border-blue-500'
                    : 'hover:bg-gray-50'
                }`}
              >
                {page}
              </button>
            )}
          </React.Fragment>
        ))}
        
        <button
          onClick={() => onPageChange(currentPage + 1)}
          disabled={currentPage >= totalPages}
          className="px-3 py-2 text-sm border rounded disabled:opacity-50 disabled:cursor-not-allowed hover:bg-gray-50"
        >
          下一页
        </button>
      </div>
    </div>
  );
}

// 使用示例
function EventList() {
  const [currentPage, setCurrentPage] = useState(1);
  const pageSize = 10;
  
  const { data, loading } = useEvents({
    page: currentPage.toString(),
    pageSize: pageSize.toString(),
  });
  
  if (loading) return <div>加载中...</div>;
  if (!data) return <div>暂无数据</div>;
  
  const totalPages = Math.ceil(data.total / pageSize);
  
  return (
    <div>
      <div className="grid gap-4 mb-6">
        {data.items.map(event => (
          <EventCard key={event.id} event={event} />
        ))}
      </div>
      
      <Pagination
        currentPage={currentPage}
        totalPages={totalPages}
        onPageChange={setCurrentPage}
        totalItems={data.total}
        pageSize={pageSize}
      />
    </div>
  );
}
```

## 🔐 认证相关示例

### 1. 登录表单
```typescript
// src/components/LoginForm.tsx
import React, { useState } from 'react';
import { useAuthStore } from '@/stores/auth';
import { useRouter } from 'next/router';

export function LoginForm() {
  const [credentials, setCredentials] = useState({
    username: '',
    password: '',
  });
  const [showPassword, setShowPassword] = useState(false);
  const { login, isLoading } = useAuthStore();
  const router = useRouter();
  
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    try {
      await login(credentials);
      
      // 登录成功后跳转
      const redirectTo = router.query.redirect as string || '/';
      router.push(redirectTo);
    } catch (error) {
      // 错误已经在store中处理
    }
  };
  
  return (
    <div className="max-w-md mx-auto bg-white rounded-lg shadow-md p-6">
      <h2 className="text-2xl font-bold text-center mb-6">登录</h2>
      
      <form onSubmit={handleSubmit} className="space-y-4">
        <div>
          <label htmlFor="username" className="block text-sm font-medium text-gray-700 mb-1">
            用户名
          </label>
          <input
            id="username"
            type="text"
            value={credentials.username}
            onChange={(e) => setCredentials(prev => ({
              ...prev,
              username: e.target.value
            }))}
            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            required
          />
        </div>
        
        <div>
          <label htmlFor="password" className="block text-sm font-medium text-gray-700 mb-1">
            密码
          </label>
          <div className="relative">
            <input
              id="password"
              type={showPassword ? 'text' : 'password'}
              value={credentials.password}
              onChange={(e) => setCredentials(prev => ({
                ...prev,
                password: e.target.value
              }))}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              required
            />
            <button
              type="button"
              onClick={() => setShowPassword(!showPassword)}
              className="absolute right-3 top-2.5 text-gray-400 hover:text-gray-600"
            >
              {showPassword ? '🙈' : '👁️'}
            </button>
          </div>
        </div>
        
        <button
          type="submit"
          disabled={isLoading}
          className="w-full bg-blue-500 text-white py-2 px-4 rounded-md hover:bg-blue-600 disabled:opacity-50 disabled:cursor-not-allowed"
        >
          {isLoading ? '登录中...' : '登录'}
        </button>
      </form>
      
      <div className="mt-4 text-center">
        <a href="/register" className="text-blue-500 hover:text-blue-600 text-sm">
          还没有账号？立即注册
        </a>
      </div>
    </div>
  );
}
```

### 2. 用户信息显示
```typescript
// src/components/UserProfile.tsx
import React from 'react';
import { useAuthStore } from '@/stores/auth';
import { usePermissions } from '@/hooks/usePermissions';

export function UserProfile() {
  const { user, logout } = useAuthStore();
  const permissions = usePermissions();
  
  if (!user) return null;
  
  const getRoleBadgeColor = (role: string) => {
    const colors = {
      admin: 'bg-red-100 text-red-800',
      editor: 'bg-blue-100 text-blue-800',
      viewer: 'bg-green-100 text-green-800',
      user: 'bg-gray-100 text-gray-800',
    };
    return colors[role as keyof typeof colors] || colors.user;
  };
  
  return (
    <div className="bg-white rounded-lg shadow-md p-6">
      <div className="flex items-center space-x-4 mb-4">
        <div className="w-16 h-16 bg-gray-300 rounded-full flex items-center justify-center">
          <span className="text-2xl font-bold text-gray-600">
            {user.username.charAt(0).toUpperCase()}
          </span>
        </div>
        
        <div>
          <h3 className="text-xl font-semibold">{user.username}</h3>
          <p className="text-gray-600">{user.email}</p>
          <span className={`inline-block px-2 py-1 rounded-full text-xs font-medium ${getRoleBadgeColor(user.role)}`}>
            {user.role}
          </span>
        </div>
      </div>
      
      <div className="border-t pt-4">
        <h4 className="font-medium mb-2">权限</h4>
        <div className="grid grid-cols-2 gap-2 text-sm">
          <div className="flex items-center">
            <span className={permissions.canManageEvents ? '✅' : '❌'}></span>
            <span className="ml-2">管理事件</span>
          </div>
          <div className="flex items-center">
            <span className={permissions.canManageCircles ? '✅' : '❌'}></span>
            <span className="ml-2">管理社团</span>
          </div>
          <div className="flex items-center">
            <span className={permissions.canViewStats ? '✅' : '❌'}></span>
            <span className="ml-2">查看统计</span>
          </div>
          <div className="flex items-center">
            <span className={permissions.canBookmark ? '✅' : '❌'}></span>
            <span className="ml-2">收藏功能</span>
          </div>
        </div>
      </div>
      
      <div className="border-t pt-4 mt-4">
        <button
          onClick={logout}
          className="w-full bg-red-500 text-white py-2 px-4 rounded-md hover:bg-red-600"
        >
          退出登录
        </button>
      </div>
    </div>
  );
}
```

## 🌐 多语言示例

### 1. 语言切换器
```typescript
// src/components/LanguageSwitcher.tsx
import React, { useState } from 'react';
import { useI18nStore } from '@/stores/i18n';
import { SUPPORTED_LOCALES } from '@/services/i18n';

export function LanguageSwitcher() {
  const { locale, setLocale } = useI18nStore();
  const [isOpen, setIsOpen] = useState(false);
  
  const currentLocale = SUPPORTED_LOCALES.find(l => l.code === locale);
  
  return (
    <div className="relative">
      <button
        onClick={() => setIsOpen(!isOpen)}
        className="flex items-center space-x-2 px-3 py-2 border rounded-md hover:bg-gray-50"
      >
        <span>{currentLocale?.flag}</span>
        <span>{currentLocale?.nativeName}</span>
        <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
        </svg>
      </button>
      
      {isOpen && (
        <div className="absolute top-full left-0 mt-1 bg-white border rounded-md shadow-lg z-10 min-w-full">
          {SUPPORTED_LOCALES.map((localeConfig) => (
            <button
              key={localeConfig.code}
              onClick={() => {
                setLocale(localeConfig.code);
                setIsOpen(false);
              }}
              className={`w-full text-left px-4 py-2 hover:bg-gray-50 flex items-center space-x-2 ${
                locale === localeConfig.code ? 'bg-blue-50 text-blue-600' : ''
              }`}
            >
              <span>{localeConfig.flag}</span>
              <span>{localeConfig.nativeName}</span>
            </button>
          ))}
        </div>
      )}
    </div>
  );
}
```

### 2. 多语言内容显示
```typescript
// src/components/MultilingualContent.tsx
import React from 'react';
import { useLocalization } from '@/hooks/useLocalization';

interface MultilingualContentProps {
  content: string | {
    zh?: string;
    ja?: string;
    en?: string;
  };
  fallback?: string;
  className?: string;
}

export function MultilingualContent({ 
  content, 
  fallback = '', 
  className = '' 
}: MultilingualContentProps) {
  const { getLocalizedText } = useLocalization();
  
  const text = typeof content === 'string' 
    ? content 
    : getLocalizedText(content) || fallback;
  
  if (!text) return null;
  
  return <span className={className}>{text}</span>;
}

// 使用示例
function EventCard({ event }) {
  return (
    <div className="bg-white rounded-lg shadow-md p-4">
      <h3 className="text-lg font-semibold mb-2">
        <MultilingualContent content={event.name} />
      </h3>
      <p className="text-gray-600">
        <MultilingualContent content={event.description} />
      </p>
    </div>
  );
}
```

## 🎨 UI组件示例

### 1. 加载状态
```typescript
// src/components/LoadingSpinner.tsx
import React from 'react';

interface LoadingSpinnerProps {
  size?: 'sm' | 'md' | 'lg';
  className?: string;
}

export function LoadingSpinner({ size = 'md', className = '' }: LoadingSpinnerProps) {
  const sizeClasses = {
    sm: 'h-4 w-4',
    md: 'h-8 w-8',
    lg: 'h-12 w-12',
  };
  
  return (
    <div className={`animate-spin border-2 border-blue-500 border-t-transparent rounded-full ${sizeClasses[size]} ${className}`}></div>
  );
}

// 页面级加载
export function PageLoading() {
  return (
    <div className="min-h-screen flex items-center justify-center">
      <div className="text-center">
        <LoadingSpinner size="lg" />
        <p className="mt-4 text-gray-600">加载中...</p>
      </div>
    </div>
  );
}

// 按钮加载状态
export function LoadingButton({ 
  loading, 
  children, 
  ...props 
}: { loading: boolean; children: React.ReactNode } & React.ButtonHTMLAttributes<HTMLButtonElement>) {
  return (
    <button
      {...props}
      disabled={loading || props.disabled}
      className={`flex items-center justify-center space-x-2 ${props.className || ''}`}
    >
      {loading && <LoadingSpinner size="sm" />}
      <span>{children}</span>
    </button>
  );
}
```

### 2. 空状态组件
```typescript
// src/components/EmptyState.tsx
import React from 'react';

interface EmptyStateProps {
  icon?: React.ReactNode;
  title: string;
  description?: string;
  action?: React.ReactNode;
}

export function EmptyState({ icon, title, description, action }: EmptyStateProps) {
  return (
    <div className="text-center py-12">
      {icon && (
        <div className="mx-auto h-12 w-12 text-gray-400 mb-4">
          {icon}
        </div>
      )}
      <h3 className="text-lg font-medium text-gray-900 mb-2">{title}</h3>
      {description && (
        <p className="text-gray-500 mb-4">{description}</p>
      )}
      {action}
    </div>
  );
}

// 使用示例
function EventList() {
  const { data, loading } = useEvents();
  
  if (loading) return <PageLoading />;
  
  if (!data?.items.length) {
    return (
      <EmptyState
        icon={
          <svg fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z" />
          </svg>
        }
        title="暂无事件"
        description="还没有任何事件，创建第一个事件吧！"
        action={
          <button className="bg-blue-500 text-white px-4 py-2 rounded-md hover:bg-blue-600">
            创建事件
          </button>
        }
      />
    );
  }
  
  return (
    <div className="grid gap-4">
      {data.items.map(event => (
        <EventCard key={event.id} event={event} />
      ))}
    </div>
  );
}
```

## 🔧 实用工具Hook

### 1. 防抖Hook
```typescript
// src/hooks/useDebounce.ts
import { useState, useEffect } from 'react';

export function useDebounce<T>(value: T, delay: number): T {
  const [debouncedValue, setDebouncedValue] = useState<T>(value);
  
  useEffect(() => {
    const handler = setTimeout(() => {
      setDebouncedValue(value);
    }, delay);
    
    return () => {
      clearTimeout(handler);
    };
  }, [value, delay]);
  
  return debouncedValue;
}
```

### 2. 本地存储Hook
```typescript
// src/hooks/useLocalStorage.ts
import { useState, useEffect } from 'react';

export function useLocalStorage<T>(
  key: string,
  initialValue: T
): [T, (value: T | ((val: T) => T)) => void] {
  const [storedValue, setStoredValue] = useState<T>(() => {
    try {
      const item = window.localStorage.getItem(key);
      return item ? JSON.parse(item) : initialValue;
    } catch (error) {
      console.error(`Error reading localStorage key "${key}":`, error);
      return initialValue;
    }
  });
  
  const setValue = (value: T | ((val: T) => T)) => {
    try {
      const valueToStore = value instanceof Function ? value(storedValue) : value;
      setStoredValue(valueToStore);
      window.localStorage.setItem(key, JSON.stringify(valueToStore));
    } catch (error) {
      console.error(`Error setting localStorage key "${key}":`, error);
    }
  };
  
  return [storedValue, setValue];
}
```

---

**下一步**: 查看 [业务流程指南](./business-flows.md) 了解核心业务逻辑 🔄
