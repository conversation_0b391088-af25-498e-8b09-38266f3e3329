"use client";

import { useParams, useRouter } from "next/navigation";
import { useEffect, useState } from "react";

import { Button } from "@/components/ui/button";
import { useZodForm } from "@/hooks";
import { useAdminCircleDetail } from "@/hooks/admin/useAdminCircleDetail";
import { useUpdateCircle } from "@/hooks/admin/useUpdateCircle";
import { CircleInputSchema, CircleInput } from "@/schemas/circle";

export default function EditCirclePage() {
  const router = useRouter();
  const { id } = useParams<{ id: string }>();
  const { data: detail, isLoading: detailLoading } = useAdminCircleDetail(id);
  const updateCircle = useUpdateCircle(id);
  const [error, setError] = useState<string | null>(null);

  const {
    register,
    handleSubmit,
    formState: { errors },
    reset,
  } = useZodForm(CircleInputSchema, {
    defaultValues: {
      name: "",
      author: "",
      category: "",
      twitter: "",
      pixiv: "",
      web: "",
    },
    mode: "onBlur",
  });

  // 当接口返回详情后重置表单
  useEffect(() => {
    if (detail) {
      let twitter = "";
      let pixiv = "";
      let web = "";
      if (detail.urls) {
        try {
          const obj = JSON.parse(detail.urls);
          twitter = obj.twitter ?? "";
          pixiv = obj.pixiv ?? "";
          web = obj.web ?? "";
        } catch {
          // 无效 JSON，保持默认空字符串
          twitter = ""
          pixiv = ""
          web = ""
        }
      }

      reset({
        name: detail.name,
        author: detail.author ?? "",
        category: detail.category ?? "",
        twitter,
        pixiv,
        web,
      });
    }
  }, [detail, reset]);

  function onSubmit(values: CircleInput) {
    const { twitter, pixiv, web, category, ...rest } = values;
    const payload = {
      ...rest,
      urls: JSON.stringify({ twitter, pixiv, web }),
      category,
    };

    updateCircle.mutate(payload, {
      onSuccess: () => {
        // 更新成功后跳转至前台详情页，便于立即查看结果
        router.push(`/circles/${id}`);
      },
      onError: (err: any) => { setError(err?.message ?? "更新失败"); },
    });
  }

  if (detailLoading) return <p>加载中...</p>;

  return (
    <div className="max-w-xl space-y-4">
      <h1 className="text-2xl font-bold">编辑社团</h1>
      {error && <p className="text-destructive text-sm">{error}</p>}
      <form onSubmit={handleSubmit(onSubmit)} className="space-y-4">
        <div>
          <label htmlFor="circle-name" className="block text-sm mb-1">名称 *</label>
          <input
            id="circle-name"
            type="text"
            {...register("name")}
            className="w-full border rounded px-3 py-2"
          />
          {errors.name && (
            <p className="text-destructive text-xs mt-1">{errors.name.message}</p>
          )}
        </div>

        {/* 社交链接 */}
        <div className="grid grid-cols-2 gap-4">
          <div className="col-span-2">
            <label className="block text-sm mb-1">Twitter</label>
            <input
              type="text"
              {...register("twitter")}
              className="w-full border rounded px-3 py-2"
            />
          </div>
          <div className="col-span-2">
            <label className="block text-sm mb-1">Pixiv</label>
            <input
              type="text"
              {...register("pixiv")}
              className="w-full border rounded px-3 py-2"
            />
          </div>
          <div className="col-span-2">
            <label className="block text-sm mb-1">Website</label>
            <input
              type="text"
              {...register("web")}
              className="w-full border rounded px-3 py-2"
            />
          </div>
        </div>

        <div>
          <label className="block text-sm mb-1">分类 *</label>
          <select
            {...register("category")}
            className="w-full border rounded px-3 py-2"
          >
            <option value="">— 请选择 —</option>
            <option value="comic">Comic</option>
            <option value="music">Music</option>
            <option value="game">Game</option>
            <option value="other">Other</option>
          </select>
          {errors.category && (
            <p className="text-destructive text-xs mt-1">{errors.category.message}</p>
          )}
        </div>

        <div>
          <label className="block text-sm mb-1">作者</label>
          <input
            type="text"
            {...register("author")}
            className="w-full border rounded px-3 py-2"
          />
        </div>

        <Button type="submit" disabled={updateCircle.isPending}>
          {updateCircle.isPending ? "保存中..." : "保存"}
        </Button>
      </form>
    </div>
  );
} 