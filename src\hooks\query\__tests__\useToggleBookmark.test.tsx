// @ts-nocheck
import { QueryClient, QueryClientProvider } from "@tanstack/react-query"
import { renderHook, act } from "@testing-library/react"
import React from "react"
import { expect, test } from "vitest"

import { useToggleBookmark } from "@/hooks/query/useToggleBookmark"

const circleId = "c1"

let state = false
// mock 生成的 fetcher，保持与实际 Hook 调用签名一致
vi.mock("@/api/generated/ayafeedComponents", () => ({
  fetchPostCirclesCircleIdBookmark: async () => {
    state = !state
    return { message: "OK", data: { isBookmarked: state } }
  },
}))

function wrapper({ children }) {
  const client = new QueryClient({ defaultOptions: { queries: { retry: false } } })
  return React.createElement(QueryClientProvider, { client }, children)
}

test("toggleBookmark toggles state", async () => {
  const { result } = renderHook(() => useToggleBookmark(), { wrapper })

  let res
  await act(async () => {
    res = await result.current.mutateAsync(circleId)
  })
  expect(res.isBookmarked).toBe(true)

  await act(async () => {
    res = await result.current.mutateAsync(circleId)
  })
  expect(res.isBookmarked).toBe(false)
}) 