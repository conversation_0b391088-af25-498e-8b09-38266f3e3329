import { describe, it, expect } from 'vitest';

import app from '@/index';

// @ts-ignore
const Request = globalThis.Request;

// Mock log data
const mockLogs = Array.from({ length: 15 }, (_, i) => ({
  id: `log${i + 1}`,
  user_id: 'u1',
  username: 'tester',
  action: 'CREATE_CIRCLE',
  target_type: 'circle',
  target_id: `c${i + 1}`,
  meta: null,
  created_at: new Date().toISOString(),
}));

function createMockDB(role?: 'admin' | 'viewer') {
  return {
    prepare: (query: string) => {
      const upper = query.toUpperCase();

      const buildResponse = () => ({
        all: async () => {
          if (upper.includes('FROM LOGS')) {
            // 返回前 10 条记录，用于 pageSize=10 的场景
            return { results: mockLogs.slice(0, 10) };
          }
          if (upper.includes('FROM AUTH_SESSION')) {
            return {
              results: role ? [{ id: 'u1', username: 'tester', role }] : [],
            };
          }
          return { results: [] };
        },
        first: async () => {
          if (upper.includes('COUNT') && upper.includes('FROM LOGS')) {
            return { total: mockLogs.length };
          }
          return null;
        },
        run: async () => ({ success: true }),
      });

      return {
        ...buildResponse(),
        bind: (..._args: any[]) => buildResponse(),
      };
    },
  };
}

function fetchWithEnv(url: string, env: any, headers?: HeadersInit) {
  const base = url.startsWith('http') ? url : `http://localhost${url}`;
  const req = new Request(base, { headers });
  return app.fetch(req, env);
}

describe('Audit logs API', () => {
  it('should reject non-admin access', async () => {
    const res = await fetchWithEnv(
      '/admin/logs',
      { DB: createMockDB('viewer') },
      {
        Cookie: 'auth_session=viewer_session',
      }
    );
    expect(res.status).toBe(403);
  });

  it('should return paginated logs for admin', async () => {
    const res = await fetchWithEnv(
      '/admin/logs?page=1&pageSize=10',
      { DB: createMockDB('admin') },
      {
        Cookie: 'auth_session=admin_session',
      }
    );
    expect(res.status).toBe(200);
    const data = (await res.json()) as any;
    expect(Array.isArray(data.items)).toBe(true);
    expect(data.items.length).toBe(10);
    expect(data.total).toBe(mockLogs.length);
  });
});
