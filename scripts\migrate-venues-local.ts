#!/usr/bin/env tsx
/**
 * 本地开发环境venues表迁移脚本
 *
 * 用法：
 *   pnpm tsx scripts/migrate-venues-local.ts
 *
 * 功能：
 *   1. 使用wrangler d1执行本地迁移
 *   2. 验证迁移结果
 */

import { execSync } from 'child_process';
import { readFileSync, existsSync } from 'fs';
import path from 'path';

const MIGRATION_FILE = path.resolve(
  __dirname,
  '../db/migrations/004_create_venues_table.sql'
);
const DB_NAME = 'ayafeed-db'; // 从wrangler.toml中获取

/**
 * 执行shell命令
 */
function execCommand(command: string): string {
  try {
    return execSync(command, { encoding: 'utf-8', stdio: 'pipe' });
  } catch (error: any) {
    console.error(`命令执行失败: ${command}`);
    console.error(error.message);
    throw error;
  }
}

/**
 * 执行D1查询
 */
function queryD1(sql: string): any {
  const command = `wrangler d1 execute ${DB_NAME} --local --command="${sql}"`;
  const output = execCommand(command);
  return output;
}

/**
 * 检查表是否存在
 */
function tableExists(tableName: string): boolean {
  try {
    const sql = `SELECT name FROM sqlite_master WHERE type='table' AND name='${tableName}'`;
    const output = queryD1(sql);
    return output.includes(tableName);
  } catch (_error) {
    return false;
  }
}

/**
 * 获取表的行数
 */
function getTableCount(tableName: string): number {
  try {
    const sql = `SELECT COUNT(*) as count FROM ${tableName}`;
    const output = queryD1(sql);
    // 解析输出中的数字
    const match = output.match(/\d+/);
    return match ? parseInt(match[0]) : 0;
  } catch (error) {
    console.error(`获取表 ${tableName} 行数时出错:`, error);
    return 0;
  }
}

/**
 * 主迁移函数
 */
async function runMigration() {
  console.log('🚀 开始执行本地venues表迁移');

  // 1. 检查wrangler是否可用
  try {
    execCommand('wrangler --version');
  } catch (_error) {
    console.error(
      '❌ wrangler未安装或不可用，请先安装: npm install -g wrangler'
    );
    process.exit(1);
  }

  // 2. 检查迁移文件
  if (!existsSync(MIGRATION_FILE)) {
    console.error(`❌ 迁移文件不存在: ${MIGRATION_FILE}`);
    process.exit(1);
  }

  // 3. 检查当前状态
  console.log('\n📊 检查当前数据库状态...');
  const venuesExists = tableExists('venues');
  const eventsCount = getTableCount('events');

  console.log(`   - venues表存在: ${venuesExists ? '✅' : '❌'}`);
  console.log(`   - events表记录数: ${eventsCount}`);

  if (venuesExists) {
    console.log('⚠️  venues表已存在，跳过迁移');
    return;
  }

  // 4. 读取并执行迁移脚本
  console.log('\n🔄 执行迁移脚本...');

  try {
    // 读取迁移文件内容（用于验证文件存在）
    readFileSync(MIGRATION_FILE, 'utf-8');

    // 使用wrangler执行迁移文件
    const command = `wrangler d1 execute ${DB_NAME} --local --file="${MIGRATION_FILE}"`;
    console.log(`执行命令: ${command}`);

    const output = execCommand(command);
    console.log('迁移输出:', output);

    console.log('✅ 迁移脚本执行完成');
  } catch (error) {
    console.error('❌ 迁移执行失败:', error);
    process.exit(1);
  }

  // 5. 验证迁移结果
  console.log('\n🔍 验证迁移结果...');

  const newVenuesExists = tableExists('venues');
  const venuesCount = getTableCount('venues');
  const newEventsCount = getTableCount('events');

  console.log(`   - venues表创建: ${newVenuesExists ? '✅' : '❌'}`);
  console.log(`   - venues表记录数: ${venuesCount}`);
  console.log(`   - events表记录数: ${newEventsCount}`);

  if (newVenuesExists && venuesCount > 0) {
    console.log('✅ 迁移验证成功');
  } else {
    console.log('⚠️  迁移验证失败，请检查数据');
  }

  console.log('\n🎉 本地venues表迁移完成！');
  console.log('\n📝 后续步骤:');
  console.log('   1. 测试API接口: GET /venues');
  console.log('   2. 测试events API: GET /events/{id} (应包含venue信息)');
  console.log('   3. 测试前端组件的venue显示');
}

/**
 * 显示数据库状态
 */
function showStatus() {
  console.log('📊 当前数据库状态:');

  const tables = ['events', 'venues', 'circles', 'users'];

  for (const table of tables) {
    const exists = tableExists(table);
    const count = exists ? getTableCount(table) : 0;
    console.log(`   - ${table}: ${exists ? '✅' : '❌'} (${count} 条记录)`);
  }
}

/**
 * 主函数
 */
async function main() {
  const args = process.argv.slice(2);

  if (args.includes('--status')) {
    showStatus();
    return;
  }

  if (args.includes('--help') || args.includes('-h')) {
    console.log(`
本地venues表迁移脚本

用法:
  pnpm tsx scripts/migrate-venues-local.ts [选项]

选项:
  --status           显示数据库状态
  --help, -h         显示帮助信息

示例:
  pnpm tsx scripts/migrate-venues-local.ts
  pnpm tsx scripts/migrate-venues-local.ts --status
`);
    return;
  }

  try {
    await runMigration();
  } catch (error) {
    console.error('❌ 脚本执行失败:', error);
    process.exit(1);
  }
}

main();
