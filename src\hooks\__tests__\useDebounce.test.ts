import { renderHook, act } from "@testing-library/react";
import { describe, test, expect, vi, beforeEach, afterEach } from "vitest";

import useDebounce from "@/hooks/useDebounce";

describe("useDebounce", () => {
  beforeEach(() => {
    vi.useFakeTimers();
  });

  afterEach(() => {
    vi.useRealTimers();
  });

  test("returns debounced value after delay", () => {
    const { result, rerender } = renderHook(({ v }) => useDebounce(v, 500), {
      initialProps: { v: "first" },
    });

    // 初始值应立即返回
    expect(result.current).toBe("first");

    // 更新值，但未到延迟时间，仍应返回旧值
    rerender({ v: "second" });
    expect(result.current).toBe("first");

    // 向前推进时间
    act(() => {
      vi.advanceTimersByTime(500);
    });

    expect(result.current).toBe("second");
  });
}); 