'use client'

import { useQueryClient } from '@tanstack/react-query';
import { useLocale } from 'next-intl';
import { useState } from 'react';
import { useGetEvents } from '@/api/generated/ayafeedComponents';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { createLocaleCacheManager } from '@/lib/locale-cache-utils';
import { setLocaleToCookie, type Locale } from '@/lib/locale-utils';

/**
 * 语言缓存调试页面
 * 用于测试语言切换时的缓存行为
 */
export default function LocaleCacheDebugPage() {
  const locale = useLocale() as Locale;
  const queryClient = useQueryClient();
  const [debugInfo, setDebugInfo] = useState<string[]>([]);

  // 测试API调用
  const { data: eventsData, isLoading, queryKey } = useGetEvents(
    { queryParams: { page: '1', pageSize: '5' } },
    { staleTime: 60 * 1000 }
  );

  const addDebugInfo = (info: string) => {
    setDebugInfo(prev => [...prev, `${new Date().toLocaleTimeString()}: ${info}`]);
  };

  const handleLanguageChange = (newLocale: Locale) => {
    addDebugInfo(`开始切换语言: ${locale} → ${newLocale}`);
    
    // 显示当前查询键
    addDebugInfo(`当前查询键: ${JSON.stringify(queryKey)}`);
    
    // 获取缓存统计
    const cacheManager = createLocaleCacheManager(queryClient);
    const statsBefore = cacheManager.getCacheStats();
    addDebugInfo(`切换前缓存统计: ${JSON.stringify(statsBefore)}`);
    
    // 切换语言
    setLocaleToCookie(newLocale);
    cacheManager.handleLocaleSwitch(locale, newLocale);
    
    // 刷新页面
    window.location.reload();
  };

  const clearDebugInfo = () => {
    setDebugInfo([]);
  };

  const inspectCache = () => {
    const cacheManager = createLocaleCacheManager(queryClient);
    const stats = cacheManager.getCacheStats();
    addDebugInfo(`当前缓存统计: ${JSON.stringify(stats, null, 2)}`);
    
    // 显示所有查询键
    const cache = queryClient.getQueryCache();
    const queries = cache.getAll();
    addDebugInfo(`所有查询键:`);
    queries.forEach((query, index) => {
      addDebugInfo(`  ${index + 1}. ${JSON.stringify(query.queryKey)}`);
    });
  };

  return (
    <div className="container mx-auto p-6 space-y-6">
      <Card>
        <CardHeader>
          <CardTitle>语言缓存调试工具</CardTitle>
          <p className="text-sm text-muted-foreground">
            当前语言: <strong>{locale}</strong>
          </p>
        </CardHeader>
        <CardContent className="space-y-4">
          {/* 语言切换按钮 */}
          <div className="flex gap-2">
            <Button 
              onClick={() => handleLanguageChange('zh')}
              variant={locale === 'zh' ? 'default' : 'outline'}
            >
              中文
            </Button>
            <Button 
              onClick={() => handleLanguageChange('ja')}
              variant={locale === 'ja' ? 'default' : 'outline'}
            >
              日本語
            </Button>
            <Button 
              onClick={() => handleLanguageChange('en')}
              variant={locale === 'en' ? 'default' : 'outline'}
            >
              English
            </Button>
          </div>

          {/* 调试操作 */}
          <div className="flex gap-2">
            <Button onClick={inspectCache} variant="secondary">
              检查缓存
            </Button>
            <Button onClick={clearDebugInfo} variant="outline">
              清空日志
            </Button>
          </div>
        </CardContent>
      </Card>

      {/* API数据显示 */}
      <Card>
        <CardHeader>
          <CardTitle>API数据测试</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-2">
            <p><strong>查询键:</strong> {JSON.stringify(queryKey)}</p>
            <p><strong>加载状态:</strong> {isLoading ? '加载中...' : '已完成'}</p>
            <p><strong>数据条数:</strong> {eventsData?.items?.length || 0}</p>
            {eventsData?.items?.[0] && (
              <div className="mt-4 p-3 bg-muted rounded">
                <p><strong>第一个事件:</strong></p>
                <p>ID: {eventsData.items[0].id}</p>
                <p>名称: {(eventsData.items[0] as any).name}</p>
              </div>
            )}
          </div>
        </CardContent>
      </Card>

      {/* 调试日志 */}
      <Card>
        <CardHeader>
          <CardTitle>调试日志</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="max-h-96 overflow-y-auto bg-muted p-3 rounded text-sm font-mono">
            {debugInfo.length === 0 ? (
              <p className="text-muted-foreground">暂无日志</p>
            ) : (
              debugInfo.map((info, index) => (
                <div key={index} className="mb-1">
                  {info}
                </div>
              ))
            )}
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
