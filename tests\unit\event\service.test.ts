import { describe, it, expect, vi, beforeEach } from 'vitest';
import type { D1Database } from '@cloudflare/workers-types';
import type { Cache, Logger } from '@/infrastructure';
import {
  listEvents,
  getEvent,
  listCirclesByEvent,
  listAppearances,
  createEvent,
  updateEvent,
  deleteEvent,
  listEventsAdmin,
  getEventAdmin,
} from '@/modules/event/service';
import { createEventRepository } from '@/modules/event/repository';
import { getVenueById } from '@/modules/venue/service';

// Mock dependencies
vi.mock('@/modules/event/repository');
vi.mock('@/modules/venue/service', () => ({
  getVenueById: vi.fn(),
}));

// Mock D1 Database
const createMockD1Database = () => {
  const mockPreparedStatement = {
    bind: vi.fn().mockReturnThis(),
    all: vi.fn(),
    first: vi.fn(),
  };

  return {
    prepare: vi.fn().mockReturnValue(mockPreparedStatement),
    _mockPreparedStatement: mockPreparedStatement,
  } as unknown as D1Database & { _mockPreparedStatement: any };
};

// Mock Cache
const createMockCache = () => ({
  get: vi.fn(),
  set: vi.fn(),
});

// Mock Logger
const createMockLogger = () => ({
  debug: vi.fn(),
  info: vi.fn(),
  warn: vi.fn(),
  error: vi.fn(),
});

// Mock Repository
const createMockRepository = () => ({
  findById: vi.fn(),
  create: vi.fn(),
  update: vi.fn(),
  delete: vi.fn(),
});

describe('event/service', () => {
  let mockDb: D1Database & { _mockPreparedStatement: any };
  let mockCache: Cache;
  let mockLogger: Logger;
  let mockRepository: any;

  beforeEach(() => {
    mockDb = createMockD1Database();
    mockCache = createMockCache();
    mockLogger = createMockLogger();
    mockRepository = createMockRepository();
    (createEventRepository as any).mockReturnValue(mockRepository);
    vi.clearAllMocks();
  });

  describe('listEvents', () => {
    it('should return cached data when cache hit', async () => {
      const cachedData = {
        items: [{ id: 'event-1', name: 'Test Event' }],
        total: 1,
        page: 1,
        pageSize: 50,
      };
      (mockCache.get as any).mockResolvedValue(cachedData);

      const searchParams = new URLSearchParams('page=1&pageSize=50');
      const result = await listEvents(
        mockDb,
        searchParams,
        'en',
        mockCache,
        mockLogger
      );

      expect(result).toEqual(cachedData);
      expect(mockCache.get).toHaveBeenCalledWith(
        expect.stringContaining('events_en_')
      );
      expect(mockLogger.debug).toHaveBeenCalledWith(
        'listEvents: hit cache',
        expect.any(Object)
      );
      expect(mockDb.prepare).not.toHaveBeenCalled();
    });

    it('should fetch data from database when cache miss', async () => {
      const mockEvents = [
        {
          id: 'event-1',
          name_en: 'Test Event',
          name_ja: 'テストイベント',
          name_zh: '测试活动',
          date_en: 'May 1, 2025',
          date_ja: '2025年5月1日',
          date_zh: '2025年5月1日',
          date_sort: 20250501,
          image_url: 'test.jpg',
          venue_name_en: 'Test Venue',
          venue_name_ja: 'テスト会場',
          venue_name_zh: '测试场地',
          venue_address_en: null,
          venue_address_ja: null,
          venue_address_zh: null,
          venue_lat: 35.0,
          venue_lng: 139.0,
          url: 'https://test.com',
          created_at: '2024-01-01T00:00:00Z',
          updated_at: '2024-01-01T00:00:00Z',
        },
      ];

      (mockCache.get as any).mockResolvedValue(null);
      mockDb._mockPreparedStatement.first.mockResolvedValue({ total: 1 });
      mockDb._mockPreparedStatement.all.mockResolvedValue({
        results: mockEvents,
      });

      const searchParams = new URLSearchParams('page=1&pageSize=50');
      const result = await listEvents(mockDb, searchParams, 'en', mockCache);

      expect(result.items).toHaveLength(1);
      expect(result.items[0]).toEqual({
        id: 'event-1',
        name: 'Test Event',
        date: 'May 1, 2025',
        date_sort: 20250501,
        image_url: 'test.jpg',
        venue_name: 'Test Venue',
        venue_address: null,
        venue_lat: 35.0,
        venue_lng: 139.0,
        url: 'https://test.com',
        created_at: '2024-01-01T00:00:00Z',
        updated_at: '2024-01-01T00:00:00Z',
      });
      expect(result.total).toBe(1);
      expect(result.page).toBe(1);
      expect(result.pageSize).toBe(50);
    });

    it('should handle different locales correctly', async () => {
      const mockEvents = [
        {
          id: 'event-1',
          name_en: 'Test Event',
          name_ja: 'テストイベント',
          name_zh: '测试活动',
          date_en: 'May 1, 2025',
          date_ja: '2025年5月1日',
          date_zh: '2025年5月1日',
          date_sort: 20250501,
          image_url: 'test.jpg',
          venue_name_en: 'Test Venue',
          venue_name_ja: 'テスト会場',
          venue_name_zh: '测试场地',
          venue_address_en: null,
          venue_address_ja: null,
          venue_address_zh: null,
          venue_lat: 35.0,
          venue_lng: 139.0,
          url: 'https://test.com',
          created_at: '2024-01-01T00:00:00Z',
          updated_at: '2024-01-01T00:00:00Z',
        },
      ];

      mockDb._mockPreparedStatement.first.mockResolvedValue({ total: 1 });
      mockDb._mockPreparedStatement.all.mockResolvedValue({
        results: mockEvents,
      });

      const searchParams = new URLSearchParams('page=1&pageSize=50');

      // Test Japanese locale
      const jaResult = await listEvents(mockDb, searchParams, 'ja');
      expect(jaResult.items[0].name).toBe('テストイベント');
      expect(jaResult.items[0].date).toBe('2025年5月1日');
      expect(jaResult.items[0].venue_name).toBe('テスト会場');

      // Test Chinese locale
      const zhResult = await listEvents(mockDb, searchParams, 'zh');
      expect(zhResult.items[0].name).toBe('测试活动');
      expect(zhResult.items[0].date).toBe('2025年5月1日');
      expect(zhResult.items[0].venue_name).toBe('测试场地');
    });

    it('should handle keyword search', async () => {
      mockDb._mockPreparedStatement.first.mockResolvedValue({ total: 0 });
      mockDb._mockPreparedStatement.all.mockResolvedValue({ results: [] });

      const searchParams = new URLSearchParams(
        'keyword=test&page=1&pageSize=50'
      );
      await listEvents(mockDb, searchParams, 'en');

      expect(mockDb.prepare).toHaveBeenCalledWith(
        expect.stringContaining(
          'e.name_en LIKE ? OR e.name_ja LIKE ? OR e.name_zh LIKE ? OR'
        )
      );
      expect(mockDb._mockPreparedStatement.bind).toHaveBeenCalledWith(
        '%test%',
        '%test%',
        '%test%',
        '%test%',
        '%test%',
        '%test%',
        50,
        0
      );
    });

    it('should handle date range filters', async () => {
      mockDb._mockPreparedStatement.first.mockResolvedValue({ total: 0 });
      mockDb._mockPreparedStatement.all.mockResolvedValue({ results: [] });

      const searchParams = new URLSearchParams(
        'date_from=20250101&date_to=20251231&page=1&pageSize=50'
      );
      await listEvents(mockDb, searchParams, 'en');

      expect(mockDb.prepare).toHaveBeenCalledWith(
        expect.stringContaining('e.date_sort >= ? AND e.date_sort <= ?')
      );
      expect(mockDb._mockPreparedStatement.bind).toHaveBeenCalledWith(
        20250101,
        20251231,
        50,
        0
      );
    });

    it('should handle pagination correctly', async () => {
      mockDb._mockPreparedStatement.first.mockResolvedValue({ total: 100 });
      mockDb._mockPreparedStatement.all.mockResolvedValue({ results: [] });

      const searchParams = new URLSearchParams('page=3&pageSize=20');
      await listEvents(mockDb, searchParams, 'en');

      // page=3, pageSize=20 -> offset=(3-1)*20=40
      expect(mockDb._mockPreparedStatement.bind).toHaveBeenCalledWith(20, 40);
    });

    it('should cache results when cache is provided', async () => {
      const mockEvents = [{ id: 'event-1', name_en: 'Test Event' }];
      (mockCache.get as any).mockResolvedValue(null);
      mockDb._mockPreparedStatement.first.mockResolvedValue({ total: 1 });
      mockDb._mockPreparedStatement.all.mockResolvedValue({
        results: mockEvents,
      });

      const searchParams = new URLSearchParams('page=1&pageSize=50');
      const result = await listEvents(mockDb, searchParams, 'en', mockCache);

      expect(mockCache.set).toHaveBeenCalledWith(
        expect.stringContaining('events_en_'),
        result,
        300
      );
    });

    it('should work without cache and logger', async () => {
      const mockEvents = [{ id: 'event-1', name_en: 'Test Event' }];
      mockDb._mockPreparedStatement.first.mockResolvedValue({ total: 1 });
      mockDb._mockPreparedStatement.all.mockResolvedValue({
        results: mockEvents,
      });

      const searchParams = new URLSearchParams('page=1&pageSize=50');
      const result = await listEvents(mockDb, searchParams, 'en');

      expect(result.items).toHaveLength(1);
      expect(result.total).toBe(1);
    });

    it('should handle edge case with Math.max for page and pageSize', async () => {
      mockDb._mockPreparedStatement.first.mockResolvedValue({ total: 0 });
      mockDb._mockPreparedStatement.all.mockResolvedValue({ results: [] });

      // Test negative and zero values
      const searchParams = new URLSearchParams('page=-1&pageSize=0');
      const result = await listEvents(mockDb, searchParams, 'en');

      expect(result.page).toBe(1); // Math.max(-1, 1) = 1
      expect(result.pageSize).toBe(1); // Math.max(0, 1) = 1
    });
  });

  describe('getEvent', () => {
    it('should return event when found', async () => {
      const mockEvent = {
        id: 'event-1',
        name_en: 'Test Event',
        name_ja: 'テストイベント',
        name_zh: '测试活动',
        date_en: 'May 1, 2025',
        date_ja: '2025年5月1日',
        date_zh: '2025年5月1日',
        date_sort: 20250501,
        image_url: 'test.jpg',
        venue_id: 'test-venue',
        url: 'https://test.com',
        created_at: '2024-01-01T00:00:00Z',
        updated_at: '2024-01-01T00:00:00Z',
      };

      const mockVenue = {
        id: 'test-venue',
        name: 'Test Venue',
        address: 'Test Address',
        lat: 35.0,
        lng: 139.0,
        capacity: 1000,
        website_url: 'https://venue.com',
        phone: '+81-3-1234-5678',
        description: 'Test venue description',
        facilities: '{"wifi": true}',
        transportation: '{"train": "Test Line"}',
        parking_info: '{"spaces": 100}',
      };

      mockDb._mockPreparedStatement.first.mockResolvedValue(mockEvent);
      (getVenueById as any).mockResolvedValue(mockVenue);

      const result = await getEvent(mockDb, 'event-1', 'en');

      expect(result).toEqual({
        id: 'event-1',
        name: 'Test Event',
        date: 'May 1, 2025',
        date_sort: 20250501,
        image_url: 'test.jpg',
        url: 'https://test.com',
        created_at: '2024-01-01T00:00:00Z',
        updated_at: '2024-01-01T00:00:00Z',
        venue: {
          id: 'test-venue',
          name: 'Test Venue',
          address: 'Test Address',
          lat: 35.0,
          lng: 139.0,
          capacity: 1000,
          website_url: 'https://venue.com',
          phone: '+81-3-1234-5678',
          description: 'Test venue description',
          facilities: '{"wifi": true}',
          transportation: '{"train": "Test Line"}',
          parking_info: '{"spaces": 100}',
        },
      });
      expect(mockDb.prepare).toHaveBeenCalled();
      expect(getVenueById).toHaveBeenCalledWith(mockDb, 'test-venue', 'en');
    });

    it('should return null when event not found', async () => {
      mockDb._mockPreparedStatement.first.mockResolvedValue(null);

      const result = await getEvent(mockDb, 'nonexistent', 'en');

      expect(result).toBeNull();
    });
  });

  describe('listCirclesByEvent', () => {
    it('should return circles for given event', async () => {
      const mockCircles = [
        { id: 'circle-1', name: 'Test Circle', booth_id: 'A01' },
        { id: 'circle-2', name: 'Another Circle', booth_id: 'B02' },
      ];

      mockDb._mockPreparedStatement.all.mockResolvedValue({
        results: mockCircles,
      });

      const result = await listCirclesByEvent(mockDb, 'event-1');

      expect(result).toEqual(mockCircles);
      expect(mockDb.prepare).toHaveBeenCalledWith(
        expect.stringContaining('JOIN appearances a ON a.circle_id = c.id')
      );
      expect(mockDb._mockPreparedStatement.bind).toHaveBeenCalledWith(
        'event-1'
      );
    });
  });

  describe('listAppearances', () => {
    it('should return paginated appearances', async () => {
      const mockAppearances = [
        {
          circle_id: 'circle-1',
          circle_name: 'Test Circle',
          circle_urls: 'https://test.com',
          category: 'original',
          artist_id: 'artist-1',
          artist_name: 'Test Artist',
          booth_id: 'A01',
        },
      ];

      mockDb._mockPreparedStatement.first.mockResolvedValue({ total: 1 });
      mockDb._mockPreparedStatement.all.mockResolvedValue({
        results: mockAppearances,
      });

      const searchParams = new URLSearchParams('page=1&pageSize=50');
      const result = await listAppearances(mockDb, 'event-1', searchParams);

      expect(result.items).toEqual(mockAppearances);
      expect(result.total).toBe(1);
      expect(result.page).toBe(1);
      expect(result.pageSize).toBe(50);
    });

    it('should handle keyword search in appearances', async () => {
      mockDb._mockPreparedStatement.first.mockResolvedValue({ total: 0 });
      mockDb._mockPreparedStatement.all.mockResolvedValue({ results: [] });

      const searchParams = new URLSearchParams(
        'keyword=test&page=1&pageSize=50'
      );
      await listAppearances(mockDb, 'event-1', searchParams);

      expect(mockDb.prepare).toHaveBeenCalledWith(
        expect.stringContaining('a.booth_id LIKE ? OR')
      );
      expect(mockDb._mockPreparedStatement.bind).toHaveBeenCalledWith(
        'event-1',
        '%test%',
        '%test%',
        '%test%',
        50,
        0
      );
    });

    it('should handle category filters', async () => {
      mockDb._mockPreparedStatement.first.mockResolvedValue({ total: 0 });
      mockDb._mockPreparedStatement.all.mockResolvedValue({ results: [] });

      const searchParams = new URLSearchParams(
        'category=original,touhou&page=1&pageSize=50'
      );
      await listAppearances(mockDb, 'event-1', searchParams);

      expect(mockDb.prepare).toHaveBeenCalledWith(
        expect.stringContaining('c.category IN (?, ?, ?)')
      );
      expect(mockDb._mockPreparedStatement.bind).toHaveBeenCalledWith(
        'event-1',
        'original,touhou',
        'original',
        'touhou',
        50,
        0
      );
    });

    it('should handle multiple category formats', async () => {
      mockDb._mockPreparedStatement.first.mockResolvedValue({ total: 0 });
      mockDb._mockPreparedStatement.all.mockResolvedValue({ results: [] });

      // Test both comma-separated and multiple category params
      const searchParams = new URLSearchParams();
      searchParams.append('category', 'original,touhou');
      searchParams.append('category', 'vocaloid');
      searchParams.append('page', '1');
      searchParams.append('pageSize', '50');

      await listAppearances(mockDb, 'event-1', searchParams);

      expect(mockDb.prepare).toHaveBeenCalledWith(
        expect.stringContaining('c.category IN (?, ?, ?, ?)')
      );
      expect(mockDb._mockPreparedStatement.bind).toHaveBeenCalledWith(
        'event-1',
        'original,touhou',
        'vocaloid',
        'original',
        'touhou',
        50,
        0
      );
    });
  });

  describe('createEvent', () => {
    it('should create event successfully', async () => {
      const mockEventData = {
        id: 'event-1',
        name_en: 'Test Event',
        name_ja: 'テストイベント',
        date_en: 'May 1, 2025',
        date_ja: '2025年5月1日',
        date_sort: 20250501,
      };

      const mockCreatedEvent = { ...mockEventData };
      mockRepository.create.mockResolvedValue(mockCreatedEvent);

      const result = await createEvent(mockDb, mockEventData);

      expect(result).toEqual(mockCreatedEvent);
      expect(mockRepository.create).toHaveBeenCalledWith(mockEventData);
    });
  });

  describe('updateEvent', () => {
    it('should update event successfully', async () => {
      const updateData = { name_en: 'Updated Event' };
      mockRepository.update.mockResolvedValue(undefined);

      await updateEvent(mockDb, 'event-1', updateData);

      expect(mockRepository.update).toHaveBeenCalledWith('event-1', updateData);
    });
  });

  describe('deleteEvent', () => {
    it('should delete event successfully', async () => {
      mockRepository.delete.mockResolvedValue(undefined);

      await deleteEvent(mockDb, 'event-1');

      expect(mockRepository.delete).toHaveBeenCalledWith('event-1');
    });
  });

  describe('listEventsAdmin', () => {
    it('should return admin events with cache', async () => {
      const cachedData = {
        items: [{ id: 'event-1', name_en: 'Test Event' }],
        total: 1,
        page: 1,
        pageSize: 50,
      };
      (mockCache.get as any).mockResolvedValue(cachedData);

      const searchParams = new URLSearchParams('page=1&pageSize=50');
      const result = await listEventsAdmin(
        mockDb,
        searchParams,
        mockCache,
        mockLogger
      );

      expect(result).toEqual(cachedData);
      expect(mockCache.get).toHaveBeenCalledWith(
        expect.stringContaining('events_admin_')
      );
      expect(mockLogger.debug).toHaveBeenCalledWith(
        'adminListEvents: hit cache',
        expect.any(Object)
      );
    });

    it('should fetch admin events from database', async () => {
      const mockEvents = [{ id: 'event-1', name_en: 'Test Event' }];
      (mockCache.get as any).mockResolvedValue(null);
      mockDb._mockPreparedStatement.first.mockResolvedValue({ total: 1 });
      mockDb._mockPreparedStatement.all.mockResolvedValue({
        results: mockEvents,
      });

      const searchParams = new URLSearchParams('page=1&pageSize=50');
      const result = await listEventsAdmin(mockDb, searchParams, mockCache);

      expect(result.items).toEqual(mockEvents);
      expect(result.total).toBe(1);
      expect(mockCache.set).toHaveBeenCalledWith(
        expect.stringContaining('events_admin_'),
        result,
        300
      );
    });
  });

  describe('getEventAdmin', () => {
    it('should return admin event when found', async () => {
      const mockEvent = { id: 'event-1', name_en: 'Test Event' };
      mockRepository.findById.mockResolvedValue(mockEvent);

      const result = await getEventAdmin(mockDb, 'event-1');

      expect(result).toEqual(mockEvent);
      expect(mockRepository.findById).toHaveBeenCalledWith('event-1');
    });

    it('should return null when admin event not found', async () => {
      mockRepository.findById.mockResolvedValue(null);

      const result = await getEventAdmin(mockDb, 'nonexistent');

      expect(result).toBeNull();
    });
  });
});
