/**
 * 图片查询 Hooks
 * 基于 React Query 的图片数据获取
 */

import React from 'react';
import { useQuery, useInfiniteQuery, useQueryClient } from '@tanstack/react-query';
import { ImageService } from '@/services/imageService';
import type { ImageListParams, ImageInfo } from '@/types/image';

/**
 * 获取图片列表
 */
export function useImages(params: ImageListParams, enabled = true) {
  return useQuery({
    queryKey: ['images', params.category, params.resourceId, params],
    queryFn: () => ImageService.list(params),
    enabled,
    staleTime: 5 * 60 * 1000, // 5分钟
    gcTime: 10 * 60 * 1000, // 10分钟
  });
}

/**
 * 获取单个图片详情
 */
export function useImage(id: string, enabled = true) {
  return useQuery({
    queryKey: ['image', id],
    queryFn: () => ImageService.getById(id),
    enabled: enabled && !!id,
    staleTime: 10 * 60 * 1000, // 10分钟
    gcTime: 30 * 60 * 1000, // 30分钟
  });
}

/**
 * 无限滚动图片列表
 */
export function useInfiniteImages(
  baseParams: Omit<ImageListParams, 'page'>,
  enabled = true
) {
  return useInfiniteQuery({
    queryKey: ['images', 'infinite', baseParams.category, baseParams.resourceId, baseParams],
    queryFn: ({ pageParam = 1 }) =>
      ImageService.list({ ...baseParams, page: pageParam }),
    enabled,
    initialPageParam: 1,
    getNextPageParam: (lastPage, allPages) => {
      const { pagination } = lastPage;
      if (!pagination) return undefined;
      
      const { page, totalPages } = pagination;
      return page < totalPages ? page + 1 : undefined;
    },
    staleTime: 5 * 60 * 1000, // 5分钟
    gcTime: 10 * 60 * 1000, // 10分钟
  });
}

/**
 * 预加载图片
 */
export function usePrefetchImage(id: string) {
  const queryClient = useQueryClient();

  return React.useCallback(() => {
    queryClient.prefetchQuery({
      queryKey: ['image', id],
      queryFn: () => ImageService.getById(id),
      staleTime: 10 * 60 * 1000,
    });
  }, [id, queryClient]);
}

/**
 * 图片缓存管理
 */
export function useImageCache() {
  const queryClient = useQueryClient();

  const invalidateImages = React.useCallback(
    (category?: string, resourceId?: string) => {
      if (category && resourceId) {
        queryClient.invalidateQueries({
          queryKey: ['images', category, resourceId],
        });
      } else {
        queryClient.invalidateQueries({
          queryKey: ['images'],
        });
      }
    },
    [queryClient]
  );

  const removeImageFromCache = React.useCallback(
    (id: string) => {
      queryClient.removeQueries({
        queryKey: ['image', id],
      });
    },
    [queryClient]
  );

  const updateImageInCache = React.useCallback(
    (id: string, updater: (oldData: ImageInfo | undefined) => ImageInfo) => {
      queryClient.setQueryData(['image', id], updater);
    },
    [queryClient]
  );

  return {
    invalidateImages,
    removeImageFromCache,
    updateImageInCache,
  };
}
