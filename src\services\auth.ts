/**
 * 认证服务
 * 基于后端文档包的认证集成规范
 */

import { request } from '@/lib/http';
import type { User } from '@/types/user';

export interface LoginCredentials {
  username: string;
  password: string;
}

export interface RegisterData {
  username: string;
  password: string;
  email?: string;
}

export interface AuthResponse {
  user: User;
  expires_at: string;
}

export class AuthService {
  private static USER_KEY = 'auth_user';
  private static currentUserPromise: Promise<User> | null = null;
  private static authCheckPromise: Promise<boolean> | null = null;

  // 添加缓存机制，减少频繁请求
  private static userCache: { user: User; timestamp: number } | null = null;
  private static CACHE_DURATION = 30 * 1000; // 30秒缓存

  // 用户登录
  static async login(credentials: LoginCredentials): Promise<AuthResponse> {
    const response = await request<{
      code: number;
      message: string;
      data: User;
    }>('/auth/login', {
      method: 'POST',
      body: JSON.stringify(credentials),
      skipAuth: true,
    });

    // 检查响应格式
    if (response.code !== 0) {
      throw new Error(response.message || '登录失败');
    }

    const user = response.data;

    // 只存储用户信息，认证依赖 Cookie
    this.setUser(user);

    // 更新缓存
    this.userCache = {
      user,
      timestamp: Date.now()
    };

    return {
      user,
      expires_at: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toISOString(), // 30天后过期
    };
  }

  // 用户注册
  static async register(userData: RegisterData): Promise<void> {
    await request('/auth/register', {
      method: 'POST',
      body: JSON.stringify(userData),
      skipAuth: true,
    });
  }

  // 退出登录
  static async logout(): Promise<void> {
    try {
      await request('/auth/logout', {
        method: 'POST',
      });
    } finally {
      // 无论请求是否成功都清除本地数据
      this.clearAuth();
    }
  }

  // 获取当前用户信息（带去重和缓存）
  static async getCurrentUser(silent: boolean = false): Promise<User> {
    // 检查缓存是否有效
    if (this.userCache && Date.now() - this.userCache.timestamp < this.CACHE_DURATION) {
      console.debug('[AuthService] 使用缓存的用户信息');
      return this.userCache.user;
    }

    // 如果已有进行中的请求，返回同一个Promise
    if (this.currentUserPromise) {
      console.debug('[AuthService] 复用进行中的用户请求');
      return this.currentUserPromise;
    }

    console.debug('[AuthService] 发起新的用户信息请求');
    this.currentUserPromise = this._fetchCurrentUser(silent);

    try {
      const user = await this.currentUserPromise;
      // 更新缓存
      this.userCache = {
        user,
        timestamp: Date.now()
      };
      return user;
    } finally {
      // 请求完成后清除Promise，允许下次请求
      this.currentUserPromise = null;
    }
  }

  private static async _fetchCurrentUser(silent: boolean = false): Promise<User> {
    const response = await request<User | {
      code: number;
      message: string;
      data: User;
    }>('/auth/me', {
      // 静默模式下不显示控制台错误
      silent: silent,
    });

    // 处理两种可能的响应格式
    let user: User;
    if ('code' in response) {
      // 标准API响应格式: {code: 0, data: {...}}
      if (response.code !== 0) {
        throw new Error(response.message || '获取用户信息失败');
      }
      user = response.data;
    } else {
      // 直接返回用户对象: {id, username, role}
      user = response;
    }

    // 更新本地用户信息
    this.setUser(user);
    return user;
  }

  // 认证状态检查（基于 Cookie，带去重）
  static async isAuthenticated(silent: boolean = false): Promise<boolean> {
    // 如果已有进行中的认证检查，返回同一个Promise
    if (this.authCheckPromise) {
      return this.authCheckPromise;
    }

    this.authCheckPromise = this._checkAuthentication(silent);

    try {
      const isAuth = await this.authCheckPromise;
      return isAuth;
    } finally {
      // 请求完成后清除Promise，允许下次请求
      this.authCheckPromise = null;
    }
  }

  private static async _checkAuthentication(silent: boolean = false): Promise<boolean> {
    try {
      await this.getCurrentUser(silent);
      return true;
    } catch {
      // 如果获取用户信息失败，清除本地缓存
      this.clearUser();
      return false;
    }
  }

  // 用户信息管理
  static setUser(user: User): void {
    if (typeof window !== 'undefined') {
      localStorage.setItem(this.USER_KEY, JSON.stringify(user));
    }
  }

  static getUser(): User | null {
    if (typeof window !== 'undefined') {
      const userStr = localStorage.getItem(this.USER_KEY);
      if (userStr) {
        try {
          return JSON.parse(userStr);
        } catch (error) {
          console.warn('Failed to parse user from localStorage:', error);
          // 清除损坏的数据
          localStorage.removeItem(this.USER_KEY);
          return null;
        }
      }
    }
    return null;
  }

  static clearUser(): void {
    if (typeof window !== 'undefined') {
      localStorage.removeItem(this.USER_KEY);
    }
  }

  // 清除所有认证信息
  static clearAuth(): void {
    this.clearUser();
    // 清除缓存
    this.userCache = null;
    // 清除进行中的请求
    this.currentUserPromise = null;
    this.authCheckPromise = null;
  }

  // 权限检查（基于实时用户信息）
  static async hasPermission(requiredRoles: string[]): Promise<boolean> {
    try {
      const user = await this.getCurrentUser();
      return requiredRoles.includes(user.role);
    } catch {
      return false;
    }
  }
}

export const authService = AuthService;
