/**
 * 多语言服务
 * 基于后端文档包的多语言集成规范
 */

export type Locale = 'zh' | 'ja' | 'en';

export interface LocaleConfig {
  code: Locale;
  name: string;
  nativeName: string;
  flag: string;
}

export const SUPPORTED_LOCALES: LocaleConfig[] = [
  { code: 'zh', name: 'Chinese', nativeName: '中文', flag: '🇨🇳' },
  { code: 'ja', name: 'Japanese', nativeName: '日本語', flag: '🇯🇵' },
  { code: 'en', name: 'English', nativeName: 'English', flag: '🇺🇸' },
];

export const DEFAULT_LOCALE: Locale = 'zh';

export class I18nService {
  private currentLocale: Locale = DEFAULT_LOCALE;
  
  constructor() {
    this.currentLocale = this.detectLocale();
  }
  
  // 检测用户语言偏好
  private detectLocale(): Locale {
    // 服务端渲染时返回默认语言
    if (typeof window === 'undefined') {
      return DEFAULT_LOCALE;
    }

    // 1. 检查localStorage
    const stored = localStorage.getItem('locale') as Locale;
    if (stored && this.isValidLocale(stored)) {
      return stored;
    }
    
    // 2. 检查Cookie
    const cookieLocale = this.getLocaleFromCookie();
    if (cookieLocale && this.isValidLocale(cookieLocale)) {
      return cookieLocale;
    }
    
    // 3. 检查浏览器语言
    const browserLang = navigator.language.split('-')[0] as Locale;
    if (this.isValidLocale(browserLang)) {
      return browserLang;
    }
    
    return DEFAULT_LOCALE;
  }
  
  private getLocaleFromCookie(): string | null {
    if (typeof document === 'undefined') return null;
    
    const cookieValue = document.cookie
      .split('; ')
      .find(row => row.startsWith('locale='))
      ?.split('=')[1];
    
    return cookieValue || null;
  }
  
  private isValidLocale(locale: string): locale is Locale {
    return SUPPORTED_LOCALES.some(l => l.code === locale);
  }
  
  // 获取当前语言
  getCurrentLocale(): Locale {
    return this.currentLocale;
  }
  
  // 设置语言
  setLocale(locale: Locale): void {
    if (!this.isValidLocale(locale)) {
      console.warn(`Unsupported locale: ${locale}`);
      return;
    }
    
    this.currentLocale = locale;
    
    // 存储到localStorage
    if (typeof window !== 'undefined') {
      localStorage.setItem('locale', locale);
    }
    
    // 设置Cookie（365天过期）
    if (typeof document !== 'undefined') {
      document.cookie = `locale=${locale}; path=/; max-age=${365 * 24 * 60 * 60}; samesite=lax`;
    }
    
    // 触发语言变更事件
    if (typeof window !== 'undefined') {
      window.dispatchEvent(new CustomEvent('localeChange', { 
        detail: { locale } 
      }));
    }
  }
  
  // 获取语言配置
  getLocaleConfig(locale?: Locale): LocaleConfig {
    const targetLocale = locale || this.currentLocale;
    return SUPPORTED_LOCALES.find(l => l.code === targetLocale) || SUPPORTED_LOCALES[0];
  }
  
  // 格式化日期
  formatDate(date: string | Date, options?: Intl.DateTimeFormatOptions): string {
    const dateObj = typeof date === 'string' ? new Date(date) : date;
    return new Intl.DateTimeFormat(this.getLocaleForIntl(), options).format(dateObj);
  }
  
  // 格式化数字
  formatNumber(number: number, options?: Intl.NumberFormatOptions): string {
    return new Intl.NumberFormat(this.getLocaleForIntl(), options).format(number);
  }
  
  // 格式化相对时间
  formatRelativeTime(date: string | Date): string {
    const now = new Date();
    const target = typeof date === 'string' ? new Date(date) : date;
    const diffInSeconds = Math.floor((now.getTime() - target.getTime()) / 1000);
    
    const rtf = new Intl.RelativeTimeFormat(this.getLocaleForIntl(), {
      numeric: 'auto'
    });
    
    if (Math.abs(diffInSeconds) < 60) return rtf.format(-diffInSeconds, 'second');
    if (Math.abs(diffInSeconds) < 3600) return rtf.format(-Math.floor(diffInSeconds / 60), 'minute');
    if (Math.abs(diffInSeconds) < 86400) return rtf.format(-Math.floor(diffInSeconds / 3600), 'hour');
    return rtf.format(-Math.floor(diffInSeconds / 86400), 'day');
  }
  
  private getLocaleForIntl(): string {
    const localeMap = {
      'zh': 'zh-CN',
      'ja': 'ja-JP',
      'en': 'en-US',
    };
    return localeMap[this.currentLocale];
  }
}

export const i18nService = new I18nService();
