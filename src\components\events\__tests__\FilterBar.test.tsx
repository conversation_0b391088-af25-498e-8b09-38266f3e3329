import { fireEvent } from "@testing-library/react";
import { expect, test } from "vitest";

import FilterBar from "@/components/events/FilterBar";
import { renderWithProviders, screen } from "@test/test-utils";

const categoryOptions = [
  { id: "comic", label: "漫画" },
  { id: "game", label: "游戏" },
];

test("keyword input triggers setKeyword", () => {
  const setKeyword = vi.fn();
  const setCategories = vi.fn();
  const toggle = vi.fn();
  renderWithProviders(
    <FilterBar
      keyword=""
      setKeyword={setKeyword}
      categories={[]}
      setCategories={setCategories}
      toggleCategory={toggle}
      categoryOptions={categoryOptions}
    />
  );

  fireEvent.change(screen.getByPlaceholderText(/搜索/), {
    target: { value: "abc" },
  });
  expect(setKeyword).toHaveBeenCalledWith("abc");
});

test("clicking category button triggers toggle", () => {
  const toggle = vi.fn();
  renderWithProviders(
    <FilterBar
      keyword=""
      setKeyword={() => {}}
      categories={[]}
      setCategories={() => {}}
      toggleCategory={toggle}
      categoryOptions={categoryOptions}
    />
  );

  fireEvent.click(screen.getByRole("button", { name: "漫画" }));
  expect(toggle).toHaveBeenCalledWith("comic");
}); 