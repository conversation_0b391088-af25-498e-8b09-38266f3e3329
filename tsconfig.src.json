{"extends": "./tsconfig.json", "compilerOptions": {"strict": true, "noImplicitAny": true, "strictNullChecks": true, "strictFunctionTypes": true, "strictBindCallApply": true, "strictPropertyInitialization": true, "noImplicitThis": true, "useUnknownInCatchVariables": true, "alwaysStrict": true, "noUncheckedIndexedAccess": true, "noImplicitReturns": true, "declaration": true, "emitDeclarationOnly": true, "noFallthroughCasesInSwitch": true, "outDir": "./dist/src", "tsBuildInfoFile": "./dist/src/tsconfig.src.tsbuildinfo"}, "include": ["src/**/*"], "exclude": ["src/**/*.test.ts", "src/**/*.spec.ts"]}