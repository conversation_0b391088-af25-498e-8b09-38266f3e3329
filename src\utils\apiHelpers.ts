/**
 * API辅助工具函数
 * 基于后端文档包的常用示例
 */

import { request } from '@/lib/http';
import { i18nService } from '@/services/i18n';

// 通用的分页参数类型
export interface PaginationParams {
  page?: number;
  pageSize?: number;
  limit?: number;
}

// 通用的分页响应类型
export interface PaginatedResponse<T> {
  items: T[];
  total: number;
  page: number;
  pageSize: number;
  hasNext: boolean;
  hasPrev: boolean;
}

// 创建带语言的API调用
export function createLocalizedApiCall<T>(
  endpoint: string,
  options?: RequestInit
) {
  const locale = i18nService.getCurrentLocale();
  
  return request<T>(endpoint, {
    ...options,
    headers: {
      'X-Locale': locale,
      ...options?.headers,
    },
  });
}

// 批量操作辅助函数
export async function batchOperation<T>(
  endpoint: string,
  action: string,
  ids: string[]
): Promise<T> {
  return request<T>(`${endpoint}/batch`, {
    method: 'POST',
    body: JSON.stringify({
      action,
      ids,
    }),
  });
}

// 文件上传辅助函数
export async function uploadFile(
  file: File,
  endpoint: string = '/upload'
): Promise<{ url: string; filename: string }> {
  const formData = new FormData();
  formData.append('file', file);
  
  return request(endpoint, {
    method: 'POST',
    body: formData,
    headers: {
      // 不设置Content-Type，让浏览器自动设置
    },
  });
}

// 导出数据辅助函数
export async function exportData(
  type: string,
  format: 'csv' | 'json' | 'xlsx' = 'csv',
  filters?: Record<string, any>
): Promise<Blob> {
  const response = await fetch(`/api/export/${type}`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    body: JSON.stringify({
      format,
      filters,
    }),
  });
  
  if (!response.ok) {
    throw new Error('导出失败');
  }
  
  return response.blob();
}

// 健康检查
export async function healthCheck(): Promise<{
  status: 'ok' | 'error';
  timestamp: string;
  version: string;
}> {
  return request('/health');
}

// 获取系统配置
export async function getSystemConfig(): Promise<{
  features: Record<string, boolean>;
  limits: Record<string, number>;
  version: string;
}> {
  return request('/system/config');
}
