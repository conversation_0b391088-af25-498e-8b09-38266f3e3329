'use client'

import { useState } from 'react'
import Image from 'next/image'
import { cn } from '@/lib/utils'
import { getImageUrl, getPlaceholderUrl } from '@/lib/image-service'

interface OptimizedImageProps {
  src: string | null | undefined
  alt: string
  width?: number
  height?: number
  className?: string
  fallbackType?: 'event' | 'circle' | 'default'
  priority?: boolean
  sizes?: string
  variant?: 'thumb' | 'medium' | 'large' | 'original'
  onError?: () => void
  onLoad?: () => void
}

/**
 * 优化的图片组件
 * 
 * 功能：
 * - 自动处理图片URL转换
 * - 提供加载状态和错误处理
 * - 支持多种图片变体
 * - 响应式图片加载
 */
export default function OptimizedImage({
  src,
  alt,
  width = 400,
  height = 300,
  className,
  fallbackType = 'default',
  priority = false,
  sizes,
  variant = 'original',
  onError,
  onLoad,
}: OptimizedImageProps) {
  const [imageError, setImageError] = useState(false)
  const [imageLoading, setImageLoading] = useState(true)

  // 处理图片URL
  const imageUrl = imageError 
    ? getPlaceholderUrl(fallbackType)
    : getImageUrl(src)

  const handleImageError = () => {
    setImageError(true)
    setImageLoading(false)
    onError?.()
  }

  const handleImageLoad = () => {
    setImageLoading(false)
    onLoad?.()
  }

  return (
    <div className={cn("relative overflow-hidden", className)}>
      {/* 加载状态骨架屏 */}
      {imageLoading && (
        <div className="absolute inset-0 bg-gray-200 animate-pulse" />
      )}
      
      {/* 图片 */}
      <Image
        src={imageUrl}
        alt={alt}
        width={width}
        height={height}
        className={cn(
          "transition-opacity duration-300",
          imageLoading ? "opacity-0" : "opacity-100"
        )}
        onError={handleImageError}
        onLoad={handleImageLoad}
        priority={priority}
        sizes={sizes}
      />
    </div>
  )
}
