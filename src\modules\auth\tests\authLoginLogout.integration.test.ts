import { describe, it, expect, beforeEach, afterEach, vi } from 'vitest';
import * as bcrypt from 'bcryptjs';

vi.mock('bcryptjs');

import * as authService from '../service';
import app from '@/app';

// @ts-ignore
const Request = globalThis.Request;

function createMockDB(hasUser = true) {
  return {
    prepare: (query: string) => {
      const upper = query.toUpperCase();

      // 登录查询
      if (upper.includes('FROM AUTH_KEY')) {
        const row = hasUser
          ? {
              id: 'u1',
              hashed_password: 'hashed',
              username: 'alice',
              role: 'viewer',
            }
          : null;
        return {
          first: async () => row,
          bind: () => ({ first: async () => row }),
        };
      }

      // 其它查询
      const buildResponse = () => ({
        all: async () => ({ results: [] }),
        first: async () => null,
        run: async () => ({ success: true }),
      });
      return {
        ...buildResponse(),
        bind: (..._args: any[]) => buildResponse(),
      };
    },
  };
}

function withEnv(url: string, init: RequestInit | undefined, env: any) {
  const base = url.startsWith('http') ? url : `http://localhost${url}`;
  return app.fetch(new Request(base, init), env);
}

let validateSpy: any;

beforeEach(() => {
  vi.mocked(bcrypt.compare).mockResolvedValue(true as any);
  validateSpy = vi
    .spyOn(authService, 'validateSession')
    .mockResolvedValue({ id: 'u1', username: 'tester', role: 'viewer' } as any);
});

afterEach(() => {
  vi.restoreAllMocks();
});

describe('/auth/login and /auth/logout', () => {
  const validBody = { username: 'alice', password: 'secret123' };

  it('should login successfully', async () => {
    const res = await withEnv(
      '/auth/login',
      {
        method: 'POST',
        body: JSON.stringify(validBody),
        headers: { 'Content-Type': 'application/json' },
      },
      { DB: createMockDB(true) }
    );

    expect(res.status).toBe(200);
    const json = (await res.json()) as any;
    expect(json.code).toBe(0);
    expect(json.message).toBe('登录成功');
    expect(json.data).toEqual({ id: 'u1', username: 'alice', role: 'viewer' });
    expect(res.headers.get('Set-Cookie')).toContain('auth_session=');
  });

  it('should respond 400 when user not found', async () => {
    const res = await withEnv(
      '/auth/login',
      {
        method: 'POST',
        body: JSON.stringify(validBody),
        headers: { 'Content-Type': 'application/json' },
      },
      { DB: createMockDB(false) }
    );

    expect(res.status).toBe(400);
    const json = (await res.json()) as any;
    expect(json.code).toBe(40002);
  });

  it('should respond 400 when password mismatch', async () => {
    vi.mocked(bcrypt.compare).mockResolvedValue(false as any);

    const res = await withEnv(
      '/auth/login',
      {
        method: 'POST',
        body: JSON.stringify(validBody),
        headers: { 'Content-Type': 'application/json' },
      },
      { DB: createMockDB(true) }
    );

    expect(res.status).toBe(400);
    const json = (await res.json()) as any;
    expect(json.code).toBe(40003);
  });

  it('should logout successfully', async () => {
    const res = await withEnv(
      '/auth/logout',
      {
        method: 'POST',
        headers: { Cookie: 'auth_session=session_abc' },
      },
      { DB: createMockDB(true) }
    );

    expect(res.status).toBe(200);
    const json = (await res.json()) as any;
    expect(json.message).toBe('已退出登录');
    expect(res.headers.get('Set-Cookie')).toContain('auth_session=');
  });

  it('should reject logout when unauthenticated', async () => {
    validateSpy.mockResolvedValue(null as any);

    const res = await withEnv(
      '/auth/logout',
      { method: 'POST' },
      { DB: createMockDB(true) }
    );

    expect(res.status).toBe(401);
  });
});
