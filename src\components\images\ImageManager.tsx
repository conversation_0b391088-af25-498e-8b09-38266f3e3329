/**
 * ImageManager 图片管理器组件
 * 集成图片上传、列表显示、预览和管理功能
 */

'use client';

import React, { useState } from 'react';
import { Upload, Filter, Grid, List } from 'lucide-react';
import { cn } from '@/lib/utils';
import { Button } from '@/components/ui/button';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { ImageUpload } from './ImageUpload';
import { ImageGrid } from './ImageGrid';
import { ImagePreview } from './ImagePreview';
import { useImages } from '@/hooks/useImages';
import { useImageDelete } from '@/hooks/useImageDelete';
import type { ImageManagerProps, ImageInfo, ImageFilterState } from '@/types/image';

export function ImageManager({
  category,
  resourceId,
  allowUpload = true,
  allowDelete = true,
  showFilters = true,
  className,
}: ImageManagerProps) {
  const [selectedImages, setSelectedImages] = useState<Set<string>>(new Set());
  const [previewState, setPreviewState] = useState({
    isOpen: false,
    currentIndex: 0,
    images: [] as ImageInfo[],
  });
  const [filters, setFilters] = useState<ImageFilterState>({});
  const [activeTab, setActiveTab] = useState<'grid' | 'upload'>('grid');

  // 获取图片列表
  const {
    data: imageData,
    isLoading,
    error,
    refetch,
  } = useImages({
    category,
    resourceId,
    ...filters,
  });

  // 调试信息
  React.useEffect(() => {
    console.log('ImageManager - Query params:', { category, resourceId, filters });
    console.log('ImageManager - API response:', { imageData, isLoading, error });
  }, [category, resourceId, filters, imageData, isLoading, error]);

  // 删除图片
  const deleteImages = useImageDelete({
    onSuccess: () => {
      setSelectedImages(new Set());
      refetch();
    },
  });

  const images = imageData?.images || [];

  // 如果没有图片且没有加载中，显示提示信息
  const showEmptyState = !isLoading && images.length === 0 && !error;

  // 处理上传成功
  const handleUploadSuccess = (uploadedImages: ImageInfo[]) => {
    refetch();
    setActiveTab('grid');
  };

  // 处理预览
  const handlePreview = (image: ImageInfo, index: number) => {
    setPreviewState({
      isOpen: true,
      currentIndex: index,
      images,
    });
  };

  // 关闭预览
  const closePreview = () => {
    setPreviewState(prev => ({ ...prev, isOpen: false }));
  };

  // 预览导航
  const handlePreviewNext = () => {
    setPreviewState(prev => ({
      ...prev,
      currentIndex: Math.min(prev.currentIndex + 1, images.length - 1),
    }));
  };

  const handlePreviewPrevious = () => {
    setPreviewState(prev => ({
      ...prev,
      currentIndex: Math.max(prev.currentIndex - 1, 0),
    }));
  };

  // 删除图片
  const handleDelete = (imagesToDelete: ImageInfo[]) => {
    const relativePaths = imagesToDelete.map(img => img.relativePath);
    deleteImages.mutate(relativePaths);
  };

  // 预览中删除图片
  const handlePreviewDelete = (image: ImageInfo) => {
    handleDelete([image]);
    
    // 如果删除的是当前预览的图片，调整预览状态
    const newImages = images.filter(img => img.id !== image.id);
    if (newImages.length === 0) {
      closePreview();
    } else {
      const newIndex = Math.min(previewState.currentIndex, newImages.length - 1);
      setPreviewState(prev => ({
        ...prev,
        currentIndex: newIndex,
        images: newImages,
      }));
    }
  };

  return (
    <div className={cn('space-y-6', className)}>
      {/* 头部 */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold">图片管理</h2>
          <p className="text-muted-foreground">
            管理 {category === 'event' ? '展会' : category === 'circle' ? '社团' : '场馆'} 的图片：{resourceId}
          </p>
        </div>
        
        {/* 统计信息 */}
        <div className="text-right">
          <div className="text-2xl font-bold">{images.length}</div>
          <div className="text-sm text-muted-foreground">
            张图片
          </div>
        </div>
      </div>

      {/* 主要内容 */}
      <Tabs value={activeTab} onValueChange={(value) => setActiveTab(value as 'grid' | 'upload')}>
        <TabsList>
          <TabsTrigger value="grid" className="flex items-center gap-2">
            <Grid className="h-4 w-4" />
            图片列表 ({images.length})
          </TabsTrigger>
          {allowUpload && (
            <TabsTrigger value="upload" className="flex items-center gap-2">
              <Upload className="h-4 w-4" />
              上传图片
            </TabsTrigger>
          )}
        </TabsList>

        {/* 图片网格 */}
        <TabsContent value="grid" className="space-y-4">
          {/* 筛选器 */}
          {showFilters && (
            <div className="flex items-center gap-4 p-4 bg-muted/50 rounded-lg">
              <Filter className="h-4 w-4 text-muted-foreground" />
              <div className="text-sm text-muted-foreground">
                筛选功能即将推出...
              </div>
            </div>
          )}

          {/* 错误状态 */}
          {error && (
            <div className="p-4 bg-red-50 border border-red-200 rounded-lg">
              <p className="text-red-700">加载图片失败：{error.message}</p>
              <Button
                variant="outline"
                size="sm"
                onClick={() => refetch()}
                className="mt-2"
              >
                重试
              </Button>
            </div>
          )}

          {/* 图片网格 */}
          <ImageGrid
            images={images}
            loading={isLoading}
            selectedImages={selectedImages}
            onSelectionChange={setSelectedImages}
            onPreview={handlePreview}
            onDelete={allowDelete ? handleDelete : undefined}
            showActions={allowDelete}
          />
        </TabsContent>

        {/* 上传页面 */}
        {allowUpload && (
          <TabsContent value="upload" className="space-y-4">
            <ImageUpload
              category={category}
              resourceId={resourceId}
              imageType="gallery"
              variant="original"
              multiple={true}
              onUploadSuccess={handleUploadSuccess}
            />
          </TabsContent>
        )}
      </Tabs>

      {/* 图片预览 */}
      <ImagePreview
        isOpen={previewState.isOpen}
        images={previewState.images}
        currentIndex={previewState.currentIndex}
        onClose={closePreview}
        onNext={handlePreviewNext}
        onPrevious={handlePreviewPrevious}
        onDelete={allowDelete ? handlePreviewDelete : undefined}
        showActions={allowDelete}
      />
    </div>
  );
}
