"use client";

import { useRouter } from "next/navigation";
import { useForm } from "react-hook-form";
import { ArrowLeft } from "lucide-react";

import { But<PERSON> } from "@/components/ui/button";
import VenueForm from "@/components/admin/VenueForm";
import { useCreateVenue } from "@/hooks/admin/useVenues";

interface VenueFormData {
  name_en: string;
  name_ja: string;
  name_zh: string;
  address_en?: string;
  address_ja?: string;
  address_zh?: string;
  lat: number;
  lng: number;
  capacity?: number;
  website_url?: string;
  phone?: string;
  description_en?: string;
  description_ja?: string;
  description_zh?: string;
  facilities?: string;
  transportation?: string;
  parking_info?: string;
}

export default function NewVenuePage() {
  const router = useRouter();
  const createVenue = useCreateVenue();

  const form = useForm<VenueFormData>({
    defaultValues: {
      name_en: "",
      name_ja: "",
      name_zh: "",
      address_en: "",
      address_ja: "",
      address_zh: "",
      lat: 0,
      lng: 0,
      capacity: undefined,
      website_url: "",
      phone: "",
      description_en: "",
      description_ja: "",
      description_zh: "",
      facilities: "",
      transportation: "",
      parking_info: "",
    },
  });

  const onSubmit = async (values: VenueFormData) => {
    try {
      await createVenue.mutateAsync(values);
      router.push("/admin/venues");
    } catch (error) {
      // Error is handled by the mutation
      console.error("Failed to create venue:", error);
    }
  };

  return (
    <div className="space-y-6">
      {/* 页面标题和导航 */}
      <div className="flex items-center gap-4">
        <Button
          variant="ghost"
          size="sm"
          onClick={() => router.back()}
        >
          <ArrowLeft className="h-4 w-4 mr-2" />
          返回
        </Button>
        <div>
          <h1 className="text-2xl font-bold">新增场馆</h1>
          <p className="text-muted-foreground text-sm">
            创建新的展会场馆，请填写完整的多语言信息
          </p>
        </div>
      </div>

      {/* 表单 */}
      <VenueForm
        form={form}
        onSubmit={onSubmit}
        isSubmitting={createVenue.isPending}
        submitText="创建场馆"
        title="新增场馆"
        description="请填写完整的场馆信息，支持多语言"
      />
    </div>
  );
}
