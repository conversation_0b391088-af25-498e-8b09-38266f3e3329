import { expect, test, vi } from "vitest"
import { fireEvent } from "@testing-library/react"

import EnhancedFilterBar from "@/components/events/EnhancedFilterBar"
import { renderWithProviders, screen } from "@test/test-utils"

const mockProps = {
  keyword: "",
  setKeyword: vi.fn(),
  categories: [],
  setCategories: vi.fn(),
  toggleCategory: vi.fn(),
  categoryOptions: [
    { id: "game", label: "游戏" },
    { id: "music", label: "音乐" },
    { id: "art", label: "绘画" },
  ],
  viewMode: "grid" as const,
  setViewMode: vi.fn(),
  sortBy: "name",
  setSortBy: vi.fn(),
  total: 150,
}

test("renders search input and controls", () => {
  const { container } = renderWithProviders(<EnhancedFilterBar {...mockProps} />)

  // 检查搜索框
  const searchInput = screen.getByPlaceholderText("搜索摊位号、社团名称或作者...")
  expect(searchInput).toBeInTheDocument()
  expect(searchInput).toHaveClass("search-input-enhanced")

  // 检查排序下拉菜单
  expect(screen.getByText("按名称排序")).toBeInTheDocument()

  // 检查视图模式切换按钮容器
  const viewModeContainer = container.querySelector('.flex.border.rounded-md')
  expect(viewModeContainer).toBeInTheDocument()
})

test("handles search input changes", () => {
  renderWithProviders(<EnhancedFilterBar {...mockProps} />)

  const searchInput = screen.getByPlaceholderText("搜索摊位号、社团名称或作者...")
  fireEvent.change(searchInput, { target: { value: "test search" } })

  expect(mockProps.setKeyword).toHaveBeenCalledWith("test search")
})

test("renders category filter badges", () => {
  renderWithProviders(<EnhancedFilterBar {...mockProps} />)

  // 检查分类筛选标题
  expect(screen.getByText("分类筛选")).toBeInTheDocument()

  // 检查分类标签
  expect(screen.getByText("游戏")).toBeInTheDocument()
  expect(screen.getByText("音乐")).toBeInTheDocument()
  expect(screen.getByText("绘画")).toBeInTheDocument()
})

test("handles category toggle", () => {
  renderWithProviders(<EnhancedFilterBar {...mockProps} />)

  const gameCategory = screen.getByText("游戏")
  fireEvent.click(gameCategory)

  expect(mockProps.toggleCategory).toHaveBeenCalledWith("game")
})

test("shows selected categories with active styling", () => {
  const propsWithSelectedCategories = {
    ...mockProps,
    categories: ["game", "music"],
  }

  const { container } = renderWithProviders(<EnhancedFilterBar {...propsWithSelectedCategories} />)

  // 检查选中的分类是否有 active 类
  const gameCategory = screen.getByText("游戏")
  const musicCategory = screen.getByText("音乐")
  const artCategory = screen.getByText("绘画")

  expect(gameCategory.closest('.filter-badge')).toHaveClass("active")
  expect(musicCategory.closest('.filter-badge')).toHaveClass("active")
  expect(artCategory.closest('.filter-badge')).not.toHaveClass("active")
})

test("handles sort option changes", () => {
  renderWithProviders(<EnhancedFilterBar {...mockProps} />)

  // 点击排序下拉菜单
  const sortButton = screen.getByText("按名称排序")
  fireEvent.click(sortButton)

  // 等待下拉菜单打开，然后选择不同的排序选项
  // 注意：由于使用了 DropdownMenu，菜单项可能不会立即出现在 DOM 中
  // 这个测试主要验证点击事件是否正确触发
  expect(sortButton).toBeInTheDocument()
  expect(mockProps.setSortBy).not.toHaveBeenCalled() // 只是点击按钮，还没有选择选项
})

test("handles view mode changes", () => {
  const { container } = renderWithProviders(<EnhancedFilterBar {...mockProps} />)

  // 获取视图模式按钮容器
  const viewModeContainer = container.querySelector('.flex.border.rounded-md')
  const buttons = viewModeContainer?.querySelectorAll('button')

  if (buttons && buttons.length >= 2) {
    // 点击第二个视图模式按钮
    fireEvent.click(buttons[1])
    expect(mockProps.setViewMode).toHaveBeenCalled()
  }
})

test("shows results count", () => {
  renderWithProviders(<EnhancedFilterBar {...mockProps} />)

  // 检查结果统计文本（可能被分割在不同元素中）
  expect(screen.getByText(/找到/)).toBeInTheDocument()
  expect(screen.getByText(/150/)).toBeInTheDocument()
  expect(screen.getByText(/个参展商/)).toBeInTheDocument()
})

test("shows filtered categories count", () => {
  const propsWithFilters = {
    ...mockProps,
    categories: ["game", "music"],
  }

  renderWithProviders(<EnhancedFilterBar {...propsWithFilters} />)

  expect(screen.getByText(/已筛选.*2.*个分类/)).toBeInTheDocument()
})

test("handles reset all filters", () => {
  const propsWithFilters = {
    ...mockProps,
    keyword: "test",
    categories: ["game"],
  }

  renderWithProviders(<EnhancedFilterBar {...propsWithFilters} />)

  const resetButton = screen.getByText("重置所有")
  fireEvent.click(resetButton)

  expect(mockProps.setKeyword).toHaveBeenCalledWith("")
  expect(mockProps.setCategories).toHaveBeenCalledWith([])
})

test("handles clear filters when categories are selected", () => {
  const propsWithCategories = {
    ...mockProps,
    categories: ["game", "music"],
  }

  renderWithProviders(<EnhancedFilterBar {...propsWithCategories} />)

  const clearButton = screen.getByText("清除筛选")
  fireEvent.click(clearButton)

  expect(mockProps.setCategories).toHaveBeenCalledWith([])
})

test("applies correct CSS classes", () => {
  const { container } = renderWithProviders(<EnhancedFilterBar {...mockProps} />)

  // 检查关键的 CSS 类
  expect(container.querySelector('.search-input-enhanced')).toBeInTheDocument()
  expect(container.querySelector('.filter-badge')).toBeInTheDocument()
})

test("renders with empty category options", () => {
  const propsWithNoCategories = {
    ...mockProps,
    categoryOptions: [],
  }

  renderWithProviders(<EnhancedFilterBar {...propsWithNoCategories} />)

  // 应该仍然渲染其他元素
  expect(screen.getByPlaceholderText("搜索摊位号、社团名称或作者...")).toBeInTheDocument()
  expect(screen.getByText("分类筛选")).toBeInTheDocument()
})
