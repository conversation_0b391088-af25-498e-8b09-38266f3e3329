'use client'

import { useQueryClient } from '@tanstack/react-query';
import { useLocale } from 'next-intl';
import { useEffect, useState } from 'react';
import { useGetEvents, getEventsQuery } from '@/api/generated/ayafeedComponents';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';

/**
 * 查询键调试页面
 * 用于验证查询键是否正确包含语言信息
 */
export default function QueryKeysDebugPage() {
  const locale = useLocale();
  const queryClient = useQueryClient();
  const [queryKeys, setQueryKeys] = useState<string[]>([]);

  // 测试不同的API调用
  const eventsQuery = useGetEvents(
    { queryParams: { page: '1', pageSize: '5' } },
    { staleTime: 60 * 1000 }
  );

  const refreshQueryKeys = () => {
    const cache = queryClient.getQueryCache();
    const queries = cache.getAll();
    const keys = queries.map(query => JSON.stringify(query.queryKey, null, 2));
    setQueryKeys(keys);
  };

  useEffect(() => {
    // 页面加载时刷新查询键列表
    const timer = setTimeout(refreshQueryKeys, 1000);
    return () => clearTimeout(timer);
  }, [queryClient]);

  // 手动生成查询键来验证
  const manualQueryKey = getEventsQuery({ 
    queryParams: { page: '1', pageSize: '5' } 
  }).queryKey;

  return (
    <div className="container mx-auto p-6 space-y-6">
      <Card>
        <CardHeader>
          <CardTitle>查询键调试工具</CardTitle>
          <p className="text-sm text-muted-foreground">
            当前语言: <strong>{locale}</strong>
          </p>
        </CardHeader>
        <CardContent className="space-y-4">
          <Button onClick={refreshQueryKeys}>
            刷新查询键列表
          </Button>
        </CardContent>
      </Card>

      <Card>
        <CardHeader>
          <CardTitle>手动生成的查询键</CardTitle>
        </CardHeader>
        <CardContent>
          <pre className="bg-muted p-3 rounded text-sm overflow-x-auto">
            {JSON.stringify(manualQueryKey, null, 2)}
          </pre>
        </CardContent>
      </Card>

      <Card>
        <CardHeader>
          <CardTitle>当前Events查询状态</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-2">
            <p><strong>查询键:</strong></p>
            <pre className="bg-muted p-3 rounded text-sm overflow-x-auto">
              {JSON.stringify(eventsQuery.queryKey, null, 2)}
            </pre>
            <p><strong>加载状态:</strong> {eventsQuery.isLoading ? '加载中...' : '已完成'}</p>
            <p><strong>数据条数:</strong> {eventsQuery.data?.items?.length || 0}</p>
            <p><strong>错误:</strong> {eventsQuery.error ? String(eventsQuery.error) : '无'}</p>
          </div>
        </CardContent>
      </Card>

      <Card>
        <CardHeader>
          <CardTitle>所有缓存的查询键</CardTitle>
          <p className="text-sm text-muted-foreground">
            总数: {queryKeys.length}
          </p>
        </CardHeader>
        <CardContent>
          <div className="max-h-96 overflow-y-auto space-y-2">
            {queryKeys.length === 0 ? (
              <p className="text-muted-foreground">暂无查询键</p>
            ) : (
              queryKeys.map((key, index) => (
                <div key={index} className="border rounded p-2">
                  <p className="text-sm font-semibold mb-1">查询 #{index + 1}</p>
                  <pre className="bg-muted p-2 rounded text-xs overflow-x-auto">
                    {key}
                  </pre>
                </div>
              ))
            )}
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
