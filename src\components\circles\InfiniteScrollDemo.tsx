"use client";

import { useState } from "react";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";

// 演示组件，用于测试无限滚动功能
export default function InfiniteScrollDemo() {
  const [searchTerm, setSearchTerm] = useState("");
  
  return (
    <div className="p-4 space-y-4">
      <Card>
        <CardHeader>
          <CardTitle>无限滚动功能测试</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div>
            <label className="text-sm font-medium">搜索测试</label>
            <Input
              placeholder="输入搜索关键词..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="mt-1"
            />
            <p className="text-xs text-muted-foreground mt-1">
              搜索词会在300ms后生效（防抖处理）
            </p>
          </div>
          
          <div className="space-y-2">
            <h4 className="text-sm font-medium">功能说明：</h4>
            <ul className="text-xs text-muted-foreground space-y-1">
              <li>• 滚动到底部自动加载更多数据</li>
              <li>• 搜索功能带防抖处理（300ms）</li>
              <li>• 每次加载50个社团</li>
              <li>• 支持实时搜索和筛选</li>
              <li>• 加载状态和完成状态提示</li>
            </ul>
          </div>
          
          <div className="pt-2 border-t">
            <p className="text-xs text-muted-foreground">
              当前搜索词: <span className="font-mono">{searchTerm || "(无)"}</span>
            </p>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
