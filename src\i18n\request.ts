import { getRequestConfig } from "next-intl/server"
import { cookies } from "next/headers"

// 支持的语言列表
const LOCALES = ["ja", "en", "zh"] as const
type Locale = (typeof LOCALES)[number]

export default getRequestConfig(async () => {
  // 从 cookie 读取语言设置，默认日语
  const cookieStore = await cookies()
  const locale = (cookieStore.get("locale")?.value || "ja") as Locale

  return {
    locale,
    messages: (await import(`./messages/${locale}.json`)).default,
    timeZone: "Asia/Tokyo",
  }
})
