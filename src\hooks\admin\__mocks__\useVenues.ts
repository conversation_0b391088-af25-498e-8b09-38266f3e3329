import { vi } from 'vitest';

// Mock venue data
const mockVenues = [
  {
    id: 'tokyo-big-sight',
    name: 'Tokyo Big Sight',
    address: '3-11-1 Ariake, Koto City, Tokyo',
    lat: 35.6298,
    lng: 139.793,
    capacity: 50000,
  },
  {
    id: 'ma<PERSON><PERSON>-messe',
    name: '<PERSON><PERSON><PERSON>',
    address: '2-1 Nakase, Mihama Ward, Chiba',
    lat: 35.6478,
    lng: 140.0347,
    capacity: 30000,
  },
];

export const usePublicVenues = vi.fn(() => ({
  data: {
    items: mockVenues,
    total: mockVenues.length,
    page: 1,
    pageSize: 50,
  },
  isLoading: false,
  error: null,
}));

export const useAdminVenues = vi.fn(() => ({
  data: {
    items: mockVenues,
    total: mockVenues.length,
    page: 1,
    pageSize: 20,
  },
  isLoading: false,
  error: null,
}));

export const useAdminVenueDetail = vi.fn((id: string) => ({
  data: mockVenues.find(v => v.id === id),
  isLoading: false,
  error: null,
}));

export const useCreateVenue = vi.fn(() => ({
  mutate: vi.fn(),
  mutateAsync: vi.fn(),
  isPending: false,
  isSuccess: false,
  isError: false,
  error: null,
}));

export const useUpdateVenue = vi.fn(() => ({
  mutate: vi.fn(),
  mutateAsync: vi.fn(),
  isPending: false,
  isSuccess: false,
  isError: false,
  error: null,
}));

export const useDeleteVenue = vi.fn(() => ({
  mutate: vi.fn(),
  mutateAsync: vi.fn(),
  isPending: false,
  isSuccess: false,
  isError: false,
  error: null,
}));
