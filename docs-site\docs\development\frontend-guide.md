# Admin 前端接入后端 API 指南

> 目标读者：前端同学（React 19 + Next.js 15 / Vite），需要将现有本地 Mock / API Routes 迁移到远程 Cloudflare Worker API。
>
> 更新时间：2025-05-21

---

## 1. 总览

| 项目       | 值                                                                                |
| ---------- | --------------------------------------------------------------------------------- |
| Base URL   | `https://api.example.com`（通过 `NEXT_PUBLIC_API_URL` 注入）                      |
| API 版本   | 无版本号前缀                                                                      |
| 认证方式   | JWT Access Token (15 min) + HttpOnly Refresh Token Cookie (14 d)                  |
| 必要请求头 | `Content-Type: application/json`, `Authorization: Bearer <token>`, `X-Locale: zh` |
| CORS       | 已允许跨域；**必须** `credentials: "include"` 才能携带 Cookie                     |

后端基于 **Hono + Cloudflare D1**，所有成功响应使用 2xx；错误响应为非 2xx，Body 为 `{ error: "error message" }`。

---

## 2. 认证流程

1. **登录** `POST /auth/login`：
   - 请求体：`{ username, password }`。
   - 成功返回三段式 JSON（含 `accessToken` 与 `user`），并在响应头 `Set-Cookie` 写入 `refresh_token`（14 天）。
2. 前端保存 `accessToken`（内存 / `sessionStorage`），所有受保护请求在 `fetch` 中附带：
   ```http
   Authorization: Bearer <accessToken>
   ```
3. `accessToken` 到期或接口返回 **401** 时：
   - 自动调用 `POST /auth/refresh`（需携带 `refresh_token` Cookie，前端无需手动添加）。
   - 成功后替换本地 `accessToken` 并重试原请求。
4. **登出** `POST /auth/logout`：后端清除 `refresh_token` Cookie；前端删除 `accessToken`。

> 未登录用户视为 `viewer` 角色，可访问公开只读接口，无需携带任何令牌。

---

## 3. 环境变量

在前端仓库根目录创建 / 更新 `.env.local`：

```bash
# 本地
NEXT_PUBLIC_API_URL=http://127.0.0.1:8787

# Preview 环境（Vercel Preview）
# NEXT_PUBLIC_API_URL=https://api-preview.example.com

# Production
# NEXT_PUBLIC_API_URL=https://api.example.com
```

- 在 CI / Preview 环境可切换到测试域名。
- 前端代码通过 `process.env.NEXT_PUBLIC_API_URL` 读取。

---

## 4. 请求封装示例

以 `fetch` 为例，可在 `utils/request.ts` 统一封装（TypeScript）：

```ts
// utils/request.ts
export async function request<T>(
  path: string,
  options: RequestInit & { skipAuth?: boolean } = {}
): Promise<T> {
  const base = process.env.NEXT_PUBLIC_API_URL!;
  const res = await fetch(`${base}${path}`, {
    credentials: 'include', // ★ 必须
    headers: {
      'Content-Type': 'application/json',
      ...options.headers,
    },
    ...options,
  });

  // 统一错误处理
  if (!res.ok) {
    // 后端保证返回 { error: "..." }
    const err = await res.json<{ error: string }>();

    // 401 → 跳转登录
    if (res.status === 401 && !options.skipAuth) {
      // 避免在非浏览器环境（如 SSR）下执行
      if (typeof window !== 'undefined') {
        window.location.href = '/login';
      }
      return Promise.reject(err);
    }

    // 其余错误抛给调用方
    return Promise.reject(err);
  }

  // 部分接口（如 /logout）可能无 body
  if (res.status === 204) return {} as T;
  return res.json() as Promise<T>;
}
```

调用示例：

```ts
// 登录
await request('/auth/login', {
  method: 'POST',
  body: JSON.stringify({ username, password }),
  skipAuth: true, // 避免 401 时再次跳转
});

// 获取展会列表
const events = await request<Event[]>('/admin/events');
```

---

## 4.1 动态字段 `fields` 示例

```ts
// 仅获取 id、name
await request<Event>(`/events/${id}?fields=id,name`);
```

> 后端会在 Roadmap 0.3.0 落地，当前调用将返回完整对象。

---

## 4.2 上传文件接口（预留）

待 `Upload Service` 上线后，调用示例：

```ts
const form = new FormData();
form.append('file', file);

await request<UploadResp>('/upload', {
  method: 'POST',
  body: form,
  headers: {
    /* Content-Type 由浏览器自动设置 */
  },
});
```

上传成功后后端返回以下结构：

```jsonc
{
  "code": 0,
  "data": {
    "url": "https://cdn.example.com/xxx.png",
  },
  "message": "上传成功",
}
```

---

## 5. 管理前端认证状态 (React 示例)

为了在整个应用中方便地管理和响应用户的登录状态，推荐使用 React Context 创建一个全局的 `AuthProvider`。

### 5.1 创建 AuthContext

在你的前端项目中，可以创建一个类似 `contexts/AuthContext.tsx` 的文件：

```tsx
// contexts/AuthContext.tsx
import {
  createContext,
  useContext,
  useState,
  useEffect,
  ReactNode,
} from 'react';
import { request } from '../utils/request'; // 引入之前封装的 request 函数

interface User {
  id: string;
  username: string;
  role: 'admin' | 'user'; // 新增角色字段
}

interface AuthContextType {
  user: User | null;
  isLoading: boolean;
  login: (credentials: object) => Promise<void>;
  logout: () => Promise<void>;
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

export function AuthProvider({ children }: { children: ReactNode }) {
  const [user, setUser] = useState<User | null>(null);
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    // 应用加载时，检查用户会话
    const checkUserSession = async () => {
      try {
        const currentUser = await request<User>('/auth/me', { skipAuth: true });
        setUser(currentUser);
      } catch (error) {
        setUser(null);
      } finally {
        setIsLoading(false);
      }
    };
    checkUserSession();
  }, []);

  const login = async (credentials: object) => {
    const loggedInUser = await request<User>('/auth/login', {
      method: 'POST',
      body: JSON.stringify(credentials),
      skipAuth: true,
    });
    setUser(loggedInUser);
  };

  const logout = async () => {
    await request('/auth/logout', { method: 'POST' });
    setUser(null);
  };

  const value = { user, isLoading, login, logout };

  return <AuthContext.Provider value={value}>{children}</AuthContext.Provider>;
}

export function useAuth() {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
}
```

### 5.2 在应用根组件使用

在 Next.js 的 `_app.tsx` 或 `layout.tsx` 中，用 `AuthProvider` 包裹你的应用：

```tsx
// pages/_app.tsx or app/layout.tsx
import { AuthProvider } from '../contexts/AuthContext';

function MyApp({ Component, pageProps }) {
  return (
    <AuthProvider>
      <Component {...pageProps} />
    </AuthProvider>
  );
}

export default MyApp;
```

### 5.3 在组件中使用

现在，任何子组件都可以通过 `useAuth` Hook 轻松访问认证状态和方法：

```tsx
import { useAuth } from '../contexts/AuthContext';

function Header() {
  const { user, logout, isLoading } = useAuth();

  if (isLoading) {
    return <div>Loading...</div>;
  }

  return (
    <header>
      {user ? (
        <>
          <span>Welcome, {user.username}!</span>
          <button onClick={logout}>Logout</button>
        </>
      ) : (
        <a href="/login">Login</a>
      )}
    </header>
  );
}
```

---

## 6. API 一览（摘录）

### 6.1 Auth

| Method | Path           | 描述                                        |
| ------ | -------------- | ------------------------------------------- |
| POST   | /auth/login    | 登录，写 Cookie                             |
| POST   | /auth/logout   | 登出，清 Cookie                             |
| POST   | /auth/register | 注册新用户                                  |
| GET    | /auth/me       | 获取当前用户信息（包含 id, username, role） |

### 6.2 Admin 资源（需登录）

| 资源   | 列表                 | 详情                      | 创建                  | 更新                      | 删除                         |
| ------ | -------------------- | ------------------------- | --------------------- | ------------------------- | ---------------------------- |
| Event  | `GET /admin/events`  | `GET /admin/events/{id}`  | `POST /admin/events`  | `PUT /admin/events/{id}`  | `DELETE /admin/events/{id}`  |
| Circle | `GET /admin/circles` | `GET /admin/circles/{id}` | `POST /admin/circles` | `PUT /admin/circles/{id}` | `DELETE /admin/circles/{id}` |
| ...    | ...                  | ...                       | ...                   | ...                       | ...                          |

> 公开可读数据（如展会列表）可通过非 `/admin/` 路径访问，例如 `GET /events`。

---

## 7. 迁移步骤（Next.js → 远程 API）

1. 在 `utils/request.ts` 加入请求封装。
2. **按照 5.1-5.2 的说明，在你的应用中集成 `AuthProvider`。**
3. 删除 / 注释现有 `pages/api/**` 或 `app/api/**` 中只用于 mock 的路由。
4. 在登录页，使用 `useAuth()` 提供的 `login` 方法处理登录逻辑。
5. 在需要退出登录的地方，调用 `useAuth()` 提供的 `logout` 方法。
6. SSR/RSC 请求同样需 `credentials: 'include'` 或手动传递 Cookie：

   ```ts
   // Example for server-side fetch
   import { cookies } from 'next/headers';
   const cookie = cookies().get('auth_session');
   const res = await fetch(`${api}/admin/events`, {
     headers: {
       Cookie: `${cookie?.name}=${cookie?.value}`,
     },
   });
   ```

7. 401 跳转逻辑已在 `request` 函数中处理，`AuthProvider` 会自动更新状态。

---

## 8. 错误格式

| HTTP | 含义             |
| ---- | ---------------- |
| 400  | 参数校验失败     |
| 401  | 未认证或会话失效 |
| 404  | 资源不存在       |
| 409  | 唯一键冲突       |
| 500  | 服务器内部错误   |

> 统一格式：
>
> ```json
> { "error": "name 字段不能为空" }
> ```

---

## 9. 调试 & FAQ

1. **本地开发**：
   ```bash
   wrangler dev # 启动后端本地服务（默认 8787）
   # 前端 .env 指向 http://127.0.0.1:8787
   ```
2. **跨域问题**：
   - 若仍报 CORS，被 fetch preflight 阻止，请确认请求方法 / 头部是否在后端 allowlist。
3. **Cookie 丢失**：
   - 确保前端请求域名与 Set-Cookie 的作用域一致。
   - 本地 `http://localhost` 与 `127.0.0.1` 视为不同域。
   - 必须 `credentials: 'include'`。
4. **日期字段**：后端统一 ISO-8601；若显示不正常，请检查前端 `Date.parse` 时区偏移。

---

## 10. 后续扩展

- RBAC 多角色：可扩展 `auth` 上下文，增加 `roleGuard('editor')` 中间件。
- 分页 / 排序：预留 `page pageSize orderBy` 等查询参数。
- 上传服务：规划 `/upload`（S3 / R2）。

如有疑问，请在 Slack #backend 频道 @后端同学 / 提 Issue。
