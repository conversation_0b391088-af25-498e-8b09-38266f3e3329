import { useQueryClient } from "@tanstack/react-query";

import { queryKeys } from "@/constants/queryKeys";
import { useAdminSuccessToast } from "@/hooks/useAdminSuccessToast";
import { request } from "@/lib/http";

type CircleInput = Record<string, any>;

export function useUpdateCircle(id: string) {
  const qc = useQueryClient();

  return useAdminSuccessToast(
    (payload: CircleInput) =>
      request(`/admin/circles/${id}`, {
        method: "PUT",
        body: JSON.stringify(payload),
      }),
    {
      onSuccess: () => {
        qc.invalidateQueries({
          queryKey: queryKeys.adminCircles(),
        });
        qc.invalidateQueries({
          queryKey: queryKeys.adminCircleDetail(id),
        });
      },
    },
    "修改成功"
  );
} 