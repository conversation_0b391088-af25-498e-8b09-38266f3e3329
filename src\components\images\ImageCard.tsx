/**
 * ImageCard 图片卡片组件
 * 显示单个图片的缩略图、信息和操作按钮
 */

'use client';

import React, { useState } from 'react';
import { Eye, Trash2, Download, Copy, Check } from 'lucide-react';
import { cn } from '@/lib/utils';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { ImageService } from '@/services/imageService';
import type { ImageCardProps } from '@/types/image';

export function ImageCard({
  image,
  selected = false,
  onSelect,
  onPreview,
  onDelete,
  showActions = true,
  className,
}: ImageCardProps) {
  const [imageLoaded, setImageLoaded] = useState(false);
  const [imageError, setImageError] = useState(false);
  const [copied, setCopied] = useState(false);

  // 格式化文件大小
  const formatFileSize = (bytes: number): string => {
    if (bytes === 0) return '0 B';
    const k = 1024;
    const sizes = ['B', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(1)) + ' ' + sizes[i];
  };

  // 格式化日期
  const formatDate = (dateString?: string): string => {
    if (!dateString) return '';
    return new Date(dateString).toLocaleDateString();
  };

  // 复制图片URL
  const handleCopyUrl = async () => {
    const url = ImageService.getUrl(image.relativePath);
    try {
      await navigator.clipboard.writeText(url);
      setCopied(true);
      setTimeout(() => setCopied(false), 2000);
    } catch (error) {
      console.error('Failed to copy URL:', error);
    }
  };

  // 下载图片
  const handleDownload = () => {
    if (!image || !image.relativePath) {
      console.warn('Cannot download: invalid image data', image);
      return;
    }

    const url = ImageService.getUrl(image.relativePath);
    if (!url) {
      console.warn('Cannot download: failed to generate URL', image);
      return;
    }

    const link = document.createElement('a');
    link.href = url;
    link.download = image.relativePath.split('/').pop() || 'image';
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  };

  // 处理选择
  const handleSelect = () => {
    onSelect?.(image, !selected);
  };

  // 验证图片数据
  if (!image || !image.relativePath) {
    console.error('ImageCard: Invalid image data', image);
    return (
      <div className="aspect-square bg-gray-100 rounded-lg flex items-center justify-center">
        <div className="text-gray-400 text-center">
          <div className="text-2xl mb-1">❌</div>
          <div className="text-xs">Invalid Image</div>
        </div>
      </div>
    );
  }

  const thumbnailUrl = ImageService.getThumbnailUrl(image.relativePath);
  const fullUrl = ImageService.getUrl(image.relativePath);

  // 如果URL生成失败，显示错误状态
  if (!thumbnailUrl || !fullUrl) {
    console.error('ImageCard: Failed to generate URLs', { image, thumbnailUrl, fullUrl });
    return (
      <div className="aspect-square bg-gray-100 rounded-lg flex items-center justify-center">
        <div className="text-gray-400 text-center">
          <div className="text-2xl mb-1">🔗</div>
          <div className="text-xs">URL Error</div>
        </div>
      </div>
    );
  }

  return (
    <div
      className={cn(
        'group relative bg-white rounded-lg border shadow-sm overflow-hidden transition-all duration-200',
        'hover:shadow-md hover:border-primary/20',
        selected && 'ring-2 ring-primary border-primary',
        className
      )}
    >
      {/* 选择框 */}
      {onSelect && (
        <div className="absolute top-2 left-2 z-10">
          <input
            type="checkbox"
            checked={selected}
            onChange={handleSelect}
            className="h-4 w-4 rounded border-gray-300 text-primary focus:ring-primary"
          />
        </div>
      )}

      {/* 图片预览 */}
      <div className="relative aspect-square bg-gray-100">
        {!imageError ? (
          <img
            src={thumbnailUrl}
            alt="Image thumbnail"
            className={cn(
              'w-full h-full object-cover transition-opacity duration-200',
              imageLoaded ? 'opacity-100' : 'opacity-0'
            )}
            onLoad={() => setImageLoaded(true)}
            onError={() => setImageError(true)}
            onClick={() => onPreview?.(image)}
          />
        ) : (
          <div className="w-full h-full flex items-center justify-center text-gray-400">
            <div className="text-center">
              <div className="text-2xl mb-1">📷</div>
              <div className="text-xs">Failed to load</div>
            </div>
          </div>
        )}

        {/* 加载状态 */}
        {!imageLoaded && !imageError && (
          <div className="absolute inset-0 flex items-center justify-center">
            <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-primary"></div>
          </div>
        )}

        {/* 悬停操作 */}
        {showActions && (
          <div className="absolute inset-0 bg-black/50 opacity-0 group-hover:opacity-100 transition-opacity duration-200 flex items-center justify-center gap-2">
            <Button
              variant="secondary"
              size="sm"
              onClick={() => onPreview?.(image)}
            >
              <Eye className="h-4 w-4" />
            </Button>
            <Button
              variant="secondary"
              size="sm"
              onClick={handleCopyUrl}
            >
              {copied ? <Check className="h-4 w-4" /> : <Copy className="h-4 w-4" />}
            </Button>
            <Button
              variant="secondary"
              size="sm"
              onClick={handleDownload}
            >
              <Download className="h-4 w-4" />
            </Button>
            {onDelete && (
              <Button
                variant="destructive"
                size="sm"
                onClick={() => onDelete(image)}
              >
                <Trash2 className="h-4 w-4" />
              </Button>
            )}
          </div>
        )}
      </div>

      {/* 图片信息 */}
      <div className="p-3 space-y-2">
        {/* 变体和类型标签 */}
        <div className="flex gap-1 flex-wrap">
          <Badge variant="secondary" className="text-xs">
            {image.variant}
          </Badge>
          <Badge variant="outline" className="text-xs">
            {image.metadata.format.toUpperCase()}
          </Badge>
        </div>

        {/* 尺寸和大小 */}
        <div className="text-xs text-muted-foreground space-y-1">
          <div>
            {image.metadata.dimensions.width} × {image.metadata.dimensions.height}
          </div>
          <div>{formatFileSize(image.metadata.size)}</div>
          {image.createdAt && (
            <div>{formatDate(image.createdAt)}</div>
          )}
        </div>

        {/* 文件路径 */}
        <div className="text-xs text-muted-foreground truncate" title={image.relativePath}>
          {image.relativePath.split('/').pop()}
        </div>
      </div>
    </div>
  );
}
