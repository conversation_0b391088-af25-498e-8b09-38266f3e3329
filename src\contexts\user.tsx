"use client";

import { createContext, useContext, useState, useEffect, ReactNode } from 'react';

import { useAuthStore } from '@/stores/auth';
import { authService } from '@/services/auth';

import type { User } from "@/types/user";

interface AuthContextType {
  user: User | null;
  isLoading: boolean;
  login: (credentials: object) => Promise<void>;
  register: (credentials: object) => Promise<void>; // 添加 register 方法
  logout: () => Promise<void>;
  ready: boolean; // Keep ready for compatibility
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

export function AuthProvider({ children }: { children: ReactNode }) {
  const authStore = useAuthStore();
  const [user, setUser] = useState<User | null>(authStore.user);
  const [isLoading, setIsLoading] = useState(true);

  // 监听 authStore 状态变化，确保 Context 状态与 Store 同步
  useEffect(() => {
    setUser(authStore.user);
  }, [authStore.user, authStore.isAuthenticated]);

  useEffect(() => {
    let mounted = true;

    // 应用加载时，检查用户会话
    const checkUserSession = async () => {
      console.debug('[AuthProvider] 开始检查用户会话');

      // 先检查是否有缓存的用户信息
      const cachedUser = authService.getUser();
      if (cachedUser && mounted) {
        console.debug('[AuthProvider] 使用缓存的用户信息:', cachedUser);
        setUser(cachedUser);
        setIsLoading(false);
        return;
      }

      try {
        // 静默检查认证状态，不在控制台显示401错误
        // 这样公共页面不会有噪音，但仍能恢复已登录用户的状态
        await authStore.checkAuth(true); // silent = true

        // 确保在 checkAuth 完成后再获取最新的用户状态
        if (mounted) {
          console.debug('[AuthProvider] 认证检查完成，用户:', authStore.user);
          setUser(authStore.user);
        }
      } catch (error) {
        // 认证失败时静默处理，不重定向
        // 只清除状态，让各个页面自己决定是否需要认证
        console.debug('[AuthProvider] 认证检查失败:', error);
        if (mounted) {
          setUser(null);
        }
      } finally {
        if (mounted) {
          setIsLoading(false);
          console.debug('[AuthProvider] 认证检查结束，isLoading: false');
        }
      }
    };

    checkUserSession();

    return () => {
      mounted = false;
    };
  }, []); // 移除 authStore 依赖，避免重复执行

  const login = async (credentials: object) => {
    await authStore.login(credentials as any);

    // 登录成功后立即从 authService 获取最新的用户信息
    const loggedInUser = authService.getUser();
    if (loggedInUser) {
      setUser(loggedInUser);
      // 登录成功后标记为已登录
      if (typeof window !== 'undefined') {
        window.localStorage.setItem('isLoggedIn', 'true');
      }
    }
  };

  const register = async (credentials: object) => {
    await authStore.register(credentials as any);
  };

  const logout = async () => {
    await authStore.logout();
    setUser(null);
    // Force redirect to login page after logout
    if (typeof window !== "undefined") {
      window.location.href = "/login";
    }
  };
  
  const value = { user, isLoading, login, register, logout, ready: !isLoading };

  return (
    <AuthContext.Provider value={value}>
      {children}
    </AuthContext.Provider>
  );
}

export function useAuth() {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
} 