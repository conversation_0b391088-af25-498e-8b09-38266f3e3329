/**
 * 表单错误显示组件
 * 基于后端文档包的错误处理规范
 */

'use client';

import React from 'react';
import { useErrorStore } from '@/stores/error';
import { ErrorType } from '@/types/errors';

interface FormErrorProps {
  field?: string;
  className?: string;
}

export function FormError({ field, className = '' }: FormErrorProps) {
  const { errors } = useErrorStore();
  
  const fieldErrors = errors.filter(
    error => error.type === ErrorType.VALIDATION_ERROR && 
             (!field || error.field === field)
  );
  
  if (fieldErrors.length === 0) {
    return null;
  }
  
  return (
    <div className={`text-red-600 text-sm mt-1 ${className}`}>
      {fieldErrors.map((error, index) => (
        <div key={index}>{error.message}</div>
      ))}
    </div>
  );
}
