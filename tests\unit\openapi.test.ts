import { OpenAPIHono } from '@hono/zod-openapi';
import { describe, it, expect } from 'vitest';

// 导入各路由，与 scripts/generate-openapi.ts 保持同步
import { routes as admin } from '@/modules/admin';
import { routes as auth } from '@/modules/auth';
import { routes as bookmarkRoutes } from '@/modules/bookmark';
import { routes as appearanceRoutes } from '@/modules/appearance';
import { routes as artistRoutes } from '@/modules/artist';
import { routes as circleRoutes } from '@/modules/circle';
import { routes as eventRoutes } from '@/modules/event';

function buildSpec() {
  const app = new OpenAPIHono();
  app.route('/auth', auth);
  app.route('/events', eventRoutes);
  app.route('/circles', circleRoutes);
  app.route('/circles', bookmarkRoutes);
  app.route('/artists', artistRoutes);
  app.route('/appearances', appearanceRoutes);
  app.route('/admin', admin);

  return app.getOpenAPIDocument({
    openapi: '3.0.0',
    info: { title: 'Ayafeed API', version: '0.3.0' },
  });
}

describe('OpenAPI generation', () => {
  it('should include /auth/register path', () => {
    const spec = buildSpec();
    expect(spec.paths).toHaveProperty('/auth/register');
    expect(spec.paths['/auth/register']).toHaveProperty('post');
  });

  it('should include /admin/users path', () => {
    const spec = buildSpec();
    expect(spec.paths).toHaveProperty('/admin/users');
    expect(spec.paths['/admin/users']).toHaveProperty('post');
  });

  it('should include bookmark routes', () => {
    const spec = buildSpec();
    expect(spec.paths).toHaveProperty('/circles/{circleId}/bookmark');
    expect(spec.paths['/circles/{circleId}/bookmark']).toHaveProperty('post');
    expect(spec.paths['/circles/{circleId}/bookmark'].post.tags).toContain(
      'Bookmarks'
    );
  });
});
