"use client";

import * as DropdownMenu from "@radix-ui/react-dropdown-menu";
import { Menu, Search, Calendar, Users, Activity, Home } from "lucide-react";
import Link from "next/link";
import { cn } from "@/lib/utils";
import { RadixButton, RadixInput } from "@/components/ui/radix-components";
import { useState } from "react";

import LanguageSwitcher from '@/components/language-switcher';
import UserMenu from '@/components/user-menu';
import { SEARCH_CONFIG, placeholderSearch } from './navbar/search-config';

// 导航项配置 - 优化信息架构
const navigationItems = [
  { href: "/", label: "首页", icon: "Home" },
  { href: "/events", label: "展会", icon: "Calendar" },
  { href: "/circles", label: "社团", icon: "Users" },
  { href: "/feed", label: "动态", icon: "Activity" },
] as const;

// 管理员专用导航项
const adminNavigationItems = [
  { href: "/admin", label: "管理后台", icon: "Settings" },
] as const;

// 导航链接组件
interface NavLinkProps {
  href: string;
  children: React.ReactNode;
  className?: string;
  onClick?: () => void;
}

function NavLink({ href, children, className, onClick }: NavLinkProps) {
  return (
    <Link
      href={href}
      onClick={onClick}
      className={cn(
        "text-sm font-medium transition-colors outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 rounded-sm",
        className
      )}
    >
      {children}
    </Link>
  );
}

// 移动端菜单项组件
function MobileMenuItem({ href, children }: { href: string; children: React.ReactNode }) {
  return (
    <DropdownMenu.Item asChild>
      <NavLink
        href={href}
        className="flex items-center px-3 py-2 rounded-sm hover:bg-accent hover:text-accent-foreground focus:bg-accent focus:text-accent-foreground cursor-pointer"
      >
        {children}
      </NavLink>
    </DropdownMenu.Item>
  );
}

// 搜索组件 - 使用占位符逻辑
function SearchBar() {
  const [searchQuery, setSearchQuery] = useState("");
  const [isSearching, setIsSearching] = useState(false);

  // 如果搜索功能被禁用，不渲染组件
  if (!SEARCH_CONFIG.enabled) {
    return null;
  }

  const handleSearch = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!searchQuery.trim()) return;

    setIsSearching(true);

    try {
      console.log('🔍 搜索查询:', searchQuery.trim());

      if (SEARCH_CONFIG.usePlaceholder) {
        // 使用占位符搜索逻辑
        const searchResult = await placeholderSearch(searchQuery.trim());

        const resultText = searchResult.results.length > 0
          ? `找到 ${searchResult.total} 个相关结果:\n\n${searchResult.results.map(r => r.title).join('\n')}`
          : '未找到相关结果';

        alert(`🔍 搜索结果 (演示)\n查询: "${searchQuery.trim()}"\n\n${resultText}\n\n💡 提示: 搜索功能正在开发中，这只是演示效果`);
      } else {
        // TODO: 调用真实的搜索 API
        // const result = await realSearch({ query: searchQuery.trim() });
        alert('真实搜索 API 尚未实现');
      }

      // 清空搜索框
      setSearchQuery("");

    } catch (error) {
      console.error('搜索错误:', error);
      alert('搜索功能暂时不可用');
    } finally {
      setIsSearching(false);
    }
  };

  const displayClass = SEARCH_CONFIG.showMobileSearch
    ? "flex items-center"
    : "hidden md:flex items-center";

  return (
    <form onSubmit={handleSearch} className={displayClass}>
      <div className="relative group">
        <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-white/60 group-focus-within:text-white/80 transition-colors" />
        <RadixInput
          type="search"
          placeholder={SEARCH_CONFIG.placeholder}
          value={searchQuery}
          onChange={(e) => setSearchQuery(e.target.value)}
          disabled={isSearching}
          className={`pl-10 pr-12 py-2.5 ${SEARCH_CONFIG.maxWidth} bg-white/10 border border-white/20 text-white placeholder:text-white/50 focus:bg-white/15 focus:border-white/40 focus:ring-2 focus:ring-white/20 disabled:opacity-50 rounded-lg shadow-sm transition-all`}
        />
        {isSearching && (
          <div className="absolute right-3 top-1/2 transform -translate-y-1/2">
            <div className="animate-spin h-4 w-4 border-2 border-white/30 border-t-white rounded-full"></div>
          </div>
        )}
      </div>
    </form>
  );
}

// Logo 组件 - 改进品牌设计
function Logo() {
  return (
    <NavLink
      href="/"
      className="flex items-center gap-3 text-white hover:text-white/90 transition-colors group"
    >
      <div className="w-9 h-9 bg-gradient-to-br from-blue-400 to-blue-600 rounded-lg flex items-center justify-center shadow-lg group-hover:shadow-blue-500/25 transition-all">
        <Activity className="h-5 w-5 text-white" />
      </div>
      <span className="text-xl font-bold tracking-tight">Ayafeed</span>
    </NavLink>
  );
}

// 图标映射
const iconMap = {
  Home,
  Calendar,
  Users,
  Activity,
} as const;

// 桌面端导航组件 - 添加图标和改进样式
function DesktopNavigation() {
  return (
    <div className="hidden gap-2 md:flex">
      {navigationItems.map(({ href, label, icon }) => {
        const IconComponent = iconMap[icon as keyof typeof iconMap];
        return (
          <NavLink
            key={href}
            href={href}
            className="flex items-center gap-2 px-4 py-2.5 rounded-lg text-white/80 hover:text-white hover:bg-white/10 transition-all font-medium text-sm relative group"
          >
            <IconComponent className="h-4 w-4 group-hover:scale-110 transition-transform" />
            {label}
            <div className="absolute inset-x-0 bottom-0 h-0.5 bg-white/0 group-hover:bg-white/30 transition-all rounded-full"></div>
          </NavLink>
        );
      })}
    </div>
  );
}

// 移动端菜单组件
function MobileMenu() {
  return (
    <DropdownMenu.Root>
      <DropdownMenu.Trigger asChild>
        <RadixButton
          variant="ghost"
          size="icon"
          className="md:hidden text-white hover:bg-white/10"
          aria-label="打开菜单"
        >
          <Menu className="h-5 w-5" />
        </RadixButton>
      </DropdownMenu.Trigger>
      <DropdownMenu.Portal>
        <DropdownMenu.Content
          align="start"
          className="min-w-[220px] bg-background border border-border rounded-md p-1 shadow-lg md:hidden z-50"
          sideOffset={5}
        >
          {navigationItems.map(({ href, label }) => (
            <MobileMenuItem key={href} href={href}>
              {label}
            </MobileMenuItem>
          ))}
        </DropdownMenu.Content>
      </DropdownMenu.Portal>
    </DropdownMenu.Root>
  );
}

// 右侧操作区组件
function NavActions() {
  return (
    <div className="flex items-center gap-3">
      <LanguageSwitcher />
      <MobileMenu />
      <UserMenu />
    </div>
  );
}

export default function Navbar() {
  return (
    <nav className="sticky top-0 z-50 w-full border-b border-slate-700/50 bg-slate-900/95 backdrop-blur supports-backdrop-blur:bg-slate-900/90 shadow-lg">
      <div className="max-w-7xl mx-auto px-4 md:px-6 lg:px-8">
        <div className="flex h-16 items-center justify-between">
          {/* 左侧：Logo */}
          <div className="flex-shrink-0">
            <Logo />
          </div>

          {/* 中间：搜索栏（桌面端） */}
          <div className="flex-1 max-w-2xl mx-8 hidden md:block">
            <SearchBar />
          </div>

          {/* 右侧：导航和操作 */}
          <div className="flex items-center gap-8">
            <DesktopNavigation />
            <div className="flex items-center gap-3 border-l border-white/10 pl-6">
              <NavActions />
            </div>
          </div>
        </div>
      </div>
    </nav>
  );
}