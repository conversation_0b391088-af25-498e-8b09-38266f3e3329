import { Button } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import { Search, Users, RefreshCw } from "lucide-react";

interface CirclesEmptyStateProps {
  type: "no-results" | "no-data" | "error";
  searchKeyword?: string;
  onReset?: () => void;
  onRetry?: () => void;
}

export default function CirclesEmptyState({
  type,
  searchKeyword,
  onReset,
  onRetry,
}: CirclesEmptyStateProps) {
  const getEmptyStateContent = () => {
    switch (type) {
      case "no-results":
        return {
          icon: <Search className="h-12 w-12 text-muted-foreground" />,
          title: "没有找到相关社团",
          description: searchKeyword
            ? `没有找到包含 "${searchKeyword}" 的社团`
            : "没有找到符合筛选条件的社团",
          action: onReset && (
            <Button onClick={onReset} variant="outline">
              清除筛选条件
            </Button>
          ),
        };
      case "no-data":
        return {
          icon: <Users className="h-12 w-12 text-muted-foreground" />,
          title: "暂无社团数据",
          description: "目前还没有社团信息，请稍后再试",
          action: onRetry && (
            <Button onClick={onRetry} variant="outline">
              <RefreshCw className="h-4 w-4 mr-2" />
              重新加载
            </Button>
          ),
        };
      case "error":
        return {
          icon: <RefreshCw className="h-12 w-12 text-destructive" />,
          title: "加载失败",
          description: "无法加载社团列表，请检查网络连接后重试",
          action: onRetry && (
            <Button onClick={onRetry}>
              <RefreshCw className="h-4 w-4 mr-2" />
              重新加载
            </Button>
          ),
        };
      default:
        return {
          icon: <Users className="h-12 w-12 text-muted-foreground" />,
          title: "暂无数据",
          description: "暂时没有可显示的内容",
          action: null,
        };
    }
  };

  const { icon, title, description, action } = getEmptyStateContent();

  return (
    <Card className="w-full">
      <CardContent className="flex flex-col items-center justify-center py-16 px-6 text-center">
        <div className="mb-4">{icon}</div>
        <h3 className="text-lg font-semibold mb-2">{title}</h3>
        <p className="text-muted-foreground mb-6 max-w-md">{description}</p>
        {action && <div>{action}</div>}
      </CardContent>
    </Card>
  );
}
