import { describe, test, expect, vi, beforeEach } from 'vitest'
import { render, screen, fireEvent, waitFor } from '@testing-library/react'
import userEvent from '@testing-library/user-event'
import { renderWithProviders } from '@/__test__/test-utils'
import CircleTextCard from '../circle-text-card'

// Mock next/image
vi.mock('next/image', () => ({
  default: (props: any) => {
    // eslint-disable-next-line jsx-a11y/alt-text, @next/next/no-img-element
    return <img {...props} />;
  },
}));

// Mock next/link
vi.mock('next/link', () => ({
  default: ({ children, href, ...props }: any) => (
    <a href={href} {...props}>
      {children}
    </a>
  ),
}));

// Sample test data
const sampleData = {
  id: "1",
  circle_name: "幻想郷文楽団",
  booth_id: "あ25a",
  artist_name: "<PERSON><PERSON>",
  circle_urls: JSON.stringify({ 
    twitter: "https://x.com/xyz", 
    web: "https://example.com",
    logo_url: "/logo.png" 
  }),
  category: "music",
  circle_id: "circle-123",
  artist_id: "artist-456",
  created_at: "2024-01-01T00:00:00Z",
  updated_at: "2024-01-02T00:00:00Z",
};

const sampleDataWithoutUrls = {
  ...sampleData,
  circle_urls: "{}",
};

const sampleDataWithInvalidUrls = {
  ...sampleData,
  circle_urls: "invalid json",
};

describe('CircleTextCard (Radix Migration)', () => {
  beforeEach(() => {
    vi.clearAllMocks()
  })

  test('renders basic circle information', () => {
    renderWithProviders(<CircleTextCard data={sampleData} />)
    
    // 检查基本信息
    expect(screen.getByText('幻想郷文楽団')).toBeInTheDocument()
    expect(screen.getByText('あ25a')).toBeInTheDocument()
    expect(screen.getByText('ZUN')).toBeInTheDocument()
    expect(screen.getByText('音乐')).toBeInTheDocument()
  })

  test('renders circle logo with correct attributes', () => {
    renderWithProviders(<CircleTextCard data={sampleData} />)
    
    const logo = screen.getByAltText('幻想郷文楽団 logo')
    expect(logo).toBeInTheDocument()
    expect(logo).toHaveAttribute('src', '/logo.png')
    expect(logo).toHaveAttribute('width', '32')
    expect(logo).toHaveAttribute('height', '32')
  })

  test('uses fallback logo when logo_url is not provided', () => {
    const dataWithoutLogo = {
      ...sampleData,
      circle_urls: JSON.stringify({ twitter: "https://x.com/xyz" })
    }
    renderWithProviders(<CircleTextCard data={dataWithoutLogo} />)
    
    const logo = screen.getByAltText('幻想郷文楽団 logo')
    expect(logo).toHaveAttribute('src', '/globe.svg')
  })

  test('renders category label correctly', () => {
    renderWithProviders(<CircleTextCard data={sampleData} />)
    
    // 检查分类标签显示中文
    expect(screen.getByText('音乐')).toBeInTheDocument()
  })

  test('handles unknown category gracefully', () => {
    const dataWithUnknownCategory = {
      ...sampleData,
      category: "unknown_category"
    }
    renderWithProviders(<CircleTextCard data={dataWithUnknownCategory} />)
    
    // 应该显示原始分类名
    expect(screen.getByText('unknown_category')).toBeInTheDocument()
  })

  test('opens dialog when card is clicked', async () => {
    const user = userEvent.setup()
    renderWithProviders(<CircleTextCard data={sampleData} />)
    
    // 点击卡片
    const card = screen.getByTestId('circle-card')
    await user.click(card)
    
    // 检查对话框内容
    await waitFor(() => {
      expect(screen.getByText('社团相关链接')).toBeInTheDocument()
    })
  })

  test('displays links in dialog when opened', async () => {
    const user = userEvent.setup()
    renderWithProviders(<CircleTextCard data={sampleData} />)
    
    // 点击卡片打开对话框
    const card = screen.getByTestId('circle-card')
    await user.click(card)
    
    // 等待对话框打开并检查链接
    await waitFor(() => {
      expect(screen.getByRole('link', { name: 'twitter' })).toHaveAttribute('href', 'https://x.com/xyz')
      expect(screen.getByRole('link', { name: 'web' })).toHaveAttribute('href', 'https://example.com')
    })
  })

  test('shows "no links" message when no URLs provided', async () => {
    const user = userEvent.setup()
    renderWithProviders(<CircleTextCard data={sampleDataWithoutUrls} />)
    
    // 点击卡片打开对话框
    const card = screen.getByTestId('circle-card')
    await user.click(card)
    
    // 检查无链接信息的提示
    await waitFor(() => {
      expect(screen.getByText('暂无链接信息')).toBeInTheDocument()
    })
  })

  test('handles invalid JSON URLs gracefully', async () => {
    const user = userEvent.setup()
    renderWithProviders(<CircleTextCard data={sampleDataWithInvalidUrls} />)
    
    // 点击卡片打开对话框
    const card = screen.getByTestId('circle-card')
    await user.click(card)
    
    // 应该显示无链接信息
    await waitFor(() => {
      expect(screen.getByText('暂无链接信息')).toBeInTheDocument()
    })
  })

  test('renders detail link with correct href', async () => {
    const user = userEvent.setup()
    renderWithProviders(<CircleTextCard data={sampleData} />)
    
    // 点击卡片打开对话框
    const card = screen.getByTestId('circle-card')
    await user.click(card)
    
    // 检查查看详情链接
    await waitFor(() => {
      const detailLink = screen.getByRole('link', { name: '查看详情' })
      expect(detailLink).toHaveAttribute('href', '/circles/1')
    })
  })

  test('renders detail link with circle_id when id is not available', async () => {
    const user = userEvent.setup()
    const dataWithoutId = { ...sampleData }
    delete (dataWithoutId as any).id
    
    renderWithProviders(<CircleTextCard data={dataWithoutId} />)
    
    // 点击卡片打开对话框
    const card = screen.getByTestId('circle-card')
    await user.click(card)
    
    // 检查查看详情链接使用 circle_id
    await waitFor(() => {
      const detailLink = screen.getByRole('link', { name: '查看详情' })
      expect(detailLink).toHaveAttribute('href', '/circles/circle-123')
    })
  })

  test('renders follow button as disabled', async () => {
    const user = userEvent.setup()
    renderWithProviders(<CircleTextCard data={sampleData} />)
    
    // 点击卡片打开对话框
    const card = screen.getByTestId('circle-card')
    await user.click(card)
    
    // 检查关注按钮是禁用的
    await waitFor(() => {
      const followButton = screen.getByRole('button', { name: '关注（待实现）' })
      expect(followButton).toBeDisabled()
    })
  })

  test('handles missing optional fields gracefully', () => {
    const minimalData = {
      circle_name: "Test Circle",
      circle_urls: "{}",
      circle_id: "test-123",
      artist_id: "artist-123",
      booth_id: "",
      artist_name: null,
      category: "",
    }

    renderWithProviders(<CircleTextCard data={minimalData} />)

    // 应该只显示社团名称
    expect(screen.getByText('Test Circle')).toBeInTheDocument()
    // 检查卡片是否正确渲染
    expect(screen.getByTestId('circle-card')).toBeInTheDocument()
  })

  test('dialog can be closed with close button', async () => {
    const user = userEvent.setup()
    renderWithProviders(<CircleTextCard data={sampleData} />)
    
    // 点击卡片打开对话框
    const card = screen.getByTestId('circle-card')
    await user.click(card)
    
    // 等待对话框打开
    await waitFor(() => {
      expect(screen.getByText('社团相关链接')).toBeInTheDocument()
    })
    
    // 点击关闭按钮
    const closeButton = screen.getByRole('button', { name: 'Close' })
    await user.click(closeButton)
    
    // 对话框应该关闭
    await waitFor(() => {
      expect(screen.queryByText('社团相关链接')).not.toBeInTheDocument()
    })
  })

  test('external links have correct attributes', async () => {
    const user = userEvent.setup()
    renderWithProviders(<CircleTextCard data={sampleData} />)
    
    // 点击卡片打开对话框
    const card = screen.getByTestId('circle-card')
    await user.click(card)
    
    // 检查外部链接属性
    await waitFor(() => {
      const twitterLink = screen.getByRole('link', { name: 'twitter' })
      expect(twitterLink).toHaveAttribute('target', '_blank')
      expect(twitterLink).toHaveAttribute('rel', 'noopener noreferrer')
    })
  })

  test('component renders without crashing', () => {
    const { container } = renderWithProviders(<CircleTextCard data={sampleData} />)
    expect(container.firstChild).toBeInTheDocument()
  })

  test('card has correct styling classes', () => {
    renderWithProviders(<CircleTextCard data={sampleData} />)
    
    const card = screen.getByTestId('circle-card')
    expect(card).toHaveClass('w-full', 'cursor-pointer', 'transition-shadow', 'hover:shadow-md')
  })

  test('dialog title matches circle name', async () => {
    const user = userEvent.setup()
    renderWithProviders(<CircleTextCard data={sampleData} />)

    // 点击卡片打开对话框
    const card = screen.getByTestId('circle-card')
    await user.click(card)

    // 检查对话框标题 - 使用更具体的选择器
    await waitFor(() => {
      expect(screen.getByRole('dialog')).toBeInTheDocument()
      // 检查对话框中的标题，而不是卡片中的标题
      const dialogTitle = screen.getByRole('dialog').querySelector('h2')
      expect(dialogTitle).toHaveTextContent('幻想郷文楽団')
    })
  })
})
