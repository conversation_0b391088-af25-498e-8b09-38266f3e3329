import { describe, it, expect, vi, beforeEach } from 'vitest';
import type { Context } from 'hono';
import * as feedService from '@/modules/feed/service';
import { getDB } from '@/infrastructure';
import { jsonError } from '@/utils/errorResponse';

// Copy the handler function for unit testing
async function getFeedHandler(c: Context) {
  const db = getDB(c);
  const cache = c.get('cache');
  const logger = c.get('logger');
  const locale = c.get('locale') || 'en';

  const searchParams = new URL(c.req.url).searchParams;
  const page = Math.max(Number(searchParams.get('page') || '1'), 1);
  const limit = Math.min(
    Math.max(Number(searchParams.get('limit') || '20'), 1),
    100
  );
  const type =
    (searchParams.get('type') as 'all' | 'events' | 'circles') || 'all';

  try {
    const { items, total } = await feedService.getFeedData(
      db,
      page,
      limit,
      type,
      locale,
      cache,
      logger
    );

    return c.json({
      success: true,
      data: items,
      locale,
      timestamp: new Date().toISOString(),
      meta: {
        total,
        page,
        limit,
        hasMore: items.length === limit && page * limit < total,
      },
    });
  } catch (error) {
    logger?.error?.('getFeedHandler error', {
      error,
      page,
      limit,
      type,
      locale,
    });
    return jsonError(c, 50002, 'Feed fetch failed', 500);
  }
}

// Mock dependencies
vi.mock('@/modules/feed/service');
vi.mock('@/infrastructure');
vi.mock('@/utils/errorResponse');

// Helper function to create mock context
const createMockContext = (overrides: any = {}) => {
  const mockDB = { prepare: vi.fn() };
  const mockCache = { get: vi.fn(), set: vi.fn() };
  const mockLogger = {
    debug: vi.fn(),
    info: vi.fn(),
    warn: vi.fn(),
    error: vi.fn(),
  };

  return {
    req: {
      url: 'http://localhost/',
      ...overrides.req,
    },
    get: vi.fn((key: string) => {
      if (key === 'cache') {
        return overrides.hasOwnProperty('cache') ? overrides.cache : mockCache;
      }
      if (key === 'logger') {
        return overrides.hasOwnProperty('logger')
          ? overrides.logger
          : mockLogger;
      }
      if (key === 'locale') return overrides.locale || 'en';
      return overrides[key] || null;
    }),
    json: vi.fn((data) => ({
      status: 200,
      json: async () => data,
    })),
    env: { DB: mockDB },
    _mockDB: mockDB,
    _mockCache: mockCache,
    _mockLogger: mockLogger,
    ...overrides,
  };
};

describe('feed/routes', () => {
  beforeEach(() => {
    vi.clearAllMocks();
    (getDB as any).mockImplementation((c) => c.env.DB);
    (jsonError as any).mockReturnValue({
      status: 500,
      json: async () => ({ code: 50002, message: 'Feed fetch failed' }),
    });
  });

  describe('getFeedHandler', () => {
    it('should return feed data with default parameters', async () => {
      const mockFeedData = {
        items: [
          {
            id: 'feed-event-1',
            type: 'event' as const,
            content: {
              id: '1',
              name: 'Test Event',
              description: 'Test Description',
              start_date: '2024-01-01T00:00:00Z',
              image_url: 'test.jpg',
            },
            created_at: '2024-01-01T00:00:00Z',
          },
        ],
        total: 1,
      };

      (feedService.getFeedData as any).mockResolvedValue(mockFeedData);

      const mockContext = createMockContext({
        req: { url: 'http://localhost/' },
      });

      await getFeedHandler(mockContext as any);

      expect(mockContext.json).toHaveBeenCalledWith({
        success: true,
        data: mockFeedData.items,
        locale: 'en',
        timestamp: expect.any(String),
        meta: {
          total: 1,
          page: 1,
          limit: 20,
          hasMore: false,
        },
      });

      expect(feedService.getFeedData).toHaveBeenCalledWith(
        mockContext._mockDB,
        1, // page
        20, // limit
        'all', // type
        'en', // locale
        mockContext._mockCache,
        mockContext._mockLogger
      );
    });

    it('should handle custom page and limit parameters', async () => {
      const mockFeedData = {
        items: Array.from({ length: 10 }, (_, i) => ({
          id: `feed-event-${i + 1}`,
          type: 'event' as const,
          content: {
            id: `${i + 1}`,
            name: `Test Event ${i + 1}`,
            description: 'Test Description',
            start_date: '2024-01-01T00:00:00Z',
            image_url: 'test.jpg',
          },
          created_at: '2024-01-01T00:00:00Z',
        })),
        total: 100,
      };

      (feedService.getFeedData as any).mockResolvedValue(mockFeedData);

      const mockContext = createMockContext({
        req: { url: 'http://localhost/?page=2&limit=10' },
      });

      await getFeedHandler(mockContext as any);

      expect(feedService.getFeedData).toHaveBeenCalledWith(
        mockContext._mockDB,
        2, // page
        10, // limit
        'all',
        'en',
        mockContext._mockCache,
        mockContext._mockLogger
      );

      expect(mockContext.json).toHaveBeenCalledWith(
        expect.objectContaining({
          meta: expect.objectContaining({
            total: 100,
            page: 2,
            limit: 10,
            hasMore: true, // 2 * 10 = 20 < 100
          }),
        })
      );
    });

    it('should handle different content types', async () => {
      const mockFeedData = { items: [], total: 0 };
      (feedService.getFeedData as any).mockResolvedValue(mockFeedData);

      const testCases = ['events', 'circles', 'all'];

      for (const type of testCases) {
        const mockContext = createMockContext({
          req: { url: `http://localhost/?type=${type}` },
        });

        await getFeedHandler(mockContext as any);

        expect(feedService.getFeedData).toHaveBeenCalledWith(
          mockContext._mockDB,
          1,
          20,
          type,
          'en',
          mockContext._mockCache,
          mockContext._mockLogger
        );

        vi.clearAllMocks();
        (feedService.getFeedData as any).mockResolvedValue(mockFeedData);
      }
    });

    it('should handle different locales', async () => {
      const mockFeedData = { items: [], total: 0 };
      (feedService.getFeedData as any).mockResolvedValue(mockFeedData);

      const mockContext = createMockContext({
        req: { url: 'http://localhost/' },
        locale: 'ja',
      });

      await getFeedHandler(mockContext as any);

      expect(feedService.getFeedData).toHaveBeenCalledWith(
        mockContext._mockDB,
        1,
        20,
        'all',
        'ja', // locale
        mockContext._mockCache,
        mockContext._mockLogger
      );

      expect(mockContext.json).toHaveBeenCalledWith(
        expect.objectContaining({
          locale: 'ja',
        })
      );
    });

    it('should default to "en" locale when not set', async () => {
      const mockFeedData = { items: [], total: 0 };
      (feedService.getFeedData as any).mockResolvedValue(mockFeedData);

      const mockContext = createMockContext({
        req: { url: 'http://localhost/' },
        locale: null, // No locale set
      });

      await getFeedHandler(mockContext as any);

      expect(feedService.getFeedData).toHaveBeenCalledWith(
        mockContext._mockDB,
        1,
        20,
        'all',
        'en', // default locale
        mockContext._mockCache,
        mockContext._mockLogger
      );
    });

    it('should enforce minimum page value of 1', async () => {
      const mockFeedData = { items: [], total: 0 };
      (feedService.getFeedData as any).mockResolvedValue(mockFeedData);

      const mockContext = createMockContext({
        req: { url: 'http://localhost/?page=0' },
      });

      await getFeedHandler(mockContext as any);

      expect(feedService.getFeedData).toHaveBeenCalledWith(
        mockContext._mockDB,
        1, // Math.max(0, 1) = 1
        20,
        'all',
        'en',
        mockContext._mockCache,
        mockContext._mockLogger
      );
    });

    it('should enforce limit bounds (1-100)', async () => {
      const mockFeedData = { items: [], total: 0 };
      (feedService.getFeedData as any).mockResolvedValue(mockFeedData);

      // Test minimum limit
      const mockContext1 = createMockContext({
        req: { url: 'http://localhost/?limit=0' },
      });

      await getFeedHandler(mockContext1 as any);

      expect(feedService.getFeedData).toHaveBeenCalledWith(
        mockContext1._mockDB,
        1,
        1, // Math.min(Math.max(0, 1), 100) = 1
        'all',
        'en',
        mockContext1._mockCache,
        mockContext1._mockLogger
      );

      vi.clearAllMocks();
      (feedService.getFeedData as any).mockResolvedValue(mockFeedData);

      // Test maximum limit
      const mockContext2 = createMockContext({
        req: { url: 'http://localhost/?limit=200' },
      });

      await getFeedHandler(mockContext2 as any);

      expect(feedService.getFeedData).toHaveBeenCalledWith(
        mockContext2._mockDB,
        1,
        100, // Math.min(Math.max(200, 1), 100) = 100
        'all',
        'en',
        mockContext2._mockCache,
        mockContext2._mockLogger
      );
    });

    it('should work without cache and logger', async () => {
      const mockFeedData = { items: [], total: 0 };
      (feedService.getFeedData as any).mockResolvedValue(mockFeedData);

      const mockContext = createMockContext({
        req: { url: 'http://localhost/' },
        cache: undefined, // No cache
        logger: undefined, // No logger
      });

      await getFeedHandler(mockContext as any);

      expect(feedService.getFeedData).toHaveBeenCalledWith(
        mockContext._mockDB,
        1,
        20,
        'all',
        'en',
        undefined, // cache
        undefined // logger
      );
    });

    it('should handle service errors and return 500', async () => {
      const serviceError = new Error('Database connection failed');
      (feedService.getFeedData as any).mockRejectedValue(serviceError);

      const mockContext = createMockContext({
        req: { url: 'http://localhost/' },
      });

      const result = await getFeedHandler(mockContext as any);

      expect(result.status).toBe(500);
      expect(mockContext._mockLogger.error).toHaveBeenCalledWith(
        'getFeedHandler error',
        {
          error: serviceError,
          page: 1,
          limit: 20,
          type: 'all',
          locale: 'en',
        }
      );
      expect(jsonError).toHaveBeenCalledWith(
        mockContext,
        50002,
        'Feed fetch failed',
        500
      );
    });

    it('should handle service errors without logger gracefully', async () => {
      const serviceError = new Error('Database connection failed');
      (feedService.getFeedData as any).mockRejectedValue(serviceError);

      const mockContext = createMockContext({
        req: { url: 'http://localhost/' },
        logger: undefined, // No logger
      });

      const result = await getFeedHandler(mockContext as any);

      expect(result.status).toBe(500);
      // Should not throw error when logger is undefined
      expect(jsonError).toHaveBeenCalledWith(
        mockContext,
        50002,
        'Feed fetch failed',
        500
      );
    });

    it('should calculate hasMore correctly for last page', async () => {
      const mockFeedData = {
        items: Array.from({ length: 5 }, (_, i) => ({
          id: `feed-event-${i + 1}`,
          type: 'event' as const,
          content: {
            id: `${i + 1}`,
            name: `Test Event ${i + 1}`,
            description: 'Test Description',
            start_date: '2024-01-01T00:00:00Z',
            image_url: 'test.jpg',
          },
          created_at: '2024-01-01T00:00:00Z',
        })),
        total: 25, // Total 25 items
      };

      (feedService.getFeedData as any).mockResolvedValue(mockFeedData);

      const mockContext = createMockContext({
        req: { url: 'http://localhost/?page=5&limit=5' }, // Last page: 5 * 5 = 25
      });

      await getFeedHandler(mockContext as any);

      expect(mockContext.json).toHaveBeenCalledWith(
        expect.objectContaining({
          meta: expect.objectContaining({
            hasMore: false, // 5 === 5 && 25 >= 25 = false
          }),
        })
      );
    });

    it('should handle invalid query parameters gracefully', async () => {
      const mockFeedData = { items: [], total: 0 };
      (feedService.getFeedData as any).mockResolvedValue(mockFeedData);

      const mockContext = createMockContext({
        req: {
          url: 'http://localhost/?page=invalid&limit=invalid&type=invalid',
        },
      });

      await getFeedHandler(mockContext as any);

      // The actual behavior: Number('invalid') = NaN, and NaN is passed through
      expect(feedService.getFeedData).toHaveBeenCalledWith(
        mockContext._mockDB,
        NaN, // Number('invalid') = NaN
        NaN, // Number('invalid') = NaN
        'invalid', // invalid type is passed through
        'en',
        mockContext._mockCache,
        mockContext._mockLogger
      );
    });

    it('should handle negative page numbers', async () => {
      const mockFeedData = { items: [], total: 0 };
      (feedService.getFeedData as any).mockResolvedValue(mockFeedData);

      const mockContext = createMockContext({
        req: { url: 'http://localhost/?page=-5' },
      });

      await getFeedHandler(mockContext as any);

      expect(feedService.getFeedData).toHaveBeenCalledWith(
        mockContext._mockDB,
        1, // Math.max(-5, 1) = 1
        20,
        'all',
        'en',
        mockContext._mockCache,
        mockContext._mockLogger
      );
    });

    it('should handle empty query string', async () => {
      const mockFeedData = { items: [], total: 0 };
      (feedService.getFeedData as any).mockResolvedValue(mockFeedData);

      const mockContext = createMockContext({
        req: { url: 'http://localhost/' }, // No query parameters
      });

      await getFeedHandler(mockContext as any);

      expect(feedService.getFeedData).toHaveBeenCalledWith(
        mockContext._mockDB,
        1, // default page
        20, // default limit
        'all', // default type
        'en',
        mockContext._mockCache,
        mockContext._mockLogger
      );
    });

    it('should include timestamp in response', async () => {
      const mockFeedData = { items: [], total: 0 };
      (feedService.getFeedData as any).mockResolvedValue(mockFeedData);

      const mockContext = createMockContext({
        req: { url: 'http://localhost/' },
      });

      await getFeedHandler(mockContext as any);

      expect(mockContext.json).toHaveBeenCalledWith(
        expect.objectContaining({
          timestamp: expect.stringMatching(
            /^\d{4}-\d{2}-\d{2}T\d{2}:\d{2}:\d{2}\.\d{3}Z$/
          ),
        })
      );
    });
  });
});
