import { describe, it, expect } from 'vitest';

import app from '@/app';

// 简易内存数据库 Mock
const bookmarks: any[] = [];

const mockDB = {
  prepare: (query: string) => {
    // AuthService: 清理过期 session
    if (query.startsWith('DELETE FROM auth_session')) {
      return {
        bind: () => ({ run: async () => ({}) }),
      };
    }

    // AuthService: 验证 session，返回固定用户
    if (
      query.trim().startsWith('SELECT') &&
      query.includes('FROM auth_session')
    ) {
      return {
        bind: () => ({
          all: async () => ({
            results: [{ id: 'user-1', username: 'tester', role: 'viewer' }],
          }),
        }),
      };
    }

    // Bookmarks: 查询是否已收藏
    if (query.startsWith('SELECT id FROM bookmarks')) {
      return {
        bind: (userId: string, circleId: string) => ({
          first: async () =>
            bookmarks.find(
              (b) => b.user_id === userId && b.circle_id === circleId
            ) ?? undefined,
        }),
      };
    }

    // Bookmarks: 删除
    if (query.startsWith('DELETE FROM bookmarks')) {
      return {
        bind: (userId: string, circleId: string) => ({
          run: async () => {
            const idx = bookmarks.findIndex(
              (b) => b.user_id === userId && b.circle_id === circleId
            );
            if (idx > -1) bookmarks.splice(idx, 1);
            return {};
          },
        }),
      };
    }

    // Bookmarks: 插入
    if (query.startsWith('INSERT INTO bookmarks')) {
      return {
        bind: (_id: string, userId: string, circleId: string) => ({
          run: async () => {
            bookmarks.push({ user_id: userId, circle_id: circleId });
            return {};
          },
        }),
      };
    }

    // fallback
    return {
      bind: () => ({
        run: async () => ({}),
        all: async () => ({ results: [] }),
        first: async () => undefined,
      }),
    };
  },
};

// @ts-ignore global polyfill
const Request = globalThis.Request;

function withEnv(url: string, init?: RequestInit & { cookie?: string }) {
  const base = url.startsWith('http') ? url : `http://localhost${url}`;
  const headers = new Headers(init?.headers);
  if (init?.cookie) headers.append('Cookie', init.cookie);
  const req = new Request(base, { method: init?.method ?? 'POST', headers });
  return app.fetch(req, { DB: mockDB });
}

describe('Bookmarks API', () => {
  it('should return 401 when not logged in', async () => {
    const res = await withEnv('/circles/123/bookmark');
    expect(res.status).toBe(401);
  });

  it('should toggle bookmark status', async () => {
    const cookie = 'auth_session=test';

    // 第一次：收藏
    let res = await withEnv('/circles/123/bookmark', { cookie });
    expect(res.status).toBe(200);
    let json = (await res.json()) as any;
    expect(json.data.isBookmarked).toBe(true);

    // 第二次：取消收藏
    res = await withEnv('/circles/123/bookmark', { cookie });
    expect(res.status).toBe(200);
    json = (await res.json()) as any;
    expect(json.data.isBookmarked).toBe(false);
  });
});
