"use client";

import { useRouter } from "next/navigation";
import { useState, type ChangeEvent, type FormEvent } from "react";

import { Button } from "@/components/ui/button";
import { useCreateUser } from "@/hooks/admin/useCreateUser";

interface NewUserForm {
  username: string;
  password: string;
  role: "admin" | "editor" | "viewer";
}

export default function NewUserPage() {
  const router = useRouter();
  const createUser = useCreateUser();
  const [form, setForm] = useState<NewUserForm>({
    username: "",
    password: "",
    role: "viewer",
  });
  const [error, setError] = useState<string | null>(null);

  function handleChange(e: ChangeEvent<HTMLInputElement | HTMLSelectElement>) {
    setForm({ ...form, [e.target.name]: e.target.value });
  }

  function handleSubmit(e: FormEvent) {
    e.preventDefault();
    createUser.mutate(form, {
      onSuccess: () => { router.push("/admin/users"); },
      onError: (err: any) => { setError(err?.message ?? "创建失败"); },
    });
  }

  return (
    <div className="max-w-xl space-y-4">
      <h1 className="text-2xl font-bold">新增用户</h1>
      {error && <p className="text-destructive text-sm">{error}</p>}
      <form onSubmit={handleSubmit} className="space-y-4">
        <div>
          <label className="block text-sm mb-1">用户名 *</label>
          <input
            type="text"
            name="username"
            required
            value={form.username}
            onChange={handleChange}
            className="w-full border rounded px-3 py-2"
          />
        </div>
        <div>
          <label className="block text-sm mb-1">密码 *</label>
          <input
            type="password"
            name="password"
            required
            value={form.password}
            onChange={handleChange}
            className="w-full border rounded px-3 py-2"
          />
        </div>
        <div>
          <label className="block text-sm mb-1">角色</label>
          <select
            name="role"
            value={form.role}
            onChange={handleChange}
            className="w-full border rounded px-3 py-2"
          >
            <option value="viewer">Viewer</option>
            <option value="editor">Editor</option>
            <option value="admin">Admin</option>
          </select>
        </div>
        <Button type="submit" disabled={createUser.isPending}>
          {createUser.isPending ? "创建中..." : "创建"}
        </Button>
      </form>
    </div>
  );
} 