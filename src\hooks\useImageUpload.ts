/**
 * 图片上传 Hook
 * 基于 React Query 的图片上传状态管理
 */

import { useMutation, useQueryClient } from '@tanstack/react-query';
import { toast } from 'sonner';
import { ImageService } from '@/services/imageService';
import type { ImageUploadRequest, ImageInfo } from '@/types/image';

interface UseImageUploadOptions {
  onSuccess?: (data: ImageInfo) => void;
  onError?: (error: Error) => void;
  showToast?: boolean;
}

export function useImageUpload(options: UseImageUploadOptions = {}) {
  const { onSuccess, onError, showToast = true } = options;
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (uploadData: ImageUploadRequest) => ImageService.upload(uploadData),
    onSuccess: (data, variables) => {
      // 更新相关查询缓存，使用上传请求中的category和resourceId
      queryClient.invalidateQueries({
        queryKey: ['images', variables.category, variables.resourceId],
      });

      if (showToast) {
        toast.success('Image uploaded successfully');
      }

      onSuccess?.(data);
    },
    onError: (error: Error) => {
      if (showToast) {
        toast.error(`Upload failed: ${error.message}`);
      }

      onError?.(error);
    },
  });
}

interface UseBatchImageUploadOptions {
  onSuccess?: (data: ImageInfo[]) => void;
  onError?: (error: Error) => void;
  onProgress?: (progress: number) => void;
  showToast?: boolean;
}

export function useBatchImageUpload(options: UseBatchImageUploadOptions = {}) {
  const { onSuccess, onError, onProgress, showToast = true } = options;
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async ({
      files,
      metadata,
    }: {
      files: File[];
      metadata: Omit<ImageUploadRequest, 'file'>;
    }) => {
      const results: ImageInfo[] = [];
      const total = files.length;

      for (let i = 0; i < files.length; i++) {
        const file = files[i];
        try {
          const result = await ImageService.upload({ ...metadata, file });
          results.push(result);
          
          // 报告进度
          const progress = ((i + 1) / total) * 100;
          onProgress?.(progress);
        } catch (error) {
          console.error(`Failed to upload ${file.name}:`, error);
          // 继续上传其他文件
        }
      }

      return results;
    },
    onSuccess: (data, variables) => {
      if (data.length > 0) {
        // 更新相关查询缓存，使用上传请求中的metadata
        queryClient.invalidateQueries({
          queryKey: ['images', variables.metadata.category, variables.metadata.resourceId],
        });

        if (showToast) {
          toast.success(`${data.length} images uploaded successfully`);
        }
      }

      onSuccess?.(data);
    },
    onError: (error: Error) => {
      if (showToast) {
        toast.error(`Batch upload failed: ${error.message}`);
      }

      onError?.(error);
    },
  });
}
