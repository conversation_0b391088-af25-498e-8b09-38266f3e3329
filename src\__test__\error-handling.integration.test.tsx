import { render, waitFor } from "@testing-library/react";
import { vi, test, beforeEach } from "vitest";
import { http, HttpResponse } from "msw";

import EventsList from "@/app/events/EventsList";
import ReactQueryProvider from "@/components/react-query-provider";
import { AuthProvider } from "@/contexts/user";
import type { ReactNode } from "react";
import { server } from "@test/testServer";
import { toast } from "sonner";
import { API_BASE } from "@/lib/http";

// Mock sonner toast
vi.mock("sonner", () => ({
  toast: {
    error: vi.fn(),
  },
}));

function Wrapper({ children }: { children: ReactNode }) {
  return (
    <ReactQueryProvider>
      <AuthProvider>{children}</AuthProvider>
    </ReactQueryProvider>
  );
}

beforeEach(() => {
  vi.clearAllMocks();
});

test("shows toast on 500 server error", async () => {
  server.use(
    http.get(`${API_BASE}/events`, () => {
      return new HttpResponse("Server error", { status: 500, statusText: "Internal Server Error" });
    })
  );

  render(<EventsList />, { wrapper: Wrapper });

  await waitFor(() => {
    expect(toast.error).toHaveBeenCalled();
  });
}); 