# Frontend Architecture

- 框架：React 19 + Next.js 15 (App Router)
- 状态管理：React Query v5（自动生成 hooks）
- 样式：Tailwind CSS + shadcn/ui
- 地图：React-Leaflet（OpenStreetMap 瓦片）
- 架构模式：功能模块化（events / circles / admin 等），按路由分文件夹
- 代码分割：基于路由的动态导入，使用 `Suspense` + `loading.tsx`

目录约定：

```text
src/
  app/            # 路由
  components/     # 通用组件
  hooks/          # React Query Hooks & utils
  lib/            # 工具函数 / Fetcher
  schemas/        # Zod & OpenAPI 输出的类型
``` 