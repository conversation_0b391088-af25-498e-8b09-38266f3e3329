import { describe, it, expect, vi, beforeEach } from 'vitest';
import type { D1Database } from '@cloudflare/workers-types';
import type { Cache, Logger } from '@/infrastructure';
import { getFeedData } from '@/modules/feed/service';
import type { FeedItem } from '@/modules/feed/schema';

// Mock D1 Database
const createMockD1Database = () => {
  const mockPreparedStatement = {
    bind: vi.fn().mockReturnThis(),
    all: vi.fn(),
    first: vi.fn(),
  };

  return {
    prepare: vi.fn().mockReturnValue(mockPreparedStatement),
    _mockPreparedStatement: mockPreparedStatement,
  } as unknown as D1Database & { _mockPreparedStatement: any };
};

// Mock Cache
const createMockCache = () => ({
  get: vi.fn(),
  set: vi.fn(),
});

// Mock <PERSON>gger
const createMockLogger = () => ({
  debug: vi.fn(),
  info: vi.fn(),
  warn: vi.fn(),
  error: vi.fn(),
});

describe('feed/service', () => {
  describe('getFeedData', () => {
    let mockDb: D1Database & { _mockPreparedStatement: any };
    let mockCache: Cache;
    let mockLogger: Logger;

    beforeEach(() => {
      mockDb = createMockD1Database();
      mockCache = createMockCache();
      mockLogger = createMockLogger();
      vi.clearAllMocks();
    });

    it('should return cached data when cache hit', async () => {
      const cachedData = {
        items: [
          {
            id: 'feed-event-1',
            type: 'event' as const,
            content: {
              id: '1',
              name: 'Test Event',
              description: 'Test Description',
              start_date: '2024-01-01T00:00:00Z',
              image_url: 'test.jpg',
            },
            created_at: '2024-01-01T00:00:00Z',
          },
        ],
        total: 1,
      };

      (mockCache.get as any).mockResolvedValue(cachedData);

      const result = await getFeedData(
        mockDb,
        1,
        10,
        'all',
        'en',
        mockCache,
        mockLogger
      );

      expect(result).toEqual(cachedData);
      expect(mockCache.get).toHaveBeenCalledWith('feed:en:all:page:1:limit:10');
      expect(mockLogger.debug).toHaveBeenCalledWith('getFeedData: hit cache', {
        key: 'feed:en:all:page:1:limit:10',
      });
      expect(mockDb.prepare).not.toHaveBeenCalled();
    });

    it('should fetch events data when type is "events"', async () => {
      const mockEventResults = [
        {
          type: 'event',
          id: '1',
          name: 'Test Event',
          description: 'Test Description',
          start_date: '2024-01-01T00:00:00Z',
          image_url: 'test.jpg',
          created_at: '2024-01-01T00:00:00Z',
        },
      ];

      mockDb._mockPreparedStatement.all.mockResolvedValue({
        results: mockEventResults,
      });
      mockDb._mockPreparedStatement.first.mockResolvedValue({ count: 1 });

      const result = await getFeedData(mockDb, 1, 10, 'events', 'en');

      expect(result.items).toHaveLength(1);
      expect(result.items[0]).toEqual({
        id: 'feed-event-1',
        type: 'event',
        content: {
          id: '1',
          name: 'Test Event',
          description: 'Test Description',
          start_date: '2024-01-01T00:00:00Z',
          image_url: 'test.jpg',
        },
        created_at: '2024-01-01T00:00:00Z',
      });
      expect(result.total).toBe(1);
    });

    it('should fetch circles data when type is "circles"', async () => {
      const mockCircleResults = [
        {
          type: 'circle',
          id: '1',
          name: 'Test Circle',
          description: 'Test Description',
          created_at: '2024-01-01T00:00:00Z',
        },
      ];

      mockDb._mockPreparedStatement.all.mockResolvedValue({
        results: mockCircleResults,
      });
      mockDb._mockPreparedStatement.first.mockResolvedValue({ count: 1 });

      const result = await getFeedData(mockDb, 1, 10, 'circles', 'zh');

      expect(result.items).toHaveLength(1);
      expect(result.items[0]).toEqual({
        id: 'feed-circle-1',
        type: 'circle',
        content: {
          id: '1',
          name: 'Test Circle',
          description: 'Test Description',
          start_date: null,
          image_url: null,
        },
        created_at: '2024-01-01T00:00:00Z',
      });
      expect(result.total).toBe(1);
    });

    it('should fetch both events and circles when type is "all"', async () => {
      const mockEventResults = [
        {
          type: 'event',
          id: '1',
          name: 'Test Event',
          description: 'Test Description',
          start_date: '2024-01-02T00:00:00Z',
          image_url: 'test.jpg',
          created_at: '2024-01-02T00:00:00Z',
        },
      ];

      const mockCircleResults = [
        {
          type: 'circle',
          id: '1',
          name: 'Test Circle',
          description: 'Test Description',
          created_at: '2024-01-01T00:00:00Z',
        },
      ];

      // Mock multiple calls to all() method
      mockDb._mockPreparedStatement.all
        .mockResolvedValueOnce({ results: mockEventResults })
        .mockResolvedValueOnce({ results: mockCircleResults });

      // Mock count queries
      mockDb._mockPreparedStatement.first
        .mockResolvedValueOnce({ count: 1 }) // events count
        .mockResolvedValueOnce({ count: 1 }); // circles count

      const result = await getFeedData(mockDb, 1, 10, 'all', 'en');

      expect(result.items).toHaveLength(2);
      // Should be sorted by created_at DESC (event first, then circle)
      expect(result.items[0].type).toBe('event');
      expect(result.items[1].type).toBe('circle');
      expect(result.total).toBe(2);
    });

    it('should handle pagination correctly', async () => {
      const mockEventResults = [
        {
          type: 'event',
          id: '1',
          name: 'Test Event',
          description: 'Test Description',
          start_date: '2024-01-01T00:00:00Z',
          image_url: 'test.jpg',
          created_at: '2024-01-01T00:00:00Z',
        },
      ];

      mockDb._mockPreparedStatement.all.mockResolvedValue({
        results: mockEventResults,
      });
      mockDb._mockPreparedStatement.first.mockResolvedValue({ count: 1 });

      await getFeedData(mockDb, 2, 5, 'events', 'en');

      // Check that bind was called with correct offset
      expect(mockDb._mockPreparedStatement.bind).toHaveBeenCalledWith(5, 5); // limit=5, offset=(2-1)*5=5
    });

    it('should handle missing created_at with default value', async () => {
      const mockEventResults = [
        {
          type: 'event',
          id: '1',
          name: 'Test Event',
          description: 'Test Description',
          start_date: '2024-01-01T00:00:00Z',
          image_url: 'test.jpg',
          created_at: null, // Missing created_at
        },
      ];

      mockDb._mockPreparedStatement.all.mockResolvedValue({
        results: mockEventResults,
      });
      mockDb._mockPreparedStatement.first.mockResolvedValue({ count: 1 });

      const result = await getFeedData(mockDb, 1, 10, 'events', 'en');

      expect(result.items[0].created_at).toMatch(
        /^\d{4}-\d{2}-\d{2}T\d{2}:\d{2}:\d{2}\.\d{3}Z$/
      );
    });

    it('should cache results when cache is provided', async () => {
      const mockEventResults = [
        {
          type: 'event',
          id: '1',
          name: 'Test Event',
          description: 'Test Description',
          start_date: '2024-01-01T00:00:00Z',
          image_url: 'test.jpg',
          created_at: '2024-01-01T00:00:00Z',
        },
      ];

      (mockCache.get as any).mockResolvedValue(null); // Cache miss
      mockDb._mockPreparedStatement.all.mockResolvedValue({
        results: mockEventResults,
      });
      mockDb._mockPreparedStatement.first.mockResolvedValue({ count: 1 });

      const result = await getFeedData(
        mockDb,
        1,
        10,
        'events',
        'en',
        mockCache,
        mockLogger
      );

      expect(mockCache.set).toHaveBeenCalledWith(
        'feed:en:events:page:1:limit:10',
        result,
        300
      );
      expect(mockLogger.debug).toHaveBeenCalledWith(
        'getFeedData: cached results',
        {
          key: 'feed:en:events:page:1:limit:10',
          count: 1,
          total: 1,
        }
      );
    });

    it('should work without cache and logger', async () => {
      const mockEventResults = [
        {
          type: 'event',
          id: '1',
          name: 'Test Event',
          description: 'Test Description',
          start_date: '2024-01-01T00:00:00Z',
          image_url: 'test.jpg',
          created_at: '2024-01-01T00:00:00Z',
        },
      ];

      mockDb._mockPreparedStatement.all.mockResolvedValue({
        results: mockEventResults,
      });
      mockDb._mockPreparedStatement.first.mockResolvedValue({ count: 1 });

      const result = await getFeedData(mockDb, 1, 10, 'events', 'en');

      expect(result.items).toHaveLength(1);
      expect(result.total).toBe(1);
    });

    it('should handle different locales in queries', async () => {
      const mockEventResults = [
        {
          type: 'event',
          id: '1',
          name: 'テストイベント',
          description: 'テスト説明',
          start_date: '2024-01-01T00:00:00Z',
          image_url: 'test.jpg',
          created_at: '2024-01-01T00:00:00Z',
        },
      ];

      mockDb._mockPreparedStatement.all.mockResolvedValue({
        results: mockEventResults,
      });
      mockDb._mockPreparedStatement.first.mockResolvedValue({ count: 1 });

      await getFeedData(mockDb, 1, 10, 'events', 'ja');

      // Verify that the query uses the correct locale
      const prepareCall = mockDb.prepare.mock.calls[0][0];
      expect(prepareCall).toContain('name_ja');
      expect(prepareCall).toContain('description_ja');
      expect(prepareCall).toContain('WHERE name_ja IS NOT NULL');
    });

    it('should limit results correctly when items exceed limit', async () => {
      // Create more items than the limit
      const mockEventResults = Array.from({ length: 15 }, (_, i) => ({
        type: 'event',
        id: `${i + 1}`,
        name: `Test Event ${i + 1}`,
        description: `Test Description ${i + 1}`,
        start_date: '2024-01-01T00:00:00Z',
        image_url: 'test.jpg',
        created_at: '2024-01-01T00:00:00Z',
      }));

      mockDb._mockPreparedStatement.all.mockResolvedValue({
        results: mockEventResults,
      });
      mockDb._mockPreparedStatement.first.mockResolvedValue({ count: 15 });

      const result = await getFeedData(mockDb, 1, 10, 'events', 'en');

      // Should only return 10 items (the limit)
      expect(result.items).toHaveLength(10);
      expect(result.total).toBe(15);
    });

    it('should handle zero count results', async () => {
      mockDb._mockPreparedStatement.all.mockResolvedValue({
        results: [],
      });
      mockDb._mockPreparedStatement.first.mockResolvedValue({ count: 0 });

      const result = await getFeedData(mockDb, 1, 10, 'events', 'en');

      expect(result.items).toHaveLength(0);
      expect(result.total).toBe(0);
    });

    it('should handle null count results', async () => {
      mockDb._mockPreparedStatement.all.mockResolvedValue({
        results: [],
      });
      mockDb._mockPreparedStatement.first.mockResolvedValue(null);

      const result = await getFeedData(mockDb, 1, 10, 'events', 'en');

      expect(result.items).toHaveLength(0);
      expect(result.total).toBe(0);
    });
  });
});
