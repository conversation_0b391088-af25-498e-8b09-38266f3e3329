import { describe, it, expect, vi, beforeEach } from "vitest";

// 使用 vi.hoisted 在 hoist 阶段创建 mock，并在后续 factory 中引用
const { mockRequest } = vi.hoisted(() => ({
  mockRequest: vi.fn(),
})) as { mockRequest: ReturnType<typeof vi.fn> };

vi.mock("../http", () => ({ request: mockRequest }));

// 被测模块在 mock 之后导入
import { ayafeedFetch } from "../apiFetcher";

// 重置 mock
beforeEach(() => {
  mockRequest.mockClear();
});

describe("ayafeedFetch – URL 构建", () => {
  it("应正确替换路径参数", async () => {
    await ayafeedFetch({
      url: "/events/{id}",
      method: "get",
      pathParams: { id: 42 },
    });
    expect(mockRequest).toHaveBeenCalledWith(
      "/events/42",
      expect.objectContaining({ method: "GET" }),
    );
  });

  it("应正确拼接查询字符串，过滤空值", async () => {
    await ayafeedFetch({
      url: "/events",
      method: "get",
      queryParams: {
        page: 1,
        search: "tokyo",
        empty: undefined,
        nil: null,
      },
    });
    const calledUrl: string = mockRequest.mock.calls[0][0];
    // 不保证参数顺序，但需包含关键片段
    expect(calledUrl.startsWith("/events?")).toBe(true);
    expect(calledUrl).toContain("page=1");
    expect(calledUrl).toContain("search=tokyo");
    expect(calledUrl).not.toContain("empty=");
    expect(calledUrl).not.toContain("nil=");
  });
}); 