import Image from "next/image"
import Link from "next/link"

interface CircleCardProps {
  logo: string
  name: string
  id: string
}

/**
 * 社团信息卡片组件
 * @param logo 社团logo图片路径
 * @param name 社团名称
 * @param id 社团id
 */
export default function CircleCard({ logo, name, id }: CircleCardProps) {
  return (
    <Link href={`/circles/${id}`} className="block">
      <div className="flex flex-col items-center bg-white rounded-lg p-2 cursor-pointer">
        <Image
          src={logo}
          alt={name}
          width={48}
          height={48}
          className="w-12 h-12 object-cover rounded-full mb-1"
        />
        <p className="text-xs font-medium text-gray-800 text-center truncate w-12">{name}</p>
      </div>
    </Link>
  )
} 