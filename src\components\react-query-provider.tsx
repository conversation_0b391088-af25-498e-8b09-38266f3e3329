"use client"

import {
  QueryClient,
  QueryClientProvider,
  QueryCache,
} from "@tanstack/react-query"
import { ReactQueryDevtools } from "@tanstack/react-query-devtools"
import { showApiError } from "@/lib/show-error"
import { ReactNode, useState } from "react"

interface Props {
  children: ReactNode
}

/**
 * React Query 全局 Provider
 *
 * - 仅在客户端渲染（use client）
 * - 初始化 QueryClient 并注入默认配置
 */
export default function ReactQueryProvider({ children }: Props) {
  // 使用 useState 保证 QueryClient 实例在客户端仅创建一次
  const [client] = useState(
    () =>
      new QueryClient({
        queryCache: new QueryCache({
          onError: (err) => {
            // 全局查询错误统一弹 Toast
            showApiError(err)
          },
        }),
        defaultOptions: {
          queries: {
            staleTime: 1000 * 60 * 5,
            // 测试环境禁用自动重试，避免干扰错误捕获
            retry: process.env.NODE_ENV === "test" ? false : 2,
            refetchOnWindowFocus: false,
          },
        },
      })
  )

  return (
    <QueryClientProvider client={client}>
      {children}
      {/* Devtools 仅开发环境显示 */}
      {process.env.NODE_ENV === "development" && (
        <ReactQueryDevtools initialIsOpen={false} />
      )}
    </QueryClientProvider>
  )
} 