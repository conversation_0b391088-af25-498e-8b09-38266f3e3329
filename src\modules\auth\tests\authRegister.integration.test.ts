import { describe, it, expect } from 'vitest';

import app from '@/app';

// @ts-ignore
const Request = globalThis.Request;

function createMockDB() {
  return {
    prepare: (_query: string) => {
      const response = {
        run: async () => ({ success: true }),
        all: async () => ({ results: [] }),
        first: async () => null,
      };
      return {
        ...response,
        bind: (..._args: any[]) => response,
      };
    },
    batch: async (_queries: any[]) => ({ success: true }),
  };
}

function withEnv(url: string, init?: RequestInit, env: any = {}) {
  const base = url.startsWith('http') ? url : `http://localhost${url}`;
  return app.fetch(new Request(base, init), env);
}

describe('/auth/register', () => {
  const validBody = {
    username: 'alice',
    password: 'secret123',
  };

  it('should register user successfully', async () => {
    const res = await withEnv(
      '/auth/register',
      {
        method: 'POST',
        body: JSON.stringify(validBody),
        headers: { 'Content-Type': 'application/json' },
      },
      { DB: createMockDB() }
    );

    expect(res.status).toBe(201);
    const json = (await res.json()) as any;
    expect(json.username).toBe(validBody.username);
    expect(json.id).toBeDefined();
  });

  it('should reject short username', async () => {
    const res = await withEnv(
      '/auth/register',
      {
        method: 'POST',
        body: JSON.stringify({ ...validBody, username: 'ab' }),
        headers: { 'Content-Type': 'application/json' },
      },
      { DB: createMockDB() }
    );

    expect(res.status).toBe(400);
  });

  it('should reject short password', async () => {
    const res = await withEnv(
      '/auth/register',
      {
        method: 'POST',
        body: JSON.stringify({ ...validBody, password: '123' }),
        headers: { 'Content-Type': 'application/json' },
      },
      { DB: createMockDB() }
    );

    expect(res.status).toBe(400);
  });
});
