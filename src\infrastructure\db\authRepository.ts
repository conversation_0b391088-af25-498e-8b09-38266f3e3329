import type {
  D1Database,
  D1PreparedStatement,
} from '@cloudflare/workers-types';

import type { AuthRepository, AuthUser } from '../../modules/auth/repository';

/**
 * D1 数据库实现
 */
export class D1AuthRepository implements AuthRepository {
  constructor(private readonly db: D1Database) {}

  /**
   * 从 D1 查询结果获取首行，兼容 .first() / .all() API
   */
  private async getFirstRow(stmt: D1PreparedStatement): Promise<any | null> {
    if (typeof (stmt as any).first === 'function') {
      const row = await (stmt as any).first();
      if (row !== null && row !== undefined) {
        return row;
      }
    }
    // fallback to .all()
    const result = await (stmt as any).all();
    return result && result.results && result.results.length
      ? result.results[0]
      : null;
  }

  async createUser(
    id: string,
    username: string,
    role: string,
    hashedPassword: string
  ): Promise<AuthUser> {
    const keyId = crypto.randomUUID();
    await this.db.batch([
      this.db
        .prepare('INSERT INTO auth_user (id, username, role) VALUES (?, ?, ?)')
        .bind(id, username, role),
      this.db
        .prepare(
          'INSERT INTO auth_key (id, user_id, hashed_password) VALUES (?, ?, ?)'
        )
        .bind(keyId, id, hashedPassword),
    ]);
    return { id, username, role, hashedPassword };
  }

  async findUserWithPasswordByUsername(
    username: string
  ): Promise<AuthUser | null> {
    const stmt = this.db
      .prepare(
        `SELECT
          k.user_id   AS id,
          k.hashed_password AS hashed_password,
          u.username  AS username,
          u.role      AS role
        FROM auth_key k
        JOIN auth_user u ON u.id = k.user_id
        WHERE u.username = ?`
      )
      .bind(username);
    const row = await this.getFirstRow(stmt);
    return row as any;
  }

  async createSession(
    sessionId: string,
    userId: string,
    expiresAt: Date
  ): Promise<void> {
    await this.db
      .prepare(
        'INSERT INTO auth_session (id, user_id, expires_at) VALUES (?, ?, ?)'
      )
      .bind(sessionId, userId, expiresAt.toISOString())
      .run();
  }

  async validateSession(sessionId: string): Promise<AuthUser | null> {
    const stmt = this.db
      .prepare(
        `SELECT u.id, u.username, u.role
         FROM auth_session s
         JOIN auth_user u ON u.id = s.user_id
         WHERE s.id = ? AND s.expires_at > ?`
      )
      .bind(sessionId, new Date().toISOString());

    const row = (await this.getFirstRow(stmt)) as {
      id: string;
      username: string;
      role: string;
    } | null;
    if (!row) return null;
    return { id: row.id, username: row.username, role: row.role };
  }

  async invalidateSession(sessionId: string): Promise<void> {
    await this.db
      .prepare('DELETE FROM auth_session WHERE id = ?')
      .bind(sessionId)
      .run();
  }

  async cleanupExpiredSessions(): Promise<void> {
    await this.db
      .prepare('DELETE FROM auth_session WHERE expires_at < ?')
      .bind(new Date().toISOString())
      .run();
  }
}
