/**
 * 语言切换组件
 * 基于后端文档包的多语言集成规范
 */

'use client';

import React from 'react';
import { useI18nStore } from '@/stores/i18n';
import { SUPPORTED_LOCALES } from '@/services/i18n';

export function LanguageSwitcher() {
  const { locale, setLocale, isLoading } = useI18nStore();
  
  return (
    <div className="relative">
      <select
        value={locale}
        onChange={(e) => setLocale(e.target.value as any)}
        disabled={isLoading}
        className="appearance-none bg-white border border-gray-300 rounded px-3 py-1 pr-8 text-sm"
      >
        {SUPPORTED_LOCALES.map((localeConfig) => (
          <option key={localeConfig.code} value={localeConfig.code}>
            {localeConfig.flag} {localeConfig.nativeName}
          </option>
        ))}
      </select>
      
      {isLoading && (
        <div className="absolute right-2 top-1/2 transform -translate-y-1/2">
          <div className="animate-spin h-4 w-4 border-2 border-blue-500 border-t-transparent rounded-full"></div>
        </div>
      )}
    </div>
  );
}
