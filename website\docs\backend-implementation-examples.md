# 后端多语言实现示例

## 🚀 快速开始

### Express.js 完整示例

```javascript
const express = require('express');
const redis = require('redis');
const { Pool } = require('pg');

const app = express();
const redisClient = redis.createClient();
const db = new Pool({ /* 数据库配置 */ });

// 1. 语言检测中间件
app.use((req, res, next) => {
  // 优先级：X-Locale > Accept-Language > 默认
  req.locale = req.headers['x-locale'] || 
               req.headers['accept-language']?.split(',')[0]?.split('-')[0] || 
               'zh';
  
  // 验证语言代码
  if (!['zh', 'ja', 'en'].includes(req.locale)) {
    req.locale = 'zh';
  }
  
  // 设置响应头
  res.setHeader('Content-Language', req.locale);
  res.setHeader('X-Response-Locale', req.locale);
  
  next();
});

// 2. 事件 API
app.get('/api/events', async (req, res) => {
  try {
    const { page = 1, limit = 20, category } = req.query;
    const locale = req.locale;
    
    // 构建缓存键
    const cacheKey = `events:${locale}:${page}:${limit}:${category || 'all'}`;
    
    // 尝试从缓存获取
    let result = await redisClient.get(cacheKey);
    if (result) {
      return res.json(JSON.parse(result));
    }
    
    // 数据库查询
    const events = await getEventsByLocale(locale, { page, limit, category });
    const total = await getEventsCount(locale, { category });
    
    // 构建响应
    const response = {
      success: true,
      data: events,
      locale,
      timestamp: new Date().toISOString(),
      meta: {
        total,
        page: parseInt(page),
        limit: parseInt(limit),
        hasMore: events.length === parseInt(limit)
      }
    };
    
    // 缓存 5 分钟
    await redisClient.setex(cacheKey, 300, JSON.stringify(response));
    
    res.json(response);
  } catch (error) {
    res.status(500).json({
      success: false,
      error: error.message,
      locale: req.locale
    });
  }
});

// 3. 数据库查询函数
async function getEventsByLocale(locale, params) {
  const { page, limit, category } = params;
  const offset = (page - 1) * limit;
  
  let query = `
    SELECT 
      e.id,
      e.venue_lat,
      e.venue_lng,
      e.start_date,
      e.end_date,
      e.image_url,
      e.url,
      et.name,
      et.description,
      et.venue_name
    FROM events e
    LEFT JOIN event_translations et ON e.id = et.event_id
    WHERE et.locale = $1
  `;
  
  const values = [locale];
  let paramIndex = 2;
  
  if (category) {
    query += ` AND e.category = $${paramIndex}`;
    values.push(category);
    paramIndex++;
  }
  
  query += ` ORDER BY e.start_date DESC LIMIT $${paramIndex} OFFSET $${paramIndex + 1}`;
  values.push(limit, offset);
  
  const result = await db.query(query, values);
  return result.rows;
}

// 4. 搜索 API
app.get('/api/search', async (req, res) => {
  try {
    const { q: query, type = 'all' } = req.query;
    const locale = req.locale;
    
    if (!query || query.trim().length === 0) {
      return res.json({
        success: true,
        data: [],
        locale,
        meta: { total: 0 }
      });
    }
    
    const results = await searchContent(locale, query.trim(), type);
    
    res.json({
      success: true,
      data: results,
      locale,
      meta: {
        total: results.length,
        query: query.trim(),
        type
      }
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      error: error.message,
      locale: req.locale
    });
  }
});

app.listen(3000, () => {
  console.log('🌐 多语言 API 服务器启动在端口 3000');
});
```

## 🗄️ 数据库设计

### PostgreSQL 表结构

```sql
-- 事件主表
CREATE TABLE events (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  venue_lat DECIMAL(10, 8),
  venue_lng DECIMAL(11, 8),
  start_date TIMESTAMP WITH TIME ZONE NOT NULL,
  end_date TIMESTAMP WITH TIME ZONE,
  image_url TEXT,
  url TEXT,
  category VARCHAR(50),
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 事件翻译表
CREATE TABLE event_translations (
  event_id UUID REFERENCES events(id) ON DELETE CASCADE,
  locale VARCHAR(5) NOT NULL,
  name VARCHAR(255) NOT NULL,
  description TEXT,
  venue_name VARCHAR(255),
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  PRIMARY KEY (event_id, locale)
);

-- 社团主表
CREATE TABLE circles (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  website_url TEXT,
  twitter_handle VARCHAR(50),
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 社团翻译表
CREATE TABLE circle_translations (
  circle_id UUID REFERENCES circles(id) ON DELETE CASCADE,
  locale VARCHAR(5) NOT NULL,
  name VARCHAR(255) NOT NULL,
  description TEXT,
  tags TEXT[], -- PostgreSQL 数组类型
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  PRIMARY KEY (circle_id, locale)
);

-- 索引优化
CREATE INDEX idx_events_start_date ON events(start_date DESC);
CREATE INDEX idx_events_category ON events(category);
CREATE INDEX idx_event_translations_locale ON event_translations(locale);
CREATE INDEX idx_event_translations_name ON event_translations(locale, name);

-- 全文搜索索引
CREATE INDEX idx_event_translations_search 
ON event_translations USING gin(to_tsvector('simple', name || ' ' || COALESCE(description, '')));

-- 日文专用搜索索引
CREATE INDEX idx_event_translations_ja_search 
ON event_translations USING gin(to_tsvector('japanese', name || ' ' || COALESCE(description, '')))
WHERE locale = 'ja';
```

## 🔍 搜索实现

### 全文搜索函数

```javascript
async function searchContent(locale, query, type) {
  // 根据语言选择搜索配置
  const searchConfig = locale === 'ja' ? 'japanese' : 'simple';
  
  let searchQuery = '';
  const values = [query, locale];
  
  if (type === 'all' || type === 'events') {
    searchQuery += `
      SELECT 
        'event' as type,
        e.id,
        et.name,
        et.description,
        et.venue_name,
        e.start_date,
        e.image_url,
        ts_rank(
          to_tsvector('${searchConfig}', et.name || ' ' || COALESCE(et.description, '')),
          plainto_tsquery('${searchConfig}', $1)
        ) as rank
      FROM events e
      JOIN event_translations et ON e.id = et.event_id
      WHERE et.locale = $2
      AND to_tsvector('${searchConfig}', et.name || ' ' || COALESCE(et.description, '')) 
          @@ plainto_tsquery('${searchConfig}', $1)
    `;
  }
  
  if (type === 'all') {
    searchQuery += ' UNION ALL ';
  }
  
  if (type === 'all' || type === 'circles') {
    searchQuery += `
      SELECT 
        'circle' as type,
        c.id,
        ct.name,
        ct.description,
        NULL as venue_name,
        NULL as start_date,
        NULL as image_url,
        ts_rank(
          to_tsvector('${searchConfig}', ct.name || ' ' || COALESCE(ct.description, '')),
          plainto_tsquery('${searchConfig}', $1)
        ) as rank
      FROM circles c
      JOIN circle_translations ct ON c.id = ct.circle_id
      WHERE ct.locale = $2
      AND to_tsvector('${searchConfig}', ct.name || ' ' || COALESCE(ct.description, '')) 
          @@ plainto_tsquery('${searchConfig}', $1)
    `;
  }
  
  searchQuery += ' ORDER BY rank DESC LIMIT 50';
  
  const result = await db.query(searchQuery, values);
  return result.rows;
}
```

## 📦 Redis 缓存策略

### 缓存键设计

```javascript
class CacheManager {
  constructor(redisClient) {
    this.redis = redisClient;
    this.defaultTTL = 300; // 5分钟
  }
  
  // 生成缓存键
  generateKey(type, locale, params = {}) {
    const sortedParams = Object.entries(params)
      .filter(([_, value]) => value !== undefined && value !== null)
      .sort(([a], [b]) => a.localeCompare(b))
      .map(([key, value]) => `${key}:${value}`)
      .join('|');
    
    return `${type}:${locale}${sortedParams ? ':' + sortedParams : ''}`;
  }
  
  // 获取缓存
  async get(type, locale, params) {
    const key = this.generateKey(type, locale, params);
    const cached = await this.redis.get(key);
    return cached ? JSON.parse(cached) : null;
  }
  
  // 设置缓存
  async set(type, locale, params, data, ttl = this.defaultTTL) {
    const key = this.generateKey(type, locale, params);
    await this.redis.setex(key, ttl, JSON.stringify(data));
  }
  
  // 删除特定语言的缓存
  async clearLocale(locale) {
    const pattern = `*:${locale}:*`;
    const keys = await this.redis.keys(pattern);
    if (keys.length > 0) {
      await this.redis.del(...keys);
    }
  }
  
  // 删除特定类型的缓存
  async clearType(type) {
    const pattern = `${type}:*`;
    const keys = await this.redis.keys(pattern);
    if (keys.length > 0) {
      await this.redis.del(...keys);
    }
  }
}

// 使用示例
const cacheManager = new CacheManager(redisClient);

app.get('/api/events', async (req, res) => {
  const { page = 1, limit = 20, category } = req.query;
  const locale = req.locale;
  
  // 尝试从缓存获取
  let result = await cacheManager.get('events', locale, { page, limit, category });
  
  if (!result) {
    // 从数据库查询
    const events = await getEventsByLocale(locale, { page, limit, category });
    const total = await getEventsCount(locale, { category });
    
    result = {
      success: true,
      data: events,
      locale,
      meta: { total, page: parseInt(page), limit: parseInt(limit) }
    };
    
    // 存入缓存
    await cacheManager.set('events', locale, { page, limit, category }, result);
  }
  
  res.json(result);
});
```

## 🔄 数据更新和缓存失效

### 智能缓存失效

```javascript
// 当事件数据更新时，清除相关缓存
async function updateEvent(eventId, updates, locale) {
  const transaction = await db.begin();
  
  try {
    // 更新数据库
    if (updates.translations) {
      await transaction.query(
        'UPDATE event_translations SET name = $1, description = $2 WHERE event_id = $3 AND locale = $4',
        [updates.translations.name, updates.translations.description, eventId, locale]
      );
    }
    
    await transaction.commit();
    
    // 清除相关缓存
    await cacheManager.clearType('events');
    await cacheManager.clearType('search');
    
    console.log(`✅ 事件 ${eventId} 更新完成，缓存已清除`);
    
  } catch (error) {
    await transaction.rollback();
    throw error;
  }
}
```

这些后端实现示例将确保与前端的多语言缓存策略完美配合！
