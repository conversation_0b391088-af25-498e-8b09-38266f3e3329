/**
 * 事件卡片组件示例
 * 基于后端文档包的常用示例
 */

'use client';

import React from 'react';
import { useLocalization } from '@/hooks/useLocalization';
import { extractLocalizedText } from '@/utils/localization';

interface Event {
  id: string;
  name: string | { zh?: string; ja?: string; en?: string };
  description: string | { zh?: string; ja?: string; en?: string };
  date: string;
  venue_name: string;
}

interface EventCardProps {
  event: Event;
  onEdit?: (event: Event) => void;
  onDelete?: (id: string) => void;
  showActions?: boolean;
}

export function EventCard({ event, onEdit, onDelete, showActions = false }: EventCardProps) {
  const { locale, formatDate, getLocalizedText } = useLocalization();
  
  // 处理多语言文本
  const eventName = typeof event.name === 'string' 
    ? event.name 
    : getLocalizedText(event.name);
    
  const eventDescription = typeof event.description === 'string'
    ? event.description
    : getLocalizedText(event.description);
  
  return (
    <div className="bg-white rounded-lg shadow-md p-6 hover:shadow-lg transition-shadow">
      <div className="flex justify-between items-start mb-4">
        <h3 className="text-xl font-bold text-gray-900 line-clamp-2">
          {eventName}
        </h3>
        
        {showActions && (
          <div className="flex space-x-2 ml-4">
            {onEdit && (
              <button
                onClick={() => onEdit(event)}
                className="text-blue-600 hover:text-blue-800 text-sm"
              >
                编辑
              </button>
            )}
            {onDelete && (
              <button
                onClick={() => onDelete(event.id)}
                className="text-red-600 hover:text-red-800 text-sm"
              >
                删除
              </button>
            )}
          </div>
        )}
      </div>
      
      <p className="text-gray-600 mb-4 line-clamp-3">
        {eventDescription}
      </p>
      
      <div className="flex justify-between items-center text-sm text-gray-500">
        <span className="flex items-center">
          <svg className="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z" />
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 11a3 3 0 11-6 0 3 3 0 016 0z" />
          </svg>
          {event.venue_name}
        </span>
        
        <span className="flex items-center">
          <svg className="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z" />
          </svg>
          {formatDate(event.date, { 
            year: 'numeric', 
            month: 'short', 
            day: 'numeric' 
          })}
        </span>
      </div>
    </div>
  );
}
