# 图片模块 (Image Module)

完整的图片管理解决方案，支持上传、预览、管理和删除功能。

## 功能特性

- 🖼️ **图片上传** - 支持拖拽上传、批量上传、进度显示
- 📱 **响应式设计** - 适配桌面端和移动端
- 🔍 **图片预览** - 全屏预览、缩放、导航
- 🗂️ **图片管理** - 网格显示、选择、批量操作
- 🔐 **权限控制** - 基于角色的访问控制
- ⚡ **性能优化** - 懒加载、缓存管理
- 🎨 **现代UI** - 基于shadcn/ui的美观界面

## 组件概览

### ImageUpload
图片上传组件，支持拖拽和点击上传。

```tsx
import { ImageUpload } from '@/components/images';

<ImageUpload
  category="event"
  resourceId="comiket-103"
  imageType="poster"
  variant="original"
  multiple={true}
  onUploadSuccess={(images) => console.log('Uploaded:', images)}
/>
```

### ImageGrid
图片网格显示组件，支持选择和批量操作。

```tsx
import { ImageGrid } from '@/components/images';

<ImageGrid
  images={images}
  selectedImages={selectedImages}
  onSelectionChange={setSelectedImages}
  onPreview={handlePreview}
  onDelete={handleDelete}
/>
```

### ImagePreview
全屏图片预览组件，支持导航和操作。

```tsx
import { ImagePreview } from '@/components/images';

<ImagePreview
  isOpen={isPreviewOpen}
  images={images}
  currentIndex={currentIndex}
  onClose={() => setIsPreviewOpen(false)}
  onNext={handleNext}
  onPrevious={handlePrevious}
/>
```

### ImageManager
完整的图片管理器，集成所有功能。

```tsx
import { ImageManager } from '@/components/images';

<ImageManager
  category="event"
  resourceId="comiket-103"
  allowUpload={true}
  allowDelete={true}
  showFilters={true}
/>
```

## Hooks

### useImageUpload
处理图片上传的Hook。

```tsx
import { useImageUpload } from '@/hooks/useImageUpload';

const uploadImage = useImageUpload({
  onSuccess: (image) => console.log('Uploaded:', image),
  onError: (error) => console.error('Upload failed:', error),
});

// 使用
uploadImage.mutate({
  file,
  category: 'event',
  resourceId: 'comiket-103',
  imageType: 'poster',
  variant: 'original',
});
```

### useImages
获取图片列表的Hook。

```tsx
import { useImages } from '@/hooks/useImages';

const { data, isLoading, error } = useImages({
  category: 'event',
  resourceId: 'comiket-103',
  page: 1,
  pageSize: 20,
});
```

### useImageDelete
删除图片的Hook。

```tsx
import { useImageDelete } from '@/hooks/useImageDelete';

const deleteImages = useImageDelete({
  onSuccess: () => console.log('Deleted successfully'),
});

// 使用
deleteImages.mutate(['/path/to/image1.jpg', '/path/to/image2.jpg']);
```

## 服务层

### ImageService
图片相关的API服务。

```tsx
import { ImageService } from '@/services/imageService';

// 上传图片
const image = await ImageService.upload({
  file,
  category: 'event',
  resourceId: 'comiket-103',
  imageType: 'poster',
  variant: 'original',
});

// 获取图片列表
const { images } = await ImageService.list({
  category: 'event',
  resourceId: 'comiket-103',
});

// 删除图片
await ImageService.delete(['/path/to/image.jpg']);

// 验证文件
const validation = ImageService.validate(file);
if (!validation.valid) {
  console.error(validation.error);
}

// 生成URL
const url = ImageService.getUrl('/path/to/image.jpg');
const thumbnailUrl = ImageService.getThumbnailUrl('/path/to/image.jpg');
```

## 类型定义

主要类型定义在 `@/types/image` 中：

```tsx
import type {
  ImageCategory,
  ImageType,
  ImageVariant,
  ImageInfo,
  ImageUploadRequest,
  ImageUploadProps,
} from '@/types/image';
```

## 权限控制

图片管理页面需要 `admin` 或 `editor` 权限：

```tsx
import RoleGuard from '@/components/RoleGuard';

<RoleGuard allow={['admin', 'editor']}>
  <ImageManager />
</RoleGuard>
```

## 配置

### 环境变量

```env
# CDN基础URL（可选）
NEXT_PUBLIC_CDN_URL=https://cdn.example.com

# API基础URL
NEXT_PUBLIC_API_URL=https://api.example.com
```

### 文件限制

- **支持格式**: JPEG, PNG, WebP, GIF
- **文件大小**: 100字节 - 10MB
- **批量上传**: 最多10个文件

## 使用示例

### 基础上传

```tsx
function BasicUpload() {
  return (
    <ImageUpload
      category="event"
      resourceId="comiket-103"
      imageType="poster"
      onUploadSuccess={(images) => {
        console.log('上传成功:', images);
      }}
    />
  );
}
```

### 完整管理界面

```tsx
function ImageManagement() {
  return (
    <div className="container mx-auto py-8">
      <h1 className="text-2xl font-bold mb-6">图片管理</h1>
      <ImageManager
        category="event"
        resourceId="comiket-103"
        allowUpload={true}
        allowDelete={true}
      />
    </div>
  );
}
```

### 自定义图片网格

```tsx
function CustomImageGrid() {
  const [selectedImages, setSelectedImages] = useState(new Set());
  const { data } = useImages({
    category: 'event',
    resourceId: 'comiket-103',
  });

  return (
    <ImageGrid
      images={data?.images || []}
      selectedImages={selectedImages}
      onSelectionChange={setSelectedImages}
      onPreview={(image, index) => {
        // 自定义预览逻辑
      }}
    />
  );
}
```

## 测试

运行测试：

```bash
# 运行所有图片模块测试
pnpm test src/components/images
pnpm test src/services/imageService
pnpm test src/hooks/useImage

# 运行特定测试
pnpm test ImageUpload.test.tsx
```

## 故障排除

### 常见问题

1. **上传失败**
   - 检查文件格式和大小
   - 确认网络连接
   - 验证API权限

2. **图片不显示**
   - 检查CDN配置
   - 确认图片路径正确
   - 验证CORS设置

3. **权限错误**
   - 确认用户角色
   - 检查认证状态
   - 验证API权限

### 调试

启用调试模式：

```tsx
// 在开发环境中启用详细日志
if (process.env.NODE_ENV === 'development') {
  console.log('Image module debug mode enabled');
}
```

## 贡献

欢迎提交Issue和Pull Request来改进图片模块！
