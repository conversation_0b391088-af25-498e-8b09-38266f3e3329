/**
 * API调用Hook
 * 基于后端文档包的错误处理规范
 */

import { useState } from 'react';
import { useErrorStore } from '@/stores/error';

export function useApiCall<T = any>() {
  const [loading, setLoading] = useState(false);
  const [data, setData] = useState<T | null>(null);
  const { handleApiError } = useErrorStore();
  
  const execute = async (
    apiCall: () => Promise<T>,
    options?: {
      onSuccess?: (data: T) => void;
      onError?: (error: any) => void;
      showError?: boolean;
    }
  ) => {
    setLoading(true);
    
    try {
      const result = await apiCall();
      setData(result);
      options?.onSuccess?.(result);
      return result;
    } catch (error) {
      if (options?.showError !== false) {
        handleApiError(error, { apiCall: apiCall.name });
      }
      options?.onError?.(error);
      throw error;
    } finally {
      setLoading(false);
    }
  };
  
  return { execute, loading, data };
}
