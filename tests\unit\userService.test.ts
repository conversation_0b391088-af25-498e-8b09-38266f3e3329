import { describe, it, expect, vi, beforeEach } from 'vitest';
// ---------- Mocks ----------
vi.mock('bcryptjs', () => ({
  default: {
    hash: async () => 'hashed_pwd',
  },
}));

vi.mock('uuid', () => ({ v4: () => 'u1' }));

import * as userService from '@/modules/user/service';

import type { D1Database } from '@cloudflare/workers-types';

function createMockDB() {
  const state = { id: 'u1', username: 'alice', role: 'viewer' };

  return {
    batch: async () => ({ success: true }),
    prepare: (query: string) => {
      const upper = query.toUpperCase();
      return {
        // 链式 .bind()
        bind: (...args: any[]) => ({
          sql: upper,
          first: async () => {
            if (
              upper.includes('SELECT ID') &&
              upper.includes('FROM AUTH_USER')
            ) {
              return { ...state };
            }
            return null;
          },
          run: async () => {
            // 处理 UPDATE auth_user
            if (upper.startsWith('UPDATE AUTH_USER')) {
              const assignments = upper.split(' SET ')[1]?.split(' WHERE ')[0];
              if (!assignments) return { success: true };
              const parts = assignments.split(',');
              let argIndex = 0;
              for (const part of parts) {
                if (part.includes('USERNAME')) {
                  state.username = args[argIndex];
                } else if (part.includes('ROLE')) {
                  state.role = args[argIndex];
                }
                argIndex++;
              }
            }
            return { success: true };
          },
        }),
        first: async () => {
          if (upper.includes('SELECT ID') && upper.includes('FROM AUTH_USER')) {
            return { ...state };
          }
          return null;
        },
        run: async () => ({ success: true }),
        all: async () => ({ results: [{ ...state }] }),
      };
    },
  } as unknown as D1Database;
}

// ---------- Tests ----------
describe('userService', () => {
  let db: D1Database;

  beforeEach(() => {
    db = createMockDB();
  });

  it('createUser should insert and return user', async () => {
    const user = await userService.createUser(db, {
      username: 'alice',
      password: 'pwd12345',
    });
    expect(user).toEqual({ id: 'u1', username: 'alice', role: 'viewer' });
  });

  it('updateUser should update fields and return updated user', async () => {
    const updated = await userService.updateUser(db, 'u1', {
      username: 'bob',
      role: 'editor',
    });
    expect(updated).toEqual({ id: 'u1', username: 'bob', role: 'editor' });
  });

  it('listUsers should return array', async () => {
    const list = await userService.listUsers(db);
    expect(Array.isArray(list)).toBe(true);
  });
});
