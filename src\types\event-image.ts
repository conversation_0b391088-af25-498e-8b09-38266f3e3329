/**
 * 事件图片相关的类型定义
 */

/**
 * 图片变体类型
 */
export type ImageVariant = 'original' | 'large' | 'medium' | 'thumb';

/**
 * 图片类型
 */
export type ImageType = 'poster' | 'logo' | 'banner' | 'gallery';

/**
 * 图片分类
 */
export type ImageCategory = 'event' | 'circle' | 'venue';

/**
 * 图片信息接口
 */
export interface EventImageInfo {
  id: string;
  groupId: string;
  relativePath: string;
  variant: ImageVariant;
  metadata: {
    size: number;
    dimensions: {
      width: number;
      height: number;
    };
    format: string;
  };
  createdAt: string;
  updatedAt?: string;
}

/**
 * 图片上传请求
 */
export interface EventImageUploadRequest {
  file: File;
  category: ImageCategory;
  resourceId: string;
  imageType: ImageType;
  variant: ImageVariant;
  groupId?: string;
}

/**
 * 图片上传响应
 */
export interface EventImageUploadResponse {
  id: string;
  groupId: string;
  relativePath: string;
  variant: ImageVariant;
  metadata: {
    size: number;
    dimensions: {
      width: number;
      height: number;
    };
    format: string;
  };
}

/**
 * 图片列表查询参数
 */
export interface EventImageListParams {
  category: ImageCategory;
  resourceId: string;
  variant?: ImageVariant;
  imageType?: ImageType;
  page?: number;
  pageSize?: number;
}

/**
 * 图片列表响应
 */
export interface EventImageListResponse {
  images: EventImageInfo[];
  pagination: {
    page: number;
    pageSize: number;
    total: number;
    totalPages: number;
  };
}

/**
 * 图片删除请求
 */
export interface EventImageDeleteRequest {
  relativePaths: string[];
}

/**
 * 图片删除响应
 */
export interface EventImageDeleteResponse {
  deletedCount: number;
  errors?: Array<{
    path: string;
    error: string;
  }>;
}

/**
 * 事件图片上传组件的 Props
 */
export interface EventImageUploadProps {
  /** 事件 ID */
  eventId: string;
  /** 当前图片组 ID（可选，用于替换现有图片） */
  groupId?: string;
  /** 上传成功回调 */
  onUploadSuccess?: (images: EventImageUploadResponse[]) => void;
  /** 上传失败回调 */
  onUploadError?: (error: Error) => void;
  /** 上传进度回调 */
  onUploadProgress?: (variant: string, progress: number) => void;
  /** 是否禁用 */
  disabled?: boolean;
  /** 自定义类名 */
  className?: string;
}

/**
 * 事件图片显示组件的 Props
 */
export interface EventImageDisplayProps {
  /** 事件 ID */
  eventId: string;
  /** 图片变体 */
  variant: 'large' | 'medium';
  /** 自定义类名 */
  className?: string;
  /** 是否启用懒加载 */
  lazy?: boolean;
  /** 回退图片 URL */
  fallbackSrc?: string;
  /** 图片加载完成回调 */
  onLoad?: () => void;
  /** 图片加载失败回调 */
  onError?: () => void;
}

/**
 * 图片处理进度信息
 */
export interface ImageProcessingProgress {
  variant: string;
  stage: 'loading' | 'processing' | 'compressing' | 'uploading' | 'complete';
  progress: number;
}

/**
 * 批量图片查询结果
 */
export interface BatchImageResult {
  [eventId: string]: EventImageInfo | null;
}

/**
 * 懒加载图片组件的 Props
 */
export interface LazyImageProps {
  /** 图片 URL */
  src: string;
  /** 替代文本 */
  alt: string;
  /** 图片宽度 */
  width?: number;
  /** 图片高度 */
  height?: number;
  /** 自定义类名 */
  className?: string;
  /** 占位符 URL */
  placeholder?: string;
  /** 根元素边距（用于提前触发加载） */
  rootMargin?: string;
  /** 可见性阈值 */
  threshold?: number;
  /** 加载完成回调 */
  onLoad?: () => void;
  /** 加载失败回调 */
  onError?: () => void;
}

/**
 * 图片骨架屏组件的 Props
 */
export interface ImageSkeletonProps {
  /** 宽度 */
  width?: number | string;
  /** 高度 */
  height?: number | string;
  /** 自定义类名 */
  className?: string;
  /** 是否显示动画 */
  animate?: boolean;
}

/**
 * 事件图片管理的状态
 */
export interface EventImageState {
  /** 当前图片组 */
  currentImages: EventImageInfo[];
  /** 是否正在加载 */
  isLoading: boolean;
  /** 是否正在上传 */
  isUploading: boolean;
  /** 上传进度 */
  uploadProgress: Record<string, ImageProcessingProgress>;
  /** 错误信息 */
  error: string | null;
}

/**
 * 图片变体配置
 */
export interface ImageVariantConfig {
  name: ImageVariant;
  width: number;
  height: number;
  quality: number;
  format: 'jpeg' | 'png' | 'webp';
  description: string;
}

/**
 * 事件图片变体配置
 */
export const EVENT_IMAGE_VARIANT_CONFIGS: Record<ImageVariant, ImageVariantConfig> = {
  original: {
    name: 'original',
    width: 0, // 保持原始尺寸
    height: 0,
    quality: 95,
    format: 'jpeg',
    description: '原始尺寸，高质量'
  },
  large: {
    name: 'large',
    width: 1200,
    height: 900,
    quality: 85,
    format: 'jpeg',
    description: '大尺寸 (1200x900)，用于详情页'
  },
  medium: {
    name: 'medium',
    width: 400,
    height: 300,
    quality: 80,
    format: 'jpeg',
    description: '中等尺寸 (400x300)，用于列表页'
  },
  thumb: {
    name: 'thumb',
    width: 200,
    height: 150,
    quality: 75,
    format: 'jpeg',
    description: '缩略图 (200x150)，用于预览'
  }
};
