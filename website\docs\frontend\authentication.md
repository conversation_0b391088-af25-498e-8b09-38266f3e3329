# 认证集成指南

> 🔐 **目标**: 完整实现用户认证流程，包括登录、注册、权限验证和自动刷新

## 🎯 认证流程概览

```mermaid
sequenceDiagram
    participant F as Frontend
    participant A as API
    participant D as Database

    F->>A: POST /auth/login
    A->>D: 验证用户凭据
    D-->>A: 返回用户信息
    A-->>F: 返回JWT token
    F->>F: 存储token
    F->>A: 带token的API请求
    A->>A: 验证token
    A-->>F: 返回数据
```

## 🚀 快速实现

### 1. 认证服务封装

```typescript
// src/services/auth.ts
import { api, apiUtils } from '@/lib/api';

export interface LoginCredentials {
  username: string;
  password: string;
}

export interface RegisterData {
  username: string;
  password: string;
  email: string;
}

export interface User {
  id: string;
  username: string;
  email: string;
  role: 'admin' | 'editor' | 'viewer' | 'user';
  created_at: string;
}

export const authService = {
  // 用户登录
  async login(credentials: LoginCredentials) {
    const { data } = await api.POST('/auth/login', {
      body: credentials,
    });

    // 存储token
    apiUtils.setToken(data.token);

    return {
      user: data.user,
      token: data.token,
      expiresAt: data.expires_at,
    };
  },

  // 用户注册
  async register(userData: RegisterData) {
    const { data } = await api.POST('/auth/register', {
      body: userData,
    });

    return data;
  },

  // 退出登录
  async logout() {
    try {
      await api.POST('/auth/logout');
    } finally {
      // 无论请求是否成功都清除本地token
      apiUtils.clearToken();
    }
  },

  // 获取当前用户信息
  async getCurrentUser() {
    const { data } = await api.GET('/auth/me');
    return data.user;
  },

  // 刷新token
  async refreshToken() {
    const { data } = await api.POST('/auth/refresh');
    apiUtils.setToken(data.token);
    return data;
  },
};
```

### 2. 认证状态管理

```typescript
// src/stores/auth.ts (使用Zustand)
import { create } from 'zustand';
import { persist } from 'zustand/middleware';
import {
  authService,
  type User,
  type LoginCredentials,
  type RegisterData,
} from '@/services/auth';

interface AuthState {
  user: User | null;
  token: string | null;
  isAuthenticated: boolean;
  isLoading: boolean;

  // Actions
  login: (credentials: LoginCredentials) => Promise<void>;
  register: (userData: RegisterData) => Promise<void>;
  logout: () => Promise<void>;
  refreshUser: () => Promise<void>;
  checkAuth: () => Promise<void>;
}

export const useAuthStore = create<AuthState>()(
  persist(
    (set, get) => ({
      user: null,
      token: null,
      isAuthenticated: false,
      isLoading: false,

      login: async (credentials) => {
        set({ isLoading: true });
        try {
          const result = await authService.login(credentials);
          set({
            user: result.user,
            token: result.token,
            isAuthenticated: true,
            isLoading: false,
          });
        } catch (error) {
          set({ isLoading: false });
          throw error;
        }
      },

      register: async (userData) => {
        set({ isLoading: true });
        try {
          await authService.register(userData);
          set({ isLoading: false });
        } catch (error) {
          set({ isLoading: false });
          throw error;
        }
      },

      logout: async () => {
        set({ isLoading: true });
        try {
          await authService.logout();
        } finally {
          set({
            user: null,
            token: null,
            isAuthenticated: false,
            isLoading: false,
          });
        }
      },

      refreshUser: async () => {
        if (!get().isAuthenticated) return;

        try {
          const user = await authService.getCurrentUser();
          set({ user });
        } catch (error) {
          // Token可能已过期，执行登出
          get().logout();
        }
      },

      checkAuth: async () => {
        const { token } = get();
        if (!token) return;

        try {
          const user = await authService.getCurrentUser();
          set({ user, isAuthenticated: true });
        } catch (error) {
          set({
            user: null,
            token: null,
            isAuthenticated: false,
          });
        }
      },
    }),
    {
      name: 'auth-storage',
      partialize: (state) => ({
        token: state.token,
        user: state.user,
        isAuthenticated: state.isAuthenticated,
      }),
    }
  )
);
```

### 3. React组件示例

```typescript
// src/components/LoginForm.tsx
import React, { useState } from 'react';
import { useAuthStore } from '@/stores/auth';

export function LoginForm() {
  const [credentials, setCredentials] = useState({
    username: '',
    password: '',
  });
  const [error, setError] = useState('');

  const { login, isLoading } = useAuthStore();

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setError('');

    try {
      await login(credentials);
      // 登录成功，路由会自动跳转
    } catch (err: any) {
      setError(err.message || '登录失败');
    }
  };

  return (
    <form onSubmit={handleSubmit} className="space-y-4">
      <div>
        <label htmlFor="username">用户名</label>
        <input
          id="username"
          type="text"
          value={credentials.username}
          onChange={(e) => setCredentials(prev => ({
            ...prev,
            username: e.target.value
          }))}
          required
        />
      </div>

      <div>
        <label htmlFor="password">密码</label>
        <input
          id="password"
          type="password"
          value={credentials.password}
          onChange={(e) => setCredentials(prev => ({
            ...prev,
            password: e.target.value
          }))}
          required
        />
      </div>

      {error && (
        <div className="text-red-500">{error}</div>
      )}

      <button
        type="submit"
        disabled={isLoading}
        className="w-full bg-blue-500 text-white py-2 px-4 rounded disabled:opacity-50"
      >
        {isLoading ? '登录中...' : '登录'}
      </button>
    </form>
  );
}
```

### 4. 路由保护

```typescript
// src/components/ProtectedRoute.tsx
import React from 'react';
import { Navigate, useLocation } from 'react-router-dom';
import { useAuthStore } from '@/stores/auth';

interface ProtectedRouteProps {
  children: React.ReactNode;
  requiredRole?: 'admin' | 'editor' | 'viewer' | 'user';
}

export function ProtectedRoute({ children, requiredRole }: ProtectedRouteProps) {
  const { isAuthenticated, user } = useAuthStore();
  const location = useLocation();

  if (!isAuthenticated) {
    return <Navigate to="/login" state={{ from: location }} replace />;
  }

  if (requiredRole && user && !hasRole(user.role, requiredRole)) {
    return <Navigate to="/unauthorized" replace />;
  }

  return <>{children}</>;
}

// 角色权限检查
function hasRole(userRole: string, requiredRole: string): boolean {
  const roleHierarchy = {
    'admin': 4,
    'editor': 3,
    'viewer': 2,
    'user': 1,
  };

  return roleHierarchy[userRole as keyof typeof roleHierarchy] >=
         roleHierarchy[requiredRole as keyof typeof roleHierarchy];
}
```

## 🔄 自动刷新机制

### Token自动刷新

```typescript
// src/utils/tokenRefresh.ts
import { useAuthStore } from '@/stores/auth';
import { authService } from '@/services/auth';

let refreshPromise: Promise<any> | null = null;

export async function refreshTokenIfNeeded() {
  const { token, isAuthenticated } = useAuthStore.getState();

  if (!isAuthenticated || !token) {
    return false;
  }

  // 检查token是否即将过期（提前5分钟刷新）
  const tokenPayload = parseJWT(token);
  const expiresAt = tokenPayload.exp * 1000;
  const now = Date.now();
  const fiveMinutes = 5 * 60 * 1000;

  if (expiresAt - now > fiveMinutes) {
    return true; // Token还有效
  }

  // 避免并发刷新
  if (refreshPromise) {
    return refreshPromise;
  }

  refreshPromise = authService
    .refreshToken()
    .then(() => {
      refreshPromise = null;
      return true;
    })
    .catch(() => {
      refreshPromise = null;
      useAuthStore.getState().logout();
      return false;
    });

  return refreshPromise;
}

function parseJWT(token: string) {
  const base64Url = token.split('.')[1];
  const base64 = base64Url.replace(/-/g, '+').replace(/_/g, '/');
  const jsonPayload = decodeURIComponent(
    atob(base64)
      .split('')
      .map((c) => '%' + ('00' + c.charCodeAt(0).toString(16)).slice(-2))
      .join('')
  );
  return JSON.parse(jsonPayload);
}
```

### 请求拦截器集成

```typescript
// src/lib/api/interceptors.ts (更新版本)
import { refreshTokenIfNeeded } from '@/utils/tokenRefresh';

// 在请求拦截器中添加token刷新
async function enhancedRequestInterceptor(config: any) {
  // 尝试刷新token
  await refreshTokenIfNeeded();

  // 获取最新的token
  const token = localStorage.getItem('auth_token');

  if (token) {
    config.headers = {
      ...config.headers,
      Authorization: `Bearer ${token}`,
    };
  }

  return config;
}
```

## 🛡️ 权限控制

### 基于角色的访问控制

```typescript
// src/hooks/usePermissions.ts
import { useAuthStore } from '@/stores/auth';

export function usePermissions() {
  const { user } = useAuthStore();

  const permissions = {
    // 管理员权限
    canManageUsers: user?.role === 'admin',
    canManageEvents: ['admin', 'editor'].includes(user?.role || ''),
    canManageCircles: ['admin', 'editor'].includes(user?.role || ''),

    // 查看权限
    canViewStats: ['admin', 'editor', 'viewer'].includes(user?.role || ''),
    canViewLogs: user?.role === 'admin',

    // 基础功能
    canBookmark: !!user,
    canComment: !!user,
  };

  return permissions;
}

// 使用示例
function AdminPanel() {
  const { canManageUsers, canViewLogs } = usePermissions();

  if (!canManageUsers) {
    return <div>无权限访问</div>;
  }

  return (
    <div>
      <h1>管理面板</h1>
      {canViewLogs && <LogsSection />}
    </div>
  );
}
```

### 条件渲染组件

```typescript
// src/components/ConditionalRender.tsx
import { usePermissions } from '@/hooks/usePermissions';

interface ConditionalRenderProps {
  children: React.ReactNode;
  permission: keyof ReturnType<typeof usePermissions>;
  fallback?: React.ReactNode;
}

export function ConditionalRender({
  children,
  permission,
  fallback = null
}: ConditionalRenderProps) {
  const permissions = usePermissions();

  if (!permissions[permission]) {
    return <>{fallback}</>;
  }

  return <>{children}</>;
}

// 使用示例
<ConditionalRender permission="canManageEvents">
  <button>创建事件</button>
</ConditionalRender>
```

## 🔍 调试和测试

### 认证状态调试

```typescript
// src/utils/authDebug.ts
export function debugAuth() {
  const state = useAuthStore.getState();

  console.group('🔐 认证状态');
  console.log('已认证:', state.isAuthenticated);
  console.log('用户:', state.user);
  console.log('Token:', state.token ? '已设置' : '未设置');
  console.log('角色:', state.user?.role);
  console.groupEnd();
}

// 在开发环境中使用
if (process.env.NODE_ENV === 'development') {
  (window as any).debugAuth = debugAuth;
}
```

### 测试用例

```typescript
// src/services/__tests__/auth.test.ts
import { authService } from '../auth';
import { api } from '@/lib/api';

jest.mock('@/lib/api');

describe('AuthService', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('should login successfully', async () => {
    const mockResponse = {
      data: {
        user: { id: '1', username: 'test', role: 'user' },
        token: 'mock-token',
        expires_at: '2024-12-31T23:59:59Z',
      },
    };

    (api.POST as jest.Mock).mockResolvedValue(mockResponse);

    const result = await authService.login({
      username: 'test',
      password: 'password',
    });

    expect(result.user.username).toBe('test');
    expect(result.token).toBe('mock-token');
  });
});
```

---

**下一步**: 查看 [多语言集成指南](./i18n-integration.md) 实现国际化功能 🌐
