import { useQueryClient } from "@tanstack/react-query";

import { useDeleteAdminEventsId } from "@/api/generated/ayafeedComponents";
import { queryKeys } from "@/constants/queryKeys";
import { useAdminSuccessToast } from "@/hooks/useAdminSuccessToast";

export function useDeleteEvent() {
  const qc = useQueryClient();

  // 使用生成的 API hook
  const deleteEventMutation = useDeleteAdminEventsId({
    onSuccess: () => {
      qc.invalidateQueries({ queryKey: queryKeys.adminEvents() });
      qc.invalidateQueries({ predicate: ({ queryKey }) => Array.isArray(queryKey) && queryKey[0] === "events" });
    },
  });

  return {
    ...deleteEventMutation,
    mutate: (id: string, options?: any) => {
      deleteEventMutation.mutate(
        { pathParams: { id } },
        {
          ...options,
          onSuccess: (data, variables, context) => {
            // 显示成功提示
            if (typeof window !== 'undefined') {
              // 简单的成功提示，可以根据需要替换为 toast
              console.log('删除成功');
            }
            options?.onSuccess?.(data, variables, context);
          },
          onError: (error, variables, context) => {
            // 显示错误提示
            if (typeof window !== 'undefined') {
              console.error('删除失败:', error);
            }
            options?.onError?.(error, variables, context);
          }
        }
      );
    }
  };
}