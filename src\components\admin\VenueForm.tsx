"use client";

import { UseFormReturn } from "react-hook-form";
import { MapPin, Globe, Phone, Users, Info } from "lucide-react";

import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { <PERSON><PERSON>, <PERSON>bsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";

interface VenueFormData {
  name_en: string;
  name_ja: string;
  name_zh: string;
  address_en?: string;
  address_ja?: string;
  address_zh?: string;
  lat: number;
  lng: number;
  capacity?: number;
  website_url?: string;
  phone?: string;
  description_en?: string;
  description_ja?: string;
  description_zh?: string;
  facilities?: string;
  transportation?: string;
  parking_info?: string;
}

interface VenueFormProps {
  form: UseFormReturn<VenueFormData>;
  onSubmit: (values: VenueFormData) => void;
  isSubmitting?: boolean;
  submitText?: string;
  title?: string;
  description?: string;
}

export default function VenueForm({
  form,
  onSubmit,
  isSubmitting = false,
  submitText = "保存",
  title = "场馆信息",
  description = "请填写完整的场馆信息，支持多语言"
}: VenueFormProps) {
  const { register, handleSubmit, formState: { errors } } = form;

  return (
    <form onSubmit={handleSubmit(onSubmit)} className="space-y-6">
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <MapPin className="h-5 w-5" />
            {title}
          </CardTitle>
          <CardDescription>{description}</CardDescription>
        </CardHeader>
        <CardContent>
          <Tabs defaultValue="basic" className="space-y-6">
            <TabsList className="grid w-full grid-cols-4">
              <TabsTrigger value="basic">基本信息</TabsTrigger>
              <TabsTrigger value="location">位置信息</TabsTrigger>
              <TabsTrigger value="contact">联系方式</TabsTrigger>
              <TabsTrigger value="description">详细描述</TabsTrigger>
            </TabsList>

            {/* 基本信息 */}
            <TabsContent value="basic" className="space-y-6">
              <div className="grid gap-4">
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="name_en">英文名称 *</Label>
                    <Input
                      id="name_en"
                      placeholder="Tokyo Big Sight"
                      {...register("name_en", { required: "英文名称必填" })}
                    />
                    {errors.name_en && (
                      <p className="text-sm text-destructive">{errors.name_en.message}</p>
                    )}
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="name_ja">日文名称 *</Label>
                    <Input
                      id="name_ja"
                      placeholder="東京ビッグサイト"
                      {...register("name_ja", { required: "日文名称必填" })}
                    />
                    {errors.name_ja && (
                      <p className="text-sm text-destructive">{errors.name_ja.message}</p>
                    )}
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="name_zh">中文名称 *</Label>
                    <Input
                      id="name_zh"
                      placeholder="东京 Big Sight"
                      {...register("name_zh", { required: "中文名称必填" })}
                    />
                    {errors.name_zh && (
                      <p className="text-sm text-destructive">{errors.name_zh.message}</p>
                    )}
                  </div>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="capacity" className="flex items-center gap-2">
                    <Users className="h-4 w-4" />
                    容量（人数）
                  </Label>
                  <Input
                    id="capacity"
                    type="number"
                    placeholder="50000"
                    {...register("capacity", { valueAsNumber: true })}
                  />
                  {errors.capacity && (
                    <p className="text-sm text-destructive">{errors.capacity.message}</p>
                  )}
                </div>
              </div>
            </TabsContent>

            {/* 位置信息 */}
            <TabsContent value="location" className="space-y-6">
              <div className="grid gap-4">
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="address_en">英文地址</Label>
                    <Input
                      id="address_en"
                      placeholder="3-11-1 Ariake, Koto City, Tokyo"
                      {...register("address_en")}
                    />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="address_ja">日文地址</Label>
                    <Input
                      id="address_ja"
                      placeholder="東京都江東区有明3-11-1"
                      {...register("address_ja")}
                    />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="address_zh">中文地址</Label>
                    <Input
                      id="address_zh"
                      placeholder="东京都江东区有明3-11-1"
                      {...register("address_zh")}
                    />
                  </div>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="lat">纬度 *</Label>
                    <Input
                      id="lat"
                      type="number"
                      step="any"
                      placeholder="35.6298"
                      {...register("lat", { 
                        required: "纬度必填",
                        valueAsNumber: true,
                        min: { value: -90, message: "纬度必须在-90到90之间" },
                        max: { value: 90, message: "纬度必须在-90到90之间" }
                      })}
                    />
                    {errors.lat && (
                      <p className="text-sm text-destructive">{errors.lat.message}</p>
                    )}
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="lng">经度 *</Label>
                    <Input
                      id="lng"
                      type="number"
                      step="any"
                      placeholder="139.793"
                      {...register("lng", { 
                        required: "经度必填",
                        valueAsNumber: true,
                        min: { value: -180, message: "经度必须在-180到180之间" },
                        max: { value: 180, message: "经度必须在-180到180之间" }
                      })}
                    />
                    {errors.lng && (
                      <p className="text-sm text-destructive">{errors.lng.message}</p>
                    )}
                  </div>
                </div>
              </div>
            </TabsContent>

            {/* 联系方式 */}
            <TabsContent value="contact" className="space-y-6">
              <div className="grid gap-4">
                <div className="space-y-2">
                  <Label htmlFor="website_url" className="flex items-center gap-2">
                    <Globe className="h-4 w-4" />
                    官方网站
                  </Label>
                  <Input
                    id="website_url"
                    type="url"
                    placeholder="https://www.bigsight.jp/"
                    {...register("website_url")}
                  />
                  {errors.website_url && (
                    <p className="text-sm text-destructive">{errors.website_url.message}</p>
                  )}
                </div>

                <div className="space-y-2">
                  <Label htmlFor="phone" className="flex items-center gap-2">
                    <Phone className="h-4 w-4" />
                    联系电话
                  </Label>
                  <Input
                    id="phone"
                    placeholder="+81-3-5530-1111"
                    {...register("phone")}
                  />
                </div>
              </div>
            </TabsContent>

            {/* 详细描述 */}
            <TabsContent value="description" className="space-y-6">
              <div className="grid gap-4">
                <div className="space-y-2">
                  <Label htmlFor="description_en" className="flex items-center gap-2">
                    <Info className="h-4 w-4" />
                    英文描述
                  </Label>
                  <Textarea
                    id="description_en"
                    placeholder="Tokyo Big Sight is Japan's largest convention center..."
                    rows={4}
                    {...register("description_en")}
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="description_ja">日文描述</Label>
                  <Textarea
                    id="description_ja"
                    placeholder="東京ビッグサイトは日本最大の国際展示場です..."
                    rows={4}
                    {...register("description_ja")}
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="description_zh">中文描述</Label>
                  <Textarea
                    id="description_zh"
                    placeholder="东京Big Sight是日本最大的国际展览中心..."
                    rows={4}
                    {...register("description_zh")}
                  />
                </div>
              </div>
            </TabsContent>
          </Tabs>

          <div className="flex justify-end gap-4 mt-6 pt-6 border-t">
            <Button type="submit" disabled={isSubmitting}>
              {isSubmitting ? "保存中..." : submitText}
            </Button>
          </div>
        </CardContent>
      </Card>
    </form>
  );
}
