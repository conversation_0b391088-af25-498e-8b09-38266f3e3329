# Ayafeed

[![codecov](https://codecov.io/gh/{你的GitHub用户名}/ayafeed/branch/master/graph/badge.svg)](https://codecov.io/gh/{你的GitHub用户名}/ayafeed)

> 同人展会信息聚合平台 - 支持多语言的现代化Web应用

## 特性

- 🌐 **多语言支持** - 支持中文、日文、英文三种语言
- 🔐 **完整的认证系统** - 用户登录、注册、权限管理
- 📱 **响应式设计** - 适配桌面端和移动端
- 🚀 **现代化技术栈** - Next.js 15 + React 19 + TypeScript
- 🎨 **优雅的UI** - 基于Tailwind CSS的现代化界面
- ⚡ **高性能** - 使用React Query进行数据管理和缓存
- 🛡️ **类型安全** - 基于OpenAPI自动生成的类型定义
- 🔧 **完善的错误处理** - 统一的错误处理和用户反馈机制

## 技术栈

### 前端框架
- **Next.js 15** - React全栈框架
- **React 19** - 用户界面库
- **TypeScript** - 类型安全的JavaScript

### 样式和UI
- **Tailwind CSS** - 实用优先的CSS框架
- **Radix UI** - 无样式的可访问组件库
- **Lucide React** - 现代化图标库

### 状态管理和数据获取
- **@tanstack/react-query** - 服务器状态管理
- **Zustand** - 客户端状态管理
- **React Hook Form** - 表单状态管理

### API集成
- **@openapi-codegen** - 基于OpenAPI的代码生成
- **Zod** - 运行时类型验证

### 国际化
- **next-intl** - Next.js国际化解决方案
- **自定义i18n服务** - 多语言内容管理

### 测试
- **Vitest** - 单元测试框架
- **@testing-library/react** - React组件测试
- **jsdom** - DOM环境模拟

### 开发工具
- **ESLint** - 代码质量检查
- **Husky** - Git hooks管理
- **lint-staged** - 暂存文件检查

## 快速开始

### 环境要求

- Node.js 18+
- pnpm 8+

### 安装和运行

```bash
# 克隆项目
git clone https://github.com/your-username/ayafeed.git
cd ayafeed

# 安装依赖
pnpm install

# 配置环境变量
cp env.example .env.local
# 编辑 .env.local 文件，设置必要的环境变量

# 生成API客户端代码
pnpm gen:api

# 启动开发服务器
pnpm dev
```

### 开发命令

```bash
# 开发服务器
pnpm dev

# 构建生产版本
pnpm build

# 启动生产服务器
pnpm start

# 代码检查
pnpm lint
pnpm lint:fix

# 类型检查
pnpm typecheck

# 测试
pnpm test
pnpm test:watch
pnpm test:coverage

# API代码生成
pnpm gen:api
pnpm gen:rq
pnpm sync:api
```

## API集成

本项目使用基于OpenAPI规范的自动化API集成方案：

### 核心特性

- **类型安全** - 基于OpenAPI自动生成TypeScript类型定义
- **React Query集成** - 自动生成带缓存的数据获取hooks
- **多语言支持** - 自动处理语言头和本地化内容
- **统一错误处理** - 集成的错误处理和用户反馈机制
- **认证管理** - 自动处理JWT token和权限验证

### 使用示例

```typescript
// 获取事件列表
import { useEvents } from '@/hooks/useEvents';

function EventList() {
  const { data: events, isLoading, error } = useEvents({
    page: 1,
    pageSize: 10
  });

  if (isLoading) return <div>加载中...</div>;
  if (error) return <div>加载失败</div>;

  return (
    <div>
      {events?.items.map(event => (
        <EventCard key={event.id} event={event} />
      ))}
    </div>
  );
}
```

### 多语言支持

```typescript
// 使用本地化Hook
import { useLocalization } from '@/hooks/useLocalization';

function MyComponent() {
  const { locale, formatDate, getLocalizedText } = useLocalization();

  return (
    <div>
      <p>当前语言: {locale}</p>
      <p>格式化日期: {formatDate(new Date())}</p>
    </div>
  );
}
```

## 测试覆盖率

本项目要求测试覆盖率不低于 80%。你可以通过以下命令查看详细的覆盖率报告：

```bash
pnpm test:coverage
```

## 项目结构

```
src/
├── api/                    # 自动生成的API客户端代码
├── app/                    # Next.js App Router页面
├── components/             # React组件
│   ├── examples/          # 示例组件
│   └── ui/                # UI基础组件
├── contexts/              # React Context
├── hooks/                 # 自定义Hooks
├── lib/                   # 工具库
│   └── api/              # API相关工具
├── services/              # 业务服务层
├── stores/                # 状态管理
├── types/                 # TypeScript类型定义
└── utils/                 # 工具函数
```

## 文档

### 📚 在线文档
访问完整的在线文档：[Ayafeed 文档站点](http://localhost:3000) （开发环境）

### 核心文档
- [开发文档](./website/docs/README.md) - 项目开发指南
- [API 文档](./website/docs/api/README.md) - API接口文档
- [架构设计](./website/docs/architecture/system-design.md) - 系统架构设计

### 前端集成文档
- [快速开始](./website/docs/frontend/quick-start.md) - 5分钟上手指南
- [API客户端生成](./website/docs/frontend/client-generation.md) - 类型安全的API客户端
- [认证集成](./website/docs/frontend/authentication.md) - 用户登录和权限验证
- [多语言集成](./website/docs/frontend/i18n-integration.md) - 国际化最佳实践
- [错误处理](./website/docs/frontend/error-handling.md) - 统一错误处理方案
- [常用示例](./website/docs/frontend/common-examples.md) - 复制粘贴即用的代码

### 其他文档
- [贡献指南](./website/docs/guides/contribution.md) - 如何参与项目开发
- [架构决策记录](./website/docs/adr/README.md) - 重要技术决策记录

### 文档开发
```bash
# 启动文档开发服务器
pnpm docs:dev

# 构建文档
pnpm docs:build
```

## 贡献

欢迎提交Issue和Pull Request！请先阅读[贡献指南](./website/docs/guides/contribution.md)。

## 许可证

MIT License