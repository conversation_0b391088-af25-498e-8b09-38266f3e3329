'use client';

import { useState } from 'react';
import { EventImageUpload } from '@/components/events/EventImageUpload';
import { EventImageDisplay, EventImageGrid } from '@/components/events/EventImageDisplay';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';

// 简化的测试页面，专注于图片上传功能验证

export default function ImageUploadTestPage() {
  const [uploadedImages, setUploadedImages] = useState<any[]>([]);
  const [eventId] = useState('test-event-123');

  return (
    <div className="container mx-auto py-8 space-y-6">
      <div className="text-center">
        <h1 className="text-3xl font-bold">Event Image Management Test</h1>
        <p className="text-muted-foreground mt-2">
          测试事件图片上传和显示功能
        </p>
        <p className="text-sm text-muted-foreground">
          Event ID: <code className="bg-muted px-2 py-1 rounded">{eventId}</code>
        </p>
      </div>

      <Tabs defaultValue="upload" className="w-full">
        <TabsList className="grid w-full grid-cols-3">
          <TabsTrigger value="upload">图片上传</TabsTrigger>
          <TabsTrigger value="display">图片显示</TabsTrigger>
          <TabsTrigger value="grid">图片网格</TabsTrigger>
        </TabsList>

        <TabsContent value="upload">
          <Card>
            <CardHeader>
              <CardTitle>图片上传测试</CardTitle>
            </CardHeader>
            <CardContent>
              <EventImageUpload
                eventId={eventId}
                onUploadSuccess={(images) => {
                  console.log('Upload success:', images);
                  setUploadedImages(images);
                }}
                onUploadError={(error) => {
                  console.error('Upload error:', error);
                }}
              />
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="display">
          <Card>
            <CardHeader>
              <CardTitle>单张图片显示测试</CardTitle>
            </CardHeader>
            <CardContent className="space-y-6">
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                <div className="space-y-2">
                  <h3 className="text-sm font-medium">Thumb (200x150)</h3>
                  <EventImageDisplay
                    eventId={eventId}
                    imageType="poster"
                    variant="thumb"
                    width={200}
                    height={150}
                  />
                </div>
                <div className="space-y-2">
                  <h3 className="text-sm font-medium">Medium (400x300)</h3>
                  <EventImageDisplay
                    eventId={eventId}
                    imageType="poster"
                    variant="medium"
                    width={400}
                    height={300}
                  />
                </div>
                <div className="space-y-2">
                  <h3 className="text-sm font-medium">Large (600x450)</h3>
                  <EventImageDisplay
                    eventId={eventId}
                    imageType="poster"
                    variant="large"
                    width={600}
                    height={450}
                  />
                </div>
                <div className="space-y-2">
                  <h3 className="text-sm font-medium">Original</h3>
                  <EventImageDisplay
                    eventId={eventId}
                    imageType="poster"
                    variant="original"
                    width={300}
                    height={225}
                  />
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="grid">
          <Card>
            <CardHeader>
              <CardTitle>图片网格显示测试</CardTitle>
            </CardHeader>
            <CardContent>
              <EventImageGrid
                eventId={eventId}
                imageType="poster"
                variant="medium"
                columns={3}
                onImageClick={(image, index) => {
                  console.log('Image clicked:', image, index);
                }}
              />
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>

      {uploadedImages.length > 0 && (
        <Card>
          <CardHeader>
            <CardTitle>上传结果</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {uploadedImages.map((image, index) => (
                <div key={index} className="border rounded-lg p-4">
                  <div className="grid grid-cols-2 gap-4 text-sm">
                    <div>
                      <strong>相对路径:</strong> {image.relativePath}
                    </div>
                    <div>
                      <strong>变体:</strong> {image.variant}
                    </div>
                    <div>
                      <strong>类型:</strong> {image.imageType}
                    </div>
                    <div>
                      <strong>分组ID:</strong> {image.groupId}
                    </div>
                  </div>
                  {image.relativePath && (
                    <div className="mt-4">
                      <img
                        src={`/api/images${image.relativePath}`}
                        alt={`Uploaded ${image.variant}`}
                        className="max-w-xs rounded border"
                        onError={(e) => {
                          console.error('Image load error:', e);
                          e.currentTarget.style.display = 'none';
                        }}
                      />
                    </div>
                  )}
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  );
}
