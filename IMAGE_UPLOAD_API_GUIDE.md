# 📸 图片上传接口使用指南

## 🔗 可用接口总览

### 1. 管理员图片上传接口

- **路径：** `POST /admin/images/upload`
- **权限：** admin 或 editor 角色
- **用途：** 上传图片文件到R2存储

### 2. 公共图片查询接口

- **路径：** `GET /images/{category}/{resourceId}`
- **权限：** 公开访问
- **用途：** 获取指定资源的图片列表

### 3. 图片详情接口

- **路径：** `GET /images/{id}`
- **权限：** 公开访问
- **用途：** 获取单个图片的详细信息

### 4. 管理员图片删除接口

- **路径：** `DELETE /admin/images/`
- **权限：** admin 或 editor 角色
- **用途：** 批量删除图片

## 📤 图片上传接口详解

### 请求格式

```http
POST /admin/images/upload
Content-Type: multipart/form-data
Authorization: Bearer <your-jwt-token>
```

### 请求参数

| 字段         | 类型   | 必填 | 说明         | 示例值                                 |
| ------------ | ------ | ---- | ------------ | -------------------------------------- |
| `file`       | File   | ✅   | 图片文件     | image.jpg                              |
| `category`   | string | ✅   | 资源分类     | `event`, `circle`, `venue`             |
| `resourceId` | string | ✅   | 关联的资源ID | `reitaisai-22`                         |
| `imageType`  | string | ✅   | 图片类型     | `poster`, `logo`, `banner`, `gallery`  |
| `variant`    | string | ✅   | 图片变体     | `original`, `large`, `medium`, `thumb` |
| `groupId`    | string | ❌   | 图片组ID     | `group-uuid-456`                       |

### 支持的文件格式

- **图片格式：** JPEG, PNG, WebP, GIF
- **文件大小：** 最小100字节，最大10MB
- **尺寸限制：** 无特定限制

### 响应格式

```json
{
  "code": 0,
  "message": "图片上传成功",
  "data": {
    "id": "uuid-123",
    "groupId": "group-uuid-456",
    "relativePath": "/images/events/reitaisai-22/poster_thumb.jpeg",
    "variant": "thumb",
    "metadata": {
      "size": 51200,
      "dimensions": {
        "width": 200,
        "height": 300
      },
      "format": "jpeg"
    }
  }
}
```

## 🔍 图片查询接口详解

### 1. 按资源查询图片列表

```http
GET /images/{category}/{resourceId}?page=1&pageSize=20&variant=thumb&imageType=poster
```

**路径参数：**

- `category`: 资源分类 (`event`, `circle`, `venue`)
- `resourceId`: 资源ID

**查询参数：**

- `page`: 页码（默认1）
- `pageSize`: 每页数量（默认20）
- `variant`: 按变体筛选（可选）
- `imageType`: 按图片类型筛选（可选）

### 2. 获取单个图片详情

```http
GET /images/{id}
```

## 🗑️ 图片删除接口详解

### 请求格式

```http
DELETE /admin/images/
Content-Type: application/json
Authorization: Bearer <your-jwt-token>
```

### 请求体

```json
{
  "relativePaths": [
    "/images/events/reitaisai-22/poster_thumb.jpg",
    "/images/events/reitaisai-22/poster_large.jpg"
  ]
}
```

## 🔐 认证与权限

### 获取访问令牌

```http
POST /auth/login
Content-Type: application/json

{
  "username": "admin",
  "password": "your-password"
}
```

### 权限要求

- **图片上传/删除：** 需要 `admin` 或 `editor` 角色
- **图片查询：** 公开访问，无需认证

## 💻 前端使用示例

### 1. React + Fetch 上传示例

```javascript
async function uploadImage(file, metadata, token) {
  const formData = new FormData();
  formData.append('file', file);
  formData.append('category', metadata.category);
  formData.append('resourceId', metadata.resourceId);
  formData.append('imageType', metadata.imageType);
  formData.append('variant', metadata.variant);

  if (metadata.groupId) {
    formData.append('groupId', metadata.groupId);
  }

  const response = await fetch('/admin/images/upload', {
    method: 'POST',
    headers: {
      Authorization: `Bearer ${token}`,
    },
    body: formData,
  });

  if (!response.ok) {
    throw new Error(`Upload failed: ${response.statusText}`);
  }

  return await response.json();
}

// 使用示例
const file = document.getElementById('fileInput').files[0];
const metadata = {
  category: 'event',
  resourceId: 'reitaisai-22',
  imageType: 'poster',
  variant: 'thumb',
};

try {
  const result = await uploadImage(file, metadata, userToken);
  console.log('上传成功:', result.data);
} catch (error) {
  console.error('上传失败:', error);
}
```

### 2. Vue.js 上传组件示例

```vue
<template>
  <div>
    <input type="file" @change="handleFileSelect" accept="image/*" />
    <button @click="uploadImage" :disabled="!selectedFile">上传图片</button>
  </div>
</template>

<script>
export default {
  data() {
    return {
      selectedFile: null,
      uploading: false,
    };
  },
  methods: {
    handleFileSelect(event) {
      this.selectedFile = event.target.files[0];
    },

    async uploadImage() {
      if (!this.selectedFile) return;

      this.uploading = true;

      const formData = new FormData();
      formData.append('file', this.selectedFile);
      formData.append('category', 'event');
      formData.append('resourceId', this.$route.params.eventId);
      formData.append('imageType', 'poster');
      formData.append('variant', 'thumb');

      try {
        const response = await this.$http.post(
          '/admin/images/upload',
          formData,
          {
            headers: {
              Authorization: `Bearer ${this.$store.state.auth.token}`,
            },
          }
        );

        this.$message.success('图片上传成功');
        this.$emit('uploaded', response.data.data);
      } catch (error) {
        this.$message.error('图片上传失败');
        console.error(error);
      } finally {
        this.uploading = false;
      }
    },
  },
};
</script>
```

## 🧪 接口验证方法

### 1. 使用curl测试上传

```bash
# 先登录获取token
curl -X POST http://localhost:8787/auth/login \
  -H "Content-Type: application/json" \
  -d '{"username":"admin","password":"your-password"}'

# 上传图片
curl -X POST http://localhost:8787/admin/images/upload \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -F "file=@/path/to/image.jpg" \
  -F "category=event" \
  -F "resourceId=reitaisai-22" \
  -F "imageType=poster" \
  -F "variant=thumb"
```

### 2. 使用Postman测试

1. **设置认证：** 在Headers中添加 `Authorization: Bearer <token>`
2. **选择Body类型：** form-data
3. **添加文件：** 选择file类型，上传图片文件
4. **添加其他字段：** 设置为text类型，填入相应值

### 3. 验证图片访问

```bash
# 查询图片列表
curl http://localhost:8787/images/event/reitaisai-22

# 获取图片详情
curl http://localhost:8787/images/{image-id}
```

## ⚠️ 注意事项

### 文件限制

- 文件大小：100字节 - 10MB
- 支持格式：JPEG, PNG, WebP, GIF
- 文件名会被重新生成，不保留原始文件名

### 路径规则

图片存储路径格式：`/images/{category}/{resourceId}/{imageType}_{variant}.{format}`

示例：`/images/events/reitaisai-22/poster_thumb.jpeg`

### 错误处理

- **401 Unauthorized：** 未登录或token无效
- **403 Forbidden：** 权限不足
- **400 Bad Request：** 参数错误或文件格式不支持
- **413 Payload Too Large：** 文件过大

图片上传接口现已完全可用！🎉
