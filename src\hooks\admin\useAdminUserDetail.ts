import { useQuery } from "@tanstack/react-query";

import { queryKeys } from "@/constants/queryKeys";
import { request } from "@/lib/http";
import type { User } from "@/types/user";

export function useAdminUserDetail(id?: string) {
  return useQuery({
    enabled: !!id,
    queryKey: id ? queryKeys.adminUserDetail(id) : ["admin", "user", "unknown"],
    queryFn: () => request<User>(`/admin/users/${id}`),
    staleTime: 1000 * 60 * 5,
  });
} 