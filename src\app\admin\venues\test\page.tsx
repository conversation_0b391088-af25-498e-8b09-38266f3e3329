"use client";

import { useState } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { useAdminVenues, useCreateVenue, useDeleteVenue } from "@/hooks/admin/useVenues";

export default function VenueTestPage() {
  const [testVenue, setTestVenue] = useState({
    name_en: "Test Venue",
    name_ja: "テスト会場",
    name_zh: "测试场馆",
    address_en: "123 Test Street, Test City",
    address_ja: "テスト市テスト街123番地",
    address_zh: "测试市测试街123号",
    lat: 35.6762,
    lng: 139.6503,
    capacity: 1000,
    website_url: "https://test-venue.com",
    phone: "+81-3-1234-5678",
    description_en: "A test venue for demonstration",
    description_ja: "デモンストレーション用のテスト会場",
    description_zh: "用于演示的测试场馆",
  });

  const { data: venues, isLoading, error, refetch } = useAdminVenues({ pageSize: "10" });
  const createVenue = useCreateVenue();
  const deleteVenue = useDeleteVenue();

  const handleCreateTest = () => {
    createVenue.mutate(testVenue);
  };

  const handleDeleteTest = (id: string) => {
    deleteVenue.mutate(id);
  };

  return (
    <div className="space-y-6 p-6">
      <div>
        <h1 className="text-2xl font-bold">Venue API 测试页面</h1>
        <p className="text-muted-foreground">测试venue相关的API功能</p>
      </div>

      {/* API状态 */}
      <Card>
        <CardHeader>
          <CardTitle>API 状态</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-2">
            <div className="flex items-center gap-2">
              <span className="font-medium">加载状态:</span>
              <span className={isLoading ? "text-yellow-600" : "text-green-600"}>
                {isLoading ? "加载中..." : "已加载"}
              </span>
            </div>
            {error && (
              <div className="flex items-center gap-2">
                <span className="font-medium">错误:</span>
                <span className="text-red-600">{String(error)}</span>
              </div>
            )}
            <div className="flex items-center gap-2">
              <span className="font-medium">场馆数量:</span>
              <span>{venues?.total || 0}</span>
            </div>
            <Button onClick={() => refetch()} size="sm">
              刷新数据
            </Button>
          </div>
        </CardContent>
      </Card>

      {/* 创建测试venue */}
      <Card>
        <CardHeader>
          <CardTitle>创建测试场馆</CardTitle>
          <CardDescription>创建一个测试场馆来验证API功能</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-2 gap-4 mb-4">
            <div>
              <Label htmlFor="name_en">英文名称</Label>
              <Input
                id="name_en"
                value={testVenue.name_en}
                onChange={(e) => setTestVenue({ ...testVenue, name_en: e.target.value })}
              />
            </div>
            <div>
              <Label htmlFor="name_zh">中文名称</Label>
              <Input
                id="name_zh"
                value={testVenue.name_zh}
                onChange={(e) => setTestVenue({ ...testVenue, name_zh: e.target.value })}
              />
            </div>
            <div>
              <Label htmlFor="lat">纬度</Label>
              <Input
                id="lat"
                type="number"
                step="any"
                value={testVenue.lat}
                onChange={(e) => setTestVenue({ ...testVenue, lat: parseFloat(e.target.value) })}
              />
            </div>
            <div>
              <Label htmlFor="lng">经度</Label>
              <Input
                id="lng"
                type="number"
                step="any"
                value={testVenue.lng}
                onChange={(e) => setTestVenue({ ...testVenue, lng: parseFloat(e.target.value) })}
              />
            </div>
          </div>
          <Button 
            onClick={handleCreateTest} 
            disabled={createVenue.isPending}
            className="w-full"
          >
            {createVenue.isPending ? "创建中..." : "创建测试场馆"}
          </Button>
        </CardContent>
      </Card>

      {/* 场馆列表 */}
      <Card>
        <CardHeader>
          <CardTitle>当前场馆列表</CardTitle>
          <CardDescription>显示所有场馆，可以删除测试数据</CardDescription>
        </CardHeader>
        <CardContent>
          {isLoading ? (
            <div>加载中...</div>
          ) : venues?.items?.length ? (
            <div className="space-y-4">
              {venues.items.map((venue: any) => (
                <div key={venue.id} className="border rounded p-4">
                  <div className="flex justify-between items-start">
                    <div className="space-y-2">
                      <h3 className="font-medium">{venue.name_zh || venue.name_en}</h3>
                      <p className="text-sm text-muted-foreground">
                        ID: {venue.id}
                      </p>
                      <p className="text-sm text-muted-foreground">
                        位置: {venue.lat}, {venue.lng}
                      </p>
                      {venue.capacity && (
                        <p className="text-sm text-muted-foreground">
                          容量: {venue.capacity} 人
                        </p>
                      )}
                      {venue.address_zh && (
                        <p className="text-sm text-muted-foreground">
                          地址: {venue.address_zh}
                        </p>
                      )}
                    </div>
                    <Button
                      variant="destructive"
                      size="sm"
                      onClick={() => handleDeleteTest(venue.id)}
                      disabled={deleteVenue.isPending}
                    >
                      删除
                    </Button>
                  </div>
                </div>
              ))}
            </div>
          ) : (
            <div className="text-center py-8 text-muted-foreground">
              暂无场馆数据
            </div>
          )}
        </CardContent>
      </Card>

      {/* 原始数据 */}
      <Card>
        <CardHeader>
          <CardTitle>原始API响应</CardTitle>
          <CardDescription>显示API返回的原始数据</CardDescription>
        </CardHeader>
        <CardContent>
          <Textarea
            value={JSON.stringify(venues, null, 2)}
            readOnly
            className="min-h-[200px] font-mono text-xs"
          />
        </CardContent>
      </Card>
    </div>
  );
}
