import { describe, it, expect } from 'vitest';

import app from '@/app';

// @ts-ignore
const Request = globalThis.Request;

interface MockOptions {
  role?: 'admin' | 'editor' | 'viewer';
  uniqueConflict?: boolean;
}

/**
 * 构造可注入到 Hono Context 的模拟 D1Database
 * - 支持 batch / prepare / bind / run / first / all
 * - 覆盖 authMiddleware 与用户管理路由内涉及的 SQL 片段
 */
function createMockDB({
  role = 'admin',
  uniqueConflict = false,
}: MockOptions = {}) {
  return {
    /**
     * D1 `batch()`：批量执行多个 PreparedStatement
     * 这里只需在触发唯一约束冲突时抛错，以模拟 DB 异常。
     */
    batch: async (stmts: any[]) => {
      if (uniqueConflict) {
        // 找到插入 auth_user 的语句则抛出冲突错误
        const hasUserInsert = stmts.some((s: any) =>
          String(s.sql || '')
            .toUpperCase()
            .includes('INSERT INTO AUTH_USER')
        );
        if (hasUserInsert) {
          throw new Error('UNIQUE constraint failed: auth_user.username');
        }
      }
      // 正常返回成功结果
      return { success: true };
    },
    /**
     * D1 `prepare()`：返回 PreparedStatement 模拟对象
     */
    prepare: (query: string) => {
      const upper = query.toUpperCase();

      // 根据调用链条封装常用方法
      const buildResponse = () => ({
        // SELECT * FROM auth_session ... 用于 authMiddleware
        all: async () => {
          if (upper.includes('FROM AUTH_SESSION')) {
            return {
              results: role ? [{ id: 'u1', username: 'tester', role }] : [],
            };
          }
          return { results: [] };
        },
        // SELECT id, username, role FROM auth_user ... 用于 updateUserHandler 查询更新后记录
        first: async () => {
          if (upper.includes('SELECT ID') && upper.includes('FROM AUTH_USER')) {
            return { id: 'u1', username: 'bob', role: 'editor' };
          }
          return null;
        },
        // INSERT / UPDATE / DELETE / 其他写操作
        run: async () => {
          if (uniqueConflict && upper.includes('INSERT INTO AUTH_USER')) {
            throw new Error('UNIQUE constraint failed: auth_user.username');
          }
          return { success: true };
        },
      });

      const response = buildResponse();
      // D1 PreparedStatement 支持链式 .bind() 传参
      return {
        sql: upper, // 用于 batch 中判断 SQL 类型
        ...response,
        bind: (..._args: any[]) => ({ sql: upper, ...response }),
      };
    },
  };
}

function fetchWithEnv(req: Request, env: any) {
  return app.fetch(req, env);
}

describe('/admin/users CRUD', () => {
  const baseHeaders = {
    'Content-Type': 'application/json',
    Cookie: 'auth_session=admin_session',
  } as const;

  it('should create user successfully and return message/header', async () => {
    const body = { username: 'alice', password: 'pwd12345' };
    const req = new Request('http://localhost/admin/users', {
      method: 'POST',
      headers: baseHeaders,
      body: JSON.stringify(body),
    });

    const res = await fetchWithEnv(req, { DB: createMockDB() });
    expect(res.status).toBe(201);
    expect(res.headers.get('X-Success-Message')).toBe(
      encodeURIComponent('用户创建成功')
    );
    const json = (await res.json()) as any;
    expect(json.message).toBe('用户创建成功');
  });

  it('should reject duplicate username (unique conflict)', async () => {
    const body = { username: 'alice', password: 'pwd12345' };
    const req = new Request('http://localhost/admin/users', {
      method: 'POST',
      headers: baseHeaders,
      body: JSON.stringify(body),
    });

    const res = await fetchWithEnv(req, {
      DB: createMockDB({ uniqueConflict: true }),
    });
    expect(res.status).toBe(409);
  });

  it('should update user successfully', async () => {
    const body = { username: 'bob', role: 'editor' };
    const req = new Request('http://localhost/admin/users/u1', {
      method: 'PUT',
      headers: baseHeaders,
      body: JSON.stringify(body),
    });

    const res = await fetchWithEnv(req, { DB: createMockDB() });
    expect(res.status).toBe(200);
    expect(res.headers.get('X-Success-Message')).toBe(
      encodeURIComponent('用户信息已更新')
    );
    const json = (await res.json()) as any;
    expect(json.message).toBe('用户信息已更新');
  });

  it('should reject update when no valid fields provided', async () => {
    const body: Record<string, never> = {};
    const req = new Request('http://localhost/admin/users/u1', {
      method: 'PUT',
      headers: baseHeaders,
      body: JSON.stringify(body),
    });

    const res = await fetchWithEnv(req, { DB: createMockDB() });
    expect(res.status).toBe(422);
  });

  it('should delete user successfully', async () => {
    const req = new Request('http://localhost/admin/users/u1', {
      method: 'DELETE',
      headers: baseHeaders,
    });

    const res = await fetchWithEnv(req, { DB: createMockDB() });
    expect(res.status).toBe(204);
    expect(res.headers.get('X-Success-Message')).toBe(
      encodeURIComponent('用户已删除')
    );
  });
});
