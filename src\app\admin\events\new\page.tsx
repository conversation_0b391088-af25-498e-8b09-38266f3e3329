"use client";

import { useRouter } from "next/navigation";

import MultilingualEventForm from "@/components/admin/MultilingualEventForm";
import { useZodForm } from "@/hooks";
import { useCreateEvent } from "@/hooks/admin/useCreateEvent";
import { showApiError } from "@/lib/show-error";
import { MultilingualEventInputSchema, type MultilingualEventInput } from "@/schemas/event";

// 生成全局唯一 ID（UUID v4）
function generateId() {
  if (typeof crypto !== "undefined" && crypto.randomUUID) {
    return crypto.randomUUID();
  }
  // Fallback（极少用到）
  return "id-" + Math.random().toString(36).slice(2, 10);
}

export default function NewEventPage() {
  const router = useRouter();

  // 使用 zodResolver 进行表单校验
  const form = useZodForm(MultilingualEventInputSchema, {
    defaultValues: {
      id: "",
      name_en: "",
      name_ja: "",
      name_zh: "",
      date_en: "",
      date_ja: "",
      date_zh: "",
      date_sort: undefined,
      image_url: "",
      venue_id: "",
      url: "",
    } satisfies Partial<MultilingualEventInput>,
    mode: "onBlur",
  });

  const createEvent = useCreateEvent();

  async function onSubmit(values: MultilingualEventInput) {
    const payload = {
      ...values,
      id: values.id?.trim() ? values.id.trim() : generateId(),
      // 如果没有设置 date_sort，尝试从英文日期生成
      date_sort: values.date_sort || generateDateSort(values.date_en),
    };

    createEvent.mutate(payload, {
      onSuccess: () => {
        form.reset();
        router.push("/admin/events");
      },
      onError: (err: unknown) => { showApiError(err); },
    });
  }

  // 从日期字符串生成排序用的数字
  function generateDateSort(dateStr: string): number | undefined {
    // 尝试匹配常见的日期格式
    const match = dateStr.match(/(\d{4})[年\-/](\d{1,2})[月\-/](\d{1,2})/);
    if (match) {
      const [, year, month, day] = match;
      return parseInt(`${year}${month.padStart(2, '0')}${day.padStart(2, '0')}`);
    }

    // 尝试解析为 Date 对象
    const date = new Date(dateStr);
    if (!isNaN(date.getTime())) {
      const year = date.getFullYear();
      const month = (date.getMonth() + 1).toString().padStart(2, '0');
      const day = date.getDate().toString().padStart(2, '0');
      return parseInt(`${year}${month}${day}`);
    }

    return undefined;
  }

  return (
    <div className="container mx-auto py-6">
      <div className="mb-6">
        <h1 className="text-3xl font-bold">新增展会</h1>
        <p className="text-muted-foreground mt-2">
          请填写三种语言的展会信息，确保国际化支持
        </p>
      </div>

      <MultilingualEventForm
        form={form}
        onSubmit={onSubmit}
        isSubmitting={createEvent.isPending}
        submitText="创建展会"
        title="新建展会信息"
        description="请完整填写中文、日文、英文三种语言的展会信息"
      />
    </div>
  );
}