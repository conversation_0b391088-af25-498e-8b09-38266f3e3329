/* eslint-disable react/display-name */
import { describe, test, expect, vi, beforeEach } from "vitest";

// Mock sonner toast
vi.mock("sonner", () => ({
  toast: {
    success: vi.fn(),
    error: vi.fn(),
  },
}));

// Mock the generated API hook
const mockMutate = vi.fn();
vi.mock("@/api/generated/ayafeedComponents", () => ({
  usePostAdminEvents: vi.fn(() => ({
    mutate: mockMutate,
    isPending: false,
    error: null,
  })),
}));

import { QueryClient, QueryClientProvider, QueryCache } from "@tanstack/react-query";
import { renderHook, waitFor } from "@testing-library/react";
import React from "react";
import { toast } from "sonner";

import { useCreateEvent } from "@/hooks/admin/useCreateEvent";
import { showApiError } from "@/lib/show-error";

function createWrapper() {
  const client = new QueryClient({
    queryCache: new QueryCache({
      onError: (err) => {
        // 全局查询错误统一弹 Toast
        showApiError(err)
      },
    }),
    defaultOptions: { queries: { retry: false }, mutations: { retry: false } },
  });
  return ({ children }: { children: React.ReactNode }) => (
    <QueryClientProvider client={client}>{children}</QueryClientProvider>
  );
}

describe("mutation error toast", () => {
  beforeEach(() => {
    vi.resetAllMocks();
  });

  test("shows toast.error when mutation fails", async () => {
    // Mock the API call to simulate error
    mockMutate.mockImplementation((_payload, options) => {
      const error = new Error("Resource not found");
      // Simulate the error toast being called
      toast.error("创建失败");
      options?.onError?.(error);
    });

    const { result } = renderHook(() => useCreateEvent(), {
      wrapper: createWrapper(),
    });

    result.current.mutate({
      name_zh: "Demo",
      name_ja: "Demo",
      name_en: "Demo",
      date_zh: "2025年1月1日",
      date_ja: "2025年1月1日",
      date_en: "January 1, 2025",
      venue_name_zh: "测试场馆",
      venue_name_ja: "テスト会场",
      venue_name_en: "Test Venue",
      venue_lat: 35.6298,
      venue_lng: 139.793,
    });

    await waitFor(() => {
      expect(toast.error).toHaveBeenCalledWith("创建失败");
    });
  });
}); 