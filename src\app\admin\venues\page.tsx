"use client";

import { useState } from "react";
import { useRouter } from "next/navigation";
import { MapPin, Plus, Search, Edit, Trash2, ExternalLink } from "lucide-react";

import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { useAdminVenues, useDeleteVenue } from "@/hooks/admin/useVenues";

export default function VenuesPage() {
  const router = useRouter();
  const [keyword, setKeyword] = useState("");
  const [currentPage, setCurrentPage] = useState("1");
  const pageSize = "20";

  const { data, isLoading, error } = useAdminVenues({
    page: currentPage,
    pageSize,
    keyword: keyword || undefined,
  });

  const deleteVenue = useDeleteVenue();

  const handleSearch = (e: React.FormEvent) => {
    e.preventDefault();
    setCurrentPage("1");
  };

  const handleDelete = async (venue: any) => {
    if (!confirm(`确定要删除场馆 "${venue.name_zh || venue.name_en}" 吗？`)) {
      return;
    }
    deleteVenue.mutate(venue.id);
  };

  if (error) {
    return (
      <div className="container mx-auto py-6">
        <div className="text-center">
          <p className="text-destructive">加载失败，请重试</p>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* 页面标题和操作 */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-2xl font-bold flex items-center gap-2">
            <MapPin className="h-6 w-6" />
            场馆管理
          </h1>
          <p className="text-muted-foreground text-sm mt-1">
            管理展会场馆信息，支持多语言编辑
          </p>
        </div>
        <Button onClick={() => router.push("/admin/venues/new")}>
          <Plus className="h-4 w-4 mr-2" />
          新增场馆
        </Button>
      </div>

      {/* 搜索栏 */}
      <form onSubmit={handleSearch} className="flex gap-2">
        <Input
          type="text"
          placeholder="搜索场馆名称或地址..."
          value={keyword}
          onChange={(e) => setKeyword(e.target.value)}
          className="max-w-md"
        />
        <Button type="submit" variant="outline">
          <Search className="h-4 w-4 mr-2" />
          搜索
        </Button>
        {keyword && (
          <Button
            type="button"
            variant="ghost"
            onClick={() => {
              setKeyword("");
              setCurrentPage("1");
            }}
          >
            清除
          </Button>
        )}
      </form>

      {/* 场馆列表 */}
      {isLoading ? (
        <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
          {Array.from({ length: 6 }).map((_, i) => (
            <Card key={i} className="animate-pulse">
              <CardHeader>
                <div className="h-4 bg-muted rounded w-3/4"></div>
                <div className="h-3 bg-muted rounded w-1/2"></div>
              </CardHeader>
              <CardContent>
                <div className="space-y-2">
                  <div className="h-3 bg-muted rounded"></div>
                  <div className="h-3 bg-muted rounded w-2/3"></div>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      ) : (
        <>
          <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
            {data?.items?.map((venue: any) => (
              <Card key={venue.id} className="hover:shadow-md transition-shadow">
                <CardHeader>
                  <CardTitle className="flex items-center justify-between">
                    <span className="truncate">{venue.name_zh || venue.name_en}</span>
                    <div className="flex gap-1">
                      <Button
                        size="sm"
                        variant="ghost"
                        onClick={() => router.push(`/admin/venues/${venue.id}/edit`)}
                      >
                        <Edit className="h-4 w-4" />
                      </Button>
                      <Button
                        size="sm"
                        variant="ghost"
                        onClick={() => handleDelete(venue)}
                        disabled={deleteVenue.isPending}
                      >
                        <Trash2 className="h-4 w-4" />
                      </Button>
                    </div>
                  </CardTitle>
                  <CardDescription className="flex items-center gap-2">
                    <MapPin className="h-3 w-3" />
                    {venue.address_zh || venue.address_en || "地址未设置"}
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="space-y-3">
                    <div className="flex items-center justify-between text-sm">
                      <span className="text-muted-foreground">容量:</span>
                      <span>{venue.capacity ? `${venue.capacity.toLocaleString()} 人` : "未设置"}</span>
                    </div>
                    
                    <div className="flex items-center justify-between text-sm">
                      <span className="text-muted-foreground">坐标:</span>
                      <span className="font-mono text-xs">
                        {venue.lat.toFixed(4)}, {venue.lng.toFixed(4)}
                      </span>
                    </div>

                    {venue.website_url && (
                      <div className="flex items-center justify-between text-sm">
                        <span className="text-muted-foreground">官网:</span>
                        <a
                          href={venue.website_url}
                          target="_blank"
                          rel="noopener noreferrer"
                          className="flex items-center gap-1 text-primary hover:underline"
                        >
                          <ExternalLink className="h-3 w-3" />
                          访问
                        </a>
                      </div>
                    )}

                    <div className="flex flex-wrap gap-1 mt-2">
                      <Badge variant="secondary" className="text-xs">
                        {venue.name_en}
                      </Badge>
                      {venue.name_ja && (
                        <Badge variant="outline" className="text-xs">
                          {venue.name_ja}
                        </Badge>
                      )}
                    </div>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>

          {/* 分页 */}
          {data && data.total > parseInt(pageSize) && (
            <div className="flex justify-center gap-2 mt-6">
              <Button
                variant="outline"
                disabled={currentPage === "1"}
                onClick={() => setCurrentPage((parseInt(currentPage) - 1).toString())}
              >
                上一页
              </Button>
              <span className="flex items-center px-4 text-sm text-muted-foreground">
                第 {currentPage} 页，共 {Math.ceil(data.total / parseInt(pageSize))} 页
              </span>
              <Button
                variant="outline"
                disabled={parseInt(currentPage) >= Math.ceil(data.total / parseInt(pageSize))}
                onClick={() => setCurrentPage((parseInt(currentPage) + 1).toString())}
              >
                下一页
              </Button>
            </div>
          )}

          {/* 空状态 */}
          {data?.items?.length === 0 && (
            <Card>
              <CardContent className="text-center py-12">
                <MapPin className="h-12 w-12 mx-auto text-muted-foreground mb-4" />
                <h3 className="text-lg font-medium mb-2">暂无场馆</h3>
                <p className="text-muted-foreground mb-4">
                  {keyword ? "没有找到匹配的场馆" : "还没有添加任何场馆"}
                </p>
                <Button onClick={() => router.push("/admin/venues/new")}>
                  <Plus className="h-4 w-4 mr-2" />
                  添加第一个场馆
                </Button>
              </CardContent>
            </Card>
          )}
        </>
      )}
    </div>
  );
}
