"use client"

import React from "react"

interface Props {
  /** 捕获到的错误对象 */
  error: Error & { digest?: string }
  /** 重置边界的方法，Next.js error boundary 提供 */
  reset: () => void
  /** 当 error.message 为空时的默认文案 */
  defaultMessage?: string
}

/**
 * 页面级通用错误回退组件。
 *
 * 用法：
 * ```tsx
 * export default function Error(props: ErrorProps) {
 *   return <ErrorFallback {...props} defaultMessage="无法加载数据，请稍后重试。" />
 * }
 * ```
 */
export default function ErrorFallback({
  error,
  reset,
  defaultMessage = "发生未知错误，请稍后重试。",
}: Props) {
  return (
    <div className="max-w-7xl mx-auto px-4 py-20 text-center">
      <h2 className="text-2xl font-semibold mb-4">出现错误</h2>
      <p className="text-muted-foreground mb-6">
        {error.message || defaultMessage}
      </p>
      <button
        className="px-4 py-2 bg-primary text-primary-foreground rounded"
        onClick={reset}
      >
        重试
      </button>
    </div>
  )
} 