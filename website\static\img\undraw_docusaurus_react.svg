<svg width="200" height="200" viewBox="0 0 200 200" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <linearGradient id="react-grad" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#61DAFB;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#21759B;stop-opacity:1" />
    </linearGradient>
  </defs>
  
  <!-- React原子图标风格 -->
  <!-- 中心圆 -->
  <circle cx="100" cy="100" r="8" fill="url(#react-grad)" />
  
  <!-- 轨道椭圆 -->
  <ellipse cx="100" cy="100" rx="60" ry="25" fill="none" stroke="url(#react-grad)" stroke-width="3" />
  <ellipse cx="100" cy="100" rx="60" ry="25" fill="none" stroke="url(#react-grad)" stroke-width="3" transform="rotate(60 100 100)" />
  <ellipse cx="100" cy="100" rx="60" ry="25" fill="none" stroke="url(#react-grad)" stroke-width="3" transform="rotate(120 100 100)" />
  
  <!-- 电子点 -->
  <circle cx="160" cy="100" r="4" fill="#61DAFB" />
  <circle cx="40" cy="100" r="4" fill="#61DAFB" />
  <circle cx="130" cy="143" r="4" fill="#61DAFB" />
  <circle cx="70" cy="57" r="4" fill="#61DAFB" />
  <circle cx="130" cy="57" r="4" fill="#61DAFB" />
  <circle cx="70" cy="143" r="4" fill="#61DAFB" />
</svg>
