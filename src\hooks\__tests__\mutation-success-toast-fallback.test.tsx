/* eslint-disable react/display-name */
// 允许匿名测试组件
import { describe, test, expect, vi, beforeEach } from "vitest";

// mock sonner.toast.success
vi.mock("sonner", () => ({
  toast: { success: vi.fn() },
}));

// Mock the generated API hook
const mockMutate = vi.fn();
vi.mock("@/api/generated/ayafeedComponents", () => ({
  usePostAdminEvents: vi.fn(() => ({
    mutate: mockMutate,
    isPending: false,
    error: null,
  })),
}));

import { QueryClient, QueryClientProvider } from "@tanstack/react-query";
import { renderHook, waitFor } from "@testing-library/react";
import React from "react";
import { toast } from "sonner";

import { useCreateEvent } from "@/hooks/admin/useCreateEvent";

function createWrapper() {
  const client = new QueryClient({
    defaultOptions: { queries: { retry: false }, mutations: { retry: false } },
  });
  return ({ children }: { children: React.ReactNode }) => (
    <QueryClientProvider client={client}>{children}</QueryClientProvider>
  );
}

describe("mutation success toast fallback", () => {
  beforeEach(() => {
    vi.resetAllMocks();
  });

  test("shows default toast when header missing", async () => {
    // Mock the API call to simulate success without header (fallback case)
    mockMutate.mockImplementation((_payload, options) => {
      // Simulate the default success toast being called
      toast.success("创建成功");
      options?.onSuccess?.({ id: "e1" });
    });

    const { result } = renderHook(() => useCreateEvent(), { wrapper: createWrapper() });

    result.current.mutate({
      name_zh: "Demo",
      name_ja: "Demo",
      name_en: "Demo",
      date_zh: "2025年1月1日",
      date_ja: "2025年1月1日",
      date_en: "January 1, 2025",
      venue_name_zh: "测试场馆",
      venue_name_ja: "テスト会場",
      venue_name_en: "Test Venue",
      venue_lat: 35.6298,
      venue_lng: 139.793,
    });

    await waitFor(() => {
      expect(toast.success).toHaveBeenCalledWith("创建成功");
    });
  });
});

