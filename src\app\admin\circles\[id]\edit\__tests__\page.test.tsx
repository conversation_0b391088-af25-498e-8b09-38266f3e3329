"use client";

import { useRouter, useParams } from "next/navigation";
import React from "react";
import type { Mock } from "vitest";

import EditCirclePage from "../page";

import {
  renderWithProviders,
  screen,
  fireEvent,
  waitFor,
} from "@test/test-utils";


// ------- mocks -------
const mutateMock = vi.fn();
const sampleDetail = {
  id: "circle-1",
  name: "原社团",
  author: "Alice",
  urls: JSON.stringify({ twitter: "https://twitter.com/alice" }),
  category: "comic",
};

vi.mock("next/navigation", () => ({
  useRouter: vi.fn(),
  useParams: vi.fn(),
}));

vi.mock("@/hooks/admin/useAdminCircleDetail", () => ({
  useAdminCircleDetail: () => ({ data: sampleDetail, isLoading: false }),
}));

const createState = { isPending: false };

vi.mock("@/hooks/admin/useUpdateCircle", () => ({
  useUpdateCircle: () => ({
    mutate: mutateMock,
    get isPending() {
      return createState.isPending;
    },
  }),
}));

const mockPush = vi.fn();

describe("EditCirclePage", () => {
  beforeEach(() => {
    vi.resetAllMocks();
    (useRouter as unknown as Mock).mockReturnValue({ push: mockPush });
    (useParams as unknown as Mock).mockReturnValue({ id: "circle-1" });
  });

  test("should render form with initial values", async () => {
    renderWithProviders(<EditCirclePage />);

    expect(screen.getByDisplayValue("原社团")).toBeInTheDocument();
  });

  test("should show validation error when name cleared", async () => {
    renderWithProviders(<EditCirclePage />);

    const nameInput = screen.getByLabelText("名称 *");
    fireEvent.change(nameInput, { target: { value: "" } });

    fireEvent.click(screen.getByRole("button", { name: "保存" }));

    await waitFor(() => {
      expect(screen.getByText("名称必填")).toBeInTheDocument();
    });

    expect(mutateMock).not.toHaveBeenCalled();
  });

  test("should submit updated data and navigate on success", async () => {
    mutateMock.mockImplementation((_payload, opts) => {
      opts?.onSuccess?.();
    });

    renderWithProviders(<EditCirclePage />);

    // 修改名称
    fireEvent.change(screen.getByLabelText("名称 *"), {
      target: { value: "修改后社团" },
    });

    fireEvent.click(screen.getByRole("button", { name: "保存" }));

    await waitFor(() => {
      expect(mutateMock).toHaveBeenCalledWith(
        expect.objectContaining({
          name: "修改后社团",
        }),
        expect.any(Object)
      );
    });

    expect(mockPush).toHaveBeenCalledWith("/circles/circle-1");
  });

  test("should show loading state when mutation is pending", () => {
    createState.isPending = true;

    renderWithProviders(<EditCirclePage />);

    const button = screen.getByRole("button", { name: "保存中..." });
    expect(button).toBeDisabled();

    createState.isPending = false;
  });

  test("should display error message on mutation failure", async () => {
    mutateMock.mockImplementation((_payload, opts) => {
      opts?.onError?.({ message: "更新失败" });
    });

    renderWithProviders(<EditCirclePage />);

    fireEvent.click(screen.getByRole("button", { name: "保存" }));

    await waitFor(() => {
      expect(screen.getByText("更新失败")).toBeInTheDocument();
    });
  });
}); 