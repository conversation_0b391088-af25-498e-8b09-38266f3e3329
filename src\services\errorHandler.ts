/**
 * 统一错误处理服务
 * 基于后端文档包的错误处理规范
 */

import { ErrorType, type ProcessedError } from '@/types/errors';
import { getErrorInfo, getHttpStatusInfo } from '@/utils/errorMapping';

export class ErrorHandlerService {
  // 处理API错误
  static processApiError(error: any): ProcessedError {
    // 网络错误
    if (!error.response && (error.code === 'NETWORK_ERROR' || error.name === 'NetworkError')) {
      return {
        type: ErrorType.NETWORK_ERROR,
        message: '网络连接失败，请检查网络设置',
        shouldRetry: true,
      };
    }
    
    // HTTP状态码错误
    const status = error.status || error.response?.status;
    const data = error.data || error.response?.data;
    
    if (status) {
      switch (status) {
        case 400:
          return this.handleValidationError(data);
        case 401:
          return {
            type: ErrorType.AUTHENTICATION_ERROR,
            message: '登录已过期，请重新登录',
            shouldRetry: false,
            shouldRedirect: '/login',
          };
        case 403:
          return {
            type: ErrorType.AUTHORIZATION_ERROR,
            message: '权限不足，无法执行此操作',
            shouldRetry: false,
          };
        case 404:
          return {
            type: ErrorType.NOT_FOUND_ERROR,
            message: '请求的资源不存在',
            shouldRetry: false,
          };
        case 422:
          return this.handleValidationError(data);
        case 500:
        case 502:
        case 503: {
          const statusInfo = getHttpStatusInfo(status);
          return {
            type: statusInfo.type,
            message: statusInfo.message,
            shouldRetry: true,
          };
        }
        default:
          return this.handleBusinessError(data);
      }
    }
    
    return this.handleBusinessError(data);
  }
  
  // 处理业务错误
  private static handleBusinessError(data: any): ProcessedError {
    if (data?.code) {
      const errorInfo = getErrorInfo(data.code);
      return {
        type: errorInfo.type,
        message: data.message || errorInfo.message,
        code: data.code,
        shouldRetry: false,
      };
    }
    
    return {
      type: ErrorType.UNKNOWN_ERROR,
      message: data?.message || '未知错误',
      shouldRetry: false,
    };
  }
  
  // 处理验证错误
  private static handleValidationError(data: any): ProcessedError {
    if (data?.errors && Array.isArray(data.errors)) {
      const firstError = data.errors[0];
      return {
        type: ErrorType.VALIDATION_ERROR,
        message: firstError.message,
        field: firstError.field,
        shouldRetry: false,
      };
    }
    
    return {
      type: ErrorType.VALIDATION_ERROR,
      message: data?.message || '输入数据无效',
      shouldRetry: false,
    };
  }
  
  // 记录错误
  static logError(error: ProcessedError, context?: any) {
    console.error('Application Error:', {
      ...error,
      context,
      timestamp: new Date().toISOString(),
    });
    
    // 在生产环境中可以发送到错误监控服务
    if (process.env.NODE_ENV === 'production') {
      // 发送到Sentry、LogRocket等服务
      // this.sendToMonitoring(error, context);
    }
  }
}
