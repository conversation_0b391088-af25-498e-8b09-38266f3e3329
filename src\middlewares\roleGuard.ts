import type { MiddlewareHandler } from 'hono';

import { jsonError } from '../utils/errorResponse';

/**
 * 用户角色类型
 * 若需扩展角色，请在此处补充
 */
export type UserRole = 'admin' | 'editor' | 'viewer';

/**
 * roleGuard 高阶中间件
 * 用法：
 *   route.use(roleGuard('admin'))                 // 仅 admin
 *   route.use(roleGuard(['admin', 'editor']))     // admin + editor
 */
export function roleGuard(required?: UserRole | UserRole[]): MiddlewareHandler {
  const allowSet = required
    ? new Set(Array.isArray(required) ? required : [required])
    : null;

  return async (c, next) => {
    // adminAuth 已将 JWT payload 写入 c.set('user')
    const user = c.get('user') as { role?: string } | undefined;

    // 未登录
    if (!user?.role) {
      return jsonError(c, 20001, '未登录', 401);
    }

    // 需要特定角色但不在列表
    if (allowSet && !allowSet.has(user.role as UserRole)) {
      // 无角色或角色不匹配 → 403 Forbidden
      return jsonError(c, 20002, '权限不足', 403);
    }

    return await next();
  };
}
