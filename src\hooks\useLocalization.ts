/**
 * 本地化Hook
 * 基于后端文档包的多语言集成规范
 */

import { useI18nStore } from '@/stores/i18n';
import { i18nService } from '@/services/i18n';

export function useLocalization() {
  const { locale } = useI18nStore();
  
  return {
    locale,
    formatDate: (date: string | Date, options?: Intl.DateTimeFormatOptions) => 
      i18nService.formatDate(date, options),
    
    formatNumber: (number: number, options?: Intl.NumberFormatOptions) => 
      i18nService.formatNumber(number, options),
    
    formatRelativeTime: (date: string | Date) => 
      i18nService.formatRelativeTime(date),
    
    // 获取本地化的文本
    getLocalizedText: (textObj: Record<string, string>) => {
      return textObj[locale] || textObj['zh'] || textObj['en'] || Object.values(textObj)[0];
    },
  };
}
