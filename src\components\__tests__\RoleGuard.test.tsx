// @ts-nocheck
import { render, screen } from "@testing-library/react"
import React from "react"
import { vi, expect, test, beforeEach } from "vitest"

const replaceMock = vi.fn()

vi.mock("next/navigation", () => ({
  useRouter: () => ({ replace: replaceMock }),
}))

function mockAuth(value) {
  vi.doMock("@/contexts/user", () => ({
    useAuth: () => value,
  }))
}

beforeEach(() => {
  vi.resetAllMocks()
  vi.resetModules() // 关键：清除模块缓存
})

async function renderGuard(auth, props) {
  mockAuth(auth)
  const { default: RoleGuard } = await import("@/components/RoleGuard")
  return render(<RoleGuard {...props} />)
}

test("renders fallback while loading", async () => {
  await renderGuard({ user: null, isLoading: true }, {
    allow: ["admin"],
    fallback: <span>loading...</span>,
    children: <p>secret</p>,
  })
  expect(screen.getByText("loading...")).toBeInTheDocument()
})

test("renders children for allowed role", async () => {
  await renderGuard({ user: { id: "u1", username: "x", role: "admin" }, isLoading: false }, {
    allow: ["admin"],
    children: <p>secret</p>,
  })
  expect(screen.getByText("secret")).toBeInTheDocument()
  expect(replaceMock).not.toHaveBeenCalled()
})

test("redirects when role not allowed", async () => {
  await renderGuard({ user: { id: "u1", username: "x", role: "viewer" }, isLoading: false }, {
    allow: ["admin"],
    children: <p>secret</p>,
  })
  expect(screen.queryByText("secret")).toBeNull()
  expect(replaceMock).toHaveBeenCalledWith("/403")
}) 