import { useQueryClient } from "@tanstack/react-query";

import { queryKeys } from "@/constants/queryKeys";
import { useAdminSuccessToast } from "@/hooks/useAdminSuccessToast";
import { request } from "@/lib/http";

export function useDeleteUser() {
  const qc = useQueryClient();
  return useAdminSuccessToast(
    (id: string) => request(`/admin/users/${id}`, { method: "DELETE" }),
    {
      onSuccess: () => {
        qc.invalidateQueries({ queryKey: queryKeys.adminUsers() });
      },
    },
    "删除成功"
  );
} 