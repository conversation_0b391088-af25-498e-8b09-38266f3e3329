-- 1. 从 auth_key 表中删除管理员的凭证
DELETE FROM auth_key WHERE user_id = '26edc487-fc17-46aa-9833-10be85fef91d';

-- 2. 从 auth_user 表中删除管理员用户
DELETE FROM auth_user WHERE id = '26edc487-fc17-46aa-9833-10be85fef91d';

-- Insert the admin user into the auth_user table
-- The user ID for the admin user is a fixed UUID for easy reference.
INSERT OR IGNORE INTO auth_user (id, username, role)
VALUES ('26edc487-fc17-46aa-9833-10be85fef91d', 'admin', 'admin');

-- Insert the admin user's key and hashed password into the auth_key table
-- The password is "password"
-- The hash was generated by the /auth/hash/:password endpoint to ensure compatibility.
INSERT OR IGNORE INTO auth_key (id, user_id, hashed_password)
VALUES ('admin', '26edc487-fc17-46aa-9833-10be85fef91d', '$2b$12$LrzCNCxj56Wy/n95myW1uenixyPN4n9xxVUFJb69C/jNaZgtjzyDu');