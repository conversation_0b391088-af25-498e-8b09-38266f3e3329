/**
 * 错误提示组件
 * 基于后端文档包的错误处理规范
 */

'use client';

import React, { useEffect } from 'react';
import { useErrorStore } from '@/stores/error';
import { ErrorType } from '@/types/errors';
import type { ProcessedError } from '@/types/errors';

export function ErrorToast() {
  const { errors, removeError, isShowingError } = useErrorStore();
  
  if (!isShowingError || errors.length === 0) {
    return null;
  }
  
  return (
    <div className="fixed top-4 right-4 z-50 space-y-2">
      {errors.map((error, index) => (
        <ErrorToastItem
          key={index}
          error={error}
          onClose={() => removeError(index)}
        />
      ))}
    </div>
  );
}

interface ErrorToastItemProps {
  error: ProcessedError;
  onClose: () => void;
}

function ErrorToastItem({ error, onClose }: ErrorToastItemProps) {
  useEffect(() => {
    const timer = setTimeout(() => {
      onClose();
    }, 5000);
    
    return () => clearTimeout(timer);
  }, [onClose]);
  
  const getErrorIcon = () => {
    switch (error.type) {
      case ErrorType.NETWORK_ERROR:
        return '🌐';
      case ErrorType.AUTHENTICATION_ERROR:
        return '🔒';
      case ErrorType.AUTHORIZATION_ERROR:
        return '⛔';
      case ErrorType.VALIDATION_ERROR:
        return '⚠️';
      case ErrorType.NOT_FOUND_ERROR:
        return '🔍';
      case ErrorType.SERVER_ERROR:
        return '🔧';
      default:
        return '❌';
    }
  };
  
  const getErrorColor = () => {
    switch (error.type) {
      case ErrorType.VALIDATION_ERROR:
        return 'bg-yellow-50 border-yellow-200 text-yellow-800';
      case ErrorType.NETWORK_ERROR:
        return 'bg-blue-50 border-blue-200 text-blue-800';
      default:
        return 'bg-red-50 border-red-200 text-red-800';
    }
  };
  
  return (
    <div className={`max-w-sm w-full border rounded-lg p-4 shadow-lg ${getErrorColor()}`}>
      <div className="flex items-start">
        <div className="flex-shrink-0">
          <span className="text-lg">{getErrorIcon()}</span>
        </div>
        <div className="ml-3 flex-1">
          <p className="text-sm font-medium">{error.message}</p>
          {error.field && (
            <p className="text-xs mt-1 opacity-75">字段: {error.field}</p>
          )}
        </div>
        <div className="ml-4 flex-shrink-0">
          <button
            onClick={onClose}
            className="text-gray-400 hover:text-gray-600"
          >
            <span className="sr-only">关闭</span>
            <svg className="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
            </svg>
          </button>
        </div>
      </div>
      
      {error.shouldRetry && (
        <div className="mt-3">
          <button
            onClick={() => window.location.reload()}
            className="text-xs bg-white bg-opacity-20 hover:bg-opacity-30 px-2 py-1 rounded"
          >
            重试
          </button>
        </div>
      )}
    </div>
  );
}
