import { Context } from 'hono';

import { toggleBookmark } from './service';
import { getDB } from '@/infrastructure';
import { jsonError, jsonSuccess } from '@/utils/errorResponse';

// 切换收藏状态：POST /circles/:circleId/bookmark
export async function toggleCircleBookmark(c: Context) {
  const user = c.get('user') as { id?: string };
  if (!user?.id) {
    return jsonError(c, 20001, '未登录', 401);
  }

  const circleId = c.req.param('circleId');
  if (!circleId) {
    return jsonError(c, 40001, '缺少 circleId', 400);
  }

  const db = getDB(c);
  const { isBookmarked } = await toggleBookmark(db, user.id, circleId);

  const message = isBookmarked ? '已加入收藏' : '已取消收藏';
  return jsonSuccess(c, message, { isBookmarked });
}
