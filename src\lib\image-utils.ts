/**
 * 图片工具函数
 * 提供图片 URL 验证和处理功能
 */

/**
 * 验证并返回有效的图片 URL
 * @param imageUrl 图片 URL（可能是相对路径或完整 URL）
 * @param fallbackUrl 备用图片 URL，默认为 "/next.svg"
 * @returns 有效的图片 URL
 */
export function getValidImageUrl(
  imageUrl: string | undefined | null, 
  fallbackUrl: string = "/next.svg"
): string {
  // 如果 URL 为空或只有空白字符，返回备用图片
  if (!imageUrl || imageUrl.trim() === '') {
    return fallbackUrl;
  }
  
  // 如果是相对路径（以 / 开头），直接返回
  if (imageUrl.startsWith('/')) {
    return imageUrl;
  }
  
  // 如果是完整 URL，验证是否有效
  try {
    new URL(imageUrl);
    return imageUrl;
  } catch {
    // URL 无效，返回备用图片
    return fallbackUrl;
  }
}

/**
 * 获取事件图片 URL
 * @param eventId 事件 ID
 * @param filename 文件名，默认为 "thumb.jpg"
 * @returns 事件图片的相对路径
 */
export function getEventImageUrl(eventId: string, filename: string = 'thumb.jpg'): string {
  return `/images/events/${eventId}/${filename}`;
}

/**
 * 获取社团图片 URL
 * @param circleId 社团 ID
 * @param filename 文件名，默认为 "logo.jpg"
 * @returns 社团图片的相对路径
 */
export function getCircleImageUrl(circleId: string, filename: string = 'logo.jpg'): string {
  return `/images/circles/${circleId}/${filename}`;
}

/**
 * 获取场馆图片 URL
 * @param venueId 场馆 ID
 * @param filename 文件名，默认为 "photo.jpg"
 * @returns 场馆图片的相对路径
 */
export function getVenueImageUrl(venueId: string, filename: string = 'photo.jpg'): string {
  return `/images/venues/${venueId}/${filename}`;
}

/**
 * 获取占位符图片 URL
 * @param type 图片类型
 * @returns 占位符图片的相对路径
 */
export function getPlaceholderImageUrl(type: 'event' | 'circle' | 'venue' | 'default' = 'default'): string {
  const placeholders = {
    event: '/images/placeholders/event.svg',
    circle: '/images/placeholders/circle.svg', 
    venue: '/images/placeholders/venue.svg',
    default: '/next.svg'
  };
  
  return placeholders[type];
}

/**
 * 检查图片 URL 是否可访问
 * @param url 图片 URL
 * @returns Promise<boolean> 是否可访问
 */
export async function checkImageAvailability(url: string): Promise<boolean> {
  try {
    const response = await fetch(url, { method: 'HEAD' });
    return response.ok;
  } catch {
    return false;
  }
}

/**
 * 从完整 URL 中提取相对路径
 * @param fullUrl 完整的图片 URL
 * @returns 相对路径
 */
export function extractRelativePath(fullUrl: string): string {
  try {
    const url = new URL(fullUrl);
    return url.pathname;
  } catch {
    // 如果不是有效的 URL，假设它已经是相对路径
    return fullUrl;
  }
}
