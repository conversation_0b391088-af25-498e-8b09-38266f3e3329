"use client";

import { useState } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { useCreateEvent } from "@/hooks/admin/useCreateEvent";
import type { MultilingualEventInput } from "@/schemas/event";

export default function EventIntegrationTestPage() {
  const [testEventData, setTestEventData] = useState<MultilingualEventInput>({
    id: "test-event-" + Date.now(),
    name_en: "Test Event",
    name_ja: "テストイベント",
    name_zh: "测试活动",
    date_en: "March 15, 2025 (Sat) 10:00 - 18:00",
    date_ja: "2025年3月15日(土) 10:00 - 18:00",
    date_zh: "2025年3月15日(周六) 10:00 - 18:00",
    date_sort: 20250315,
    venue_id: "tokyo-big-sight",
    image_url: "/images/events/test-event/thumb.jpg",
    url: "https://test-event.com",
  });

  const createEvent = useCreateEvent();

  const handleSubmitTest = () => {
    console.log("提交测试Event数据:", testEventData);
    createEvent.mutate(testEventData);
  };

  const handleFieldChange = (field: keyof MultilingualEventInput, value: string | number) => {
    setTestEventData(prev => ({
      ...prev,
      [field]: value
    }));
  };

  return (
    <div className="space-y-6 p-6">
      <div>
        <h1 className="text-2xl font-bold">Event集成测试</h1>
        <p className="text-muted-foreground">测试新的event数据结构和API集成</p>
      </div>

      {/* 测试状态 */}
      <Card>
        <CardHeader>
          <CardTitle>测试状态</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-2">
            <div className="flex items-center gap-2">
              <span className="font-medium">创建状态:</span>
              <span className={createEvent.isPending ? "text-yellow-600" : createEvent.isSuccess ? "text-green-600" : createEvent.isError ? "text-red-600" : "text-gray-600"}>
                {createEvent.isPending ? "创建中..." : createEvent.isSuccess ? "创建成功" : createEvent.isError ? "创建失败" : "待测试"}
              </span>
            </div>
            {createEvent.isError && (
              <div className="text-red-600 text-sm">
                错误: {String(createEvent.error)}
              </div>
            )}
            {createEvent.isSuccess && (
              <div className="text-green-600 text-sm">
                ✅ Event创建成功！数据结构验证通过
              </div>
            )}
          </div>
        </CardContent>
      </Card>

      {/* Event数据表单 */}
      <Card>
        <CardHeader>
          <CardTitle>Event测试数据</CardTitle>
          <CardDescription>修改测试数据并提交验证</CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          {/* 基本信息 */}
          <div className="grid grid-cols-3 gap-4">
            <div>
              <Label htmlFor="name_en">英文名称</Label>
              <Input
                id="name_en"
                value={testEventData.name_en}
                onChange={(e) => handleFieldChange("name_en", e.target.value)}
              />
            </div>
            <div>
              <Label htmlFor="name_ja">日文名称</Label>
              <Input
                id="name_ja"
                value={testEventData.name_ja}
                onChange={(e) => handleFieldChange("name_ja", e.target.value)}
              />
            </div>
            <div>
              <Label htmlFor="name_zh">中文名称</Label>
              <Input
                id="name_zh"
                value={testEventData.name_zh}
                onChange={(e) => handleFieldChange("name_zh", e.target.value)}
              />
            </div>
          </div>

          {/* 日期信息 */}
          <div className="grid grid-cols-3 gap-4">
            <div>
              <Label htmlFor="date_en">英文日期</Label>
              <Input
                id="date_en"
                value={testEventData.date_en}
                onChange={(e) => handleFieldChange("date_en", e.target.value)}
              />
            </div>
            <div>
              <Label htmlFor="date_ja">日文日期</Label>
              <Input
                id="date_ja"
                value={testEventData.date_ja}
                onChange={(e) => handleFieldChange("date_ja", e.target.value)}
              />
            </div>
            <div>
              <Label htmlFor="date_zh">中文日期</Label>
              <Input
                id="date_zh"
                value={testEventData.date_zh}
                onChange={(e) => handleFieldChange("date_zh", e.target.value)}
              />
            </div>
          </div>

          {/* 其他字段 */}
          <div className="grid grid-cols-2 gap-4">
            <div>
              <Label htmlFor="venue_id">场馆ID</Label>
              <Input
                id="venue_id"
                value={testEventData.venue_id}
                onChange={(e) => handleFieldChange("venue_id", e.target.value)}
                placeholder="tokyo-big-sight"
              />
            </div>
            <div>
              <Label htmlFor="date_sort">排序日期</Label>
              <Input
                id="date_sort"
                type="number"
                value={testEventData.date_sort || ""}
                onChange={(e) => handleFieldChange("date_sort", parseInt(e.target.value) || 0)}
                placeholder="20250315"
              />
            </div>
          </div>

          <div className="grid grid-cols-2 gap-4">
            <div>
              <Label htmlFor="image_url">封面图路径</Label>
              <Input
                id="image_url"
                value={testEventData.image_url || ""}
                onChange={(e) => handleFieldChange("image_url", e.target.value)}
                placeholder="/images/events/test-event/thumb.jpg"
              />
            </div>
            <div>
              <Label htmlFor="url">官网URL</Label>
              <Input
                id="url"
                value={testEventData.url || ""}
                onChange={(e) => handleFieldChange("url", e.target.value)}
                placeholder="https://test-event.com"
              />
            </div>
          </div>

          <Button 
            onClick={handleSubmitTest} 
            disabled={createEvent.isPending}
            className="w-full"
          >
            {createEvent.isPending ? "创建中..." : "测试创建Event"}
          </Button>
        </CardContent>
      </Card>

      {/* 数据预览 */}
      <Card>
        <CardHeader>
          <CardTitle>数据结构预览</CardTitle>
          <CardDescription>当前测试数据的JSON结构</CardDescription>
        </CardHeader>
        <CardContent>
          <Textarea
            value={JSON.stringify(testEventData, null, 2)}
            readOnly
            className="min-h-[300px] font-mono text-xs"
          />
        </CardContent>
      </Card>

      {/* 验证清单 */}
      <Card>
        <CardHeader>
          <CardTitle>验证清单</CardTitle>
          <CardDescription>确认以下功能正常工作</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-2">
            <div className="flex items-center gap-2">
              <input type="checkbox" id="test1" />
              <label htmlFor="test1" className="text-sm">
                ✅ Event schema使用venue_id而不是venue多语言字段
              </label>
            </div>
            <div className="flex items-center gap-2">
              <input type="checkbox" id="test2" />
              <label htmlFor="test2" className="text-sm">
                🔧 API请求体包含venue_id字段
              </label>
            </div>
            <div className="flex items-center gap-2">
              <input type="checkbox" id="test3" />
              <label htmlFor="test3" className="text-sm">
                📝 表单验证正常工作
              </label>
            </div>
            <div className="flex items-center gap-2">
              <input type="checkbox" id="test4" />
              <label htmlFor="test4" className="text-sm">
                🚀 Event创建API调用成功
              </label>
            </div>
            <div className="flex items-center gap-2">
              <input type="checkbox" id="test5" />
              <label htmlFor="test5" className="text-sm">
                🖼️ 封面图路径字段为字符串类型
              </label>
            </div>
            <div className="flex items-center gap-2">
              <input type="checkbox" id="test6" />
              <label htmlFor="test6" className="text-sm">
                🔗 URL字段验证正常
              </label>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
