import { QueryClient, QueryClientProvider } from "@tanstack/react-query"
import { render, RenderOptions } from "@testing-library/react"
import { PropsWithChildren, ReactElement } from "react"
import { NextIntlClientProvider } from 'next-intl'

import { AuthProvider } from "@/contexts/user"

// 创建独立的 QueryClient，避免测试间状态泄漏
function createTestQueryClient() {
  return new QueryClient({
    defaultOptions: {
      queries: {
        retry: false,
        refetchOnWindowFocus: false,
      },
    },
  })
}

function Providers({ children }: PropsWithChildren) {
  const client = createTestQueryClient()

  // 测试用的简单消息对象
  const messages = {
    common: {
      loading: "Loading...",
      error: "Error occurred",
    },
    navigation: {
      home: "Home",
      events: "Events",
      circles: "Circles",
    },
    EventHeader: {
      date: "日付",
      venue: "会場",
      address: "住所",
      description: "概要",
      officialWebsite: "公式サイト",
      defaultVenueName: "会場",
      imageAlt: "イベントポスター",
      mapLoading: "地図を読み込み中...",
      mapError: "地図の読み込みに失敗しました"
    },
  }

  return (
    <QueryClientProvider client={client}>
      <NextIntlClientProvider locale="ja" messages={messages}>
        <AuthProvider>{children}</AuthProvider>
      </NextIntlClientProvider>
    </QueryClientProvider>
  )
}

export function renderWithProviders(
  ui: ReactElement,
  options?: Omit<RenderOptions, "wrapper">
) {
  return render(ui, { wrapper: Providers, ...options })
}

// 重新导出测试库方法，方便引用
export * from "@testing-library/react" 