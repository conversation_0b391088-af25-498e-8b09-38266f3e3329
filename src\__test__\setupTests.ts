import "@testing-library/jest-dom/vitest"
import { afterAll, afterEach, beforeAll } from "vitest"

import { server } from "./testServer"

beforeAll(() => { server.listen(); })
// 给所有测试提供默认 API 地址，避免 http.ts 抛异常
process.env.NEXT_PUBLIC_API_URL ??= "http://127.0.0.1:8787"
afterEach(() => { server.resetHandlers(); })
afterAll(() => { server.close(); })

// 可以在此处扩展其他全局mock，例如mock Date 或 localStorage

// 如需集成 MSW，可在此处引入 server 并挂载钩子 

// ---------- 全局模拟 localStorage 与 window.location ----------

// mock localStorage
const localStorageMock = (() => {
  let store: Record<string, string> = {}
  return {
    getItem: (key: string) => store[key] ?? null,
    setItem: (key: string, value: string) => {
      store[key] = value.toString()
    },
    clear: () => {
      store = {}
    },
    removeItem: (key: string) => {
      delete store[key]
    },
  }
})()
Object.defineProperty(window, "localStorage", { value: localStorageMock })
;(globalThis as any).localStorageMock = localStorageMock

// mock window.location（仅可写 href 即可满足测试需求）
const locationMock = {
  ...window.location,
  href: "",
}
Object.defineProperty(window, "location", {
  value: locationMock,
  writable: true,
})
;(globalThis as any).locationMock = locationMock

// ---------------------------------------------------------------- 

// ------ 全局 Mock next/image ------
// 解决 Invalid base URL 错误：在测试环境中将 next/image 替换为普通 <img /> 标签
import React from "react";
import { vi } from "vitest";

vi.mock("next/image", () => {
  const NextImage = React.forwardRef<HTMLImageElement, any>((props, ref) => {
    const { src, alt, ...rest } = props;
    return React.createElement("img", { ref, src, alt, ...rest });
  });
  NextImage.displayName = "NextImageMock";
  return {
    __esModule: true,
    default: NextImage,
  };
});
// ---------------------------------------------------- 