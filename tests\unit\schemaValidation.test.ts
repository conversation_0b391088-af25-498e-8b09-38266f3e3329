import { describe, it, expect } from 'vitest';

import { appearanceSchema } from '@/modules/appearance/schema';
import { artistSchema } from '@/modules/artist/schema';
import { statsResponseSchema } from '@/modules/stats/schema';

function shouldPass(schema: any, data: any) {
  expect(() => schema.parse(data)).not.toThrow();
}

function shouldFail(schema: any, data: any) {
  expect(() => schema.parse(data)).toThrow();
}

describe('DTO Schema Validation', () => {
  it('artistSchema validates correctly', () => {
    shouldPass(artistSchema, {
      id: 'uuid-123',
      name: 'Alice',
      urls: null,
      created_at: '2024-01-01T00:00:00Z',
      updated_at: '2024-01-01T00:00:00Z',
      description: null,
    });

    shouldFail(artistSchema, {
      name: 'Alice', // 缺少必要字段 id
    });
  });

  it('appearanceSchema validates correctly', () => {
    shouldPass(appearanceSchema, {
      id: 'uuid-123',
      circle_id: 'circle-uuid',
      event_id: 'event-uuid',
      artist_id: null,
      booth_id: 'A01a',
      path: '/2025/05/03/A1.jpg',
      created_at: '2024-01-01T00:00:00Z',
      updated_at: '2024-01-01T00:00:00Z',
    });

    shouldFail(appearanceSchema, {
      id: 'uuid-123',
      circle_id: 'circle-uuid',
      event_id: 'event-uuid',
      // booth_id 缺失，应报错
      created_at: '2024-01-01T00:00:00Z',
      updated_at: '2024-01-01T00:00:00Z',
    });
  });

  it('statsResponseSchema validates correctly', () => {
    shouldPass(statsResponseSchema, {
      totals: {
        circles: 10,
        artists: 20,
        events: 5,
      },
      year: 2025,
      eventsByMonth: [
        { month: '01', count: 2 },
        { month: '02', count: 3 },
      ],
    });

    shouldFail(statsResponseSchema, {
      totals: { circles: '10' }, // circles 应为 number
      year: '2025', // year 应为 number
      eventsByMonth: [],
    });
  });
});
