import { Context, Next } from 'hono';
import { getCookie, setCookie } from 'hono/cookie';

import { SESSION_COOKIE_NAME, validateSession } from '../modules/auth/service';

/**
 * Define the shape of the auth context that will be available
 * in <PERSON><PERSON>'s context (`c.var.auth`).
 */
type AuthContext = {
  user: {
    id: string;
    username: string;
    role: string;
  } | null;
  session: {
    id: string;
  } | null;
};

/**
 * Hono middleware for handling session validation.
 *
 * It reads the session cookie, validates it using AuthService,
 * and injects the authentication context (`c.var.auth`) into the request.
 * This makes user and session information available to all downstream handlers.
 *
 * It also defines a `c.auth.protect()` utility for easily securing routes.
 */
export const authMiddleware = () => {
  return async (c: Context, next: Next) => {
    const db = c.env.DB;
    const sessionId = getCookie(c, SESSION_COOKIE_NAME);

    // Initialize context with null user/session
    const authContext: AuthContext = {
      user: null,
      session: null,
    };

    if (sessionId) {
      const user = await validateSession(db, sessionId);
      if (user) {
        authContext.user = user;
        authContext.session = { id: sessionId };
        // 兼容 roleGuard：直接把用户信息写入 c.var.user
        c.set('user', {
          id: user.id,
          role: user.role,
          username: user.username,
        });
      } else {
        // Clear invalid cookie from the browser
        setCookie(c, SESSION_COOKIE_NAME, '', {
          path: '/',
          maxAge: 0, // Expire immediately
        });
      }
    }

    // Make auth context available as `c.var.auth`
    c.set('auth', authContext);

    await next();
  };
};

// ---------------- legacy notice ----------------
// protect() 已废弃，请统一使用 roleGuard()，如仍调用将抛出错误。
