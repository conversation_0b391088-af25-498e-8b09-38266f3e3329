'use client';

import { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';

export default function VenueEditTestPage() {
  const [testResults, setTestResults] = useState<string[]>([]);

  const addResult = (result: string) => {
    setTestResults(prev => [...prev, `${new Date().toLocaleTimeString()}: ${result}`]);
  };

  const testVenueEdit = () => {
    addResult('🧪 开始测试场馆编辑功能...');
    addResult('📝 请手动执行以下步骤：');
    addResult('1. 打开 /admin/venues 页面');
    addResult('2. 点击任意场馆的"编辑"按钮');
    addResult('3. 修改任意字段（如名称）');
    addResult('4. 点击"保存更改"按钮');
    addResult('5. 观察是否：');
    addResult('   ✅ 保留在编辑页面（不跳转到列表页）');
    addResult('   ✅ 显示"修改成功"提示');
    addResult('   ✅ 如果出错，显示错误提示');
  };

  const testEventEdit = () => {
    addResult('🧪 开始测试展会编辑功能...');
    addResult('📝 请手动执行以下步骤：');
    addResult('1. 打开 /admin/events 页面');
    addResult('2. 点击任意展会的"编辑"按钮');
    addResult('3. 修改任意字段');
    addResult('4. 点击"保存更改"按钮');
    addResult('5. 观察是否：');
    addResult('   ✅ 保留在编辑页面（不跳转到列表页）');
    addResult('   ✅ 显示成功或错误提示');
  };

  const clearResults = () => {
    setTestResults([]);
  };

  return (
    <div className="container mx-auto py-8 max-w-4xl">
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            🔧 Admin 编辑页面修复测试
            <Badge variant="outline">修复验证</Badge>
          </CardTitle>
          <p className="text-sm text-muted-foreground">
            验证编辑页面不再自动跳转，并正确显示成功/错误提示
          </p>
        </CardHeader>
        <CardContent className="space-y-6">
          {/* 修复说明 */}
          <div className="p-4 bg-blue-50 border border-blue-200 rounded-lg">
            <h3 className="font-medium text-blue-900 mb-2">🛠️ 已修复的问题</h3>
            <ul className="text-sm text-blue-800 space-y-1">
              <li>• <strong>场馆编辑</strong>：移除成功后的强制跳转，保留在编辑页面</li>
              <li>• <strong>展会编辑</strong>：移除成功后的强制跳转，保留在编辑页面</li>
              <li>• <strong>错误处理</strong>：统一使用 useAdminSuccessToast 处理成功/错误提示</li>
              <li>• <strong>用户体验</strong>：用户可以连续编辑，看到即时反馈</li>
            </ul>
          </div>

          {/* 测试按钮 */}
          <div className="flex gap-4">
            <Button onClick={testVenueEdit} className="flex-1">
              🏢 测试场馆编辑
            </Button>
            <Button onClick={testEventEdit} variant="outline" className="flex-1">
              🎪 测试展会编辑
            </Button>
            <Button onClick={clearResults} variant="ghost">
              🗑️ 清空结果
            </Button>
          </div>

          {/* 快速链接 */}
          <div className="grid grid-cols-2 gap-4">
            <Card>
              <CardContent className="p-4">
                <h4 className="font-medium mb-2">🏢 场馆管理</h4>
                <div className="space-y-2">
                  <a 
                    href="/admin/venues" 
                    target="_blank"
                    className="block text-sm text-blue-600 hover:underline"
                  >
                    → 场馆列表页面
                  </a>
                  <p className="text-xs text-muted-foreground">
                    点击任意场馆的编辑按钮进行测试
                  </p>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardContent className="p-4">
                <h4 className="font-medium mb-2">🎪 展会管理</h4>
                <div className="space-y-2">
                  <a 
                    href="/admin/events" 
                    target="_blank"
                    className="block text-sm text-blue-600 hover:underline"
                  >
                    → 展会列表页面
                  </a>
                  <p className="text-xs text-muted-foreground">
                    点击任意展会的编辑按钮进行测试
                  </p>
                </div>
              </CardContent>
            </Card>
          </div>

          {/* 测试结果 */}
          {testResults.length > 0 && (
            <div className="mt-6 p-4 bg-muted rounded-lg">
              <h3 className="font-medium mb-2">📋 测试步骤：</h3>
              <div className="space-y-1">
                {testResults.map((result, index) => (
                  <div key={index} className="text-sm font-mono">
                    {result}
                  </div>
                ))}
              </div>
            </div>
          )}

          {/* 预期行为 */}
          <div className="p-4 bg-green-50 border border-green-200 rounded-lg">
            <h3 className="font-medium text-green-900 mb-2">✅ 预期行为</h3>
            <ul className="text-sm text-green-800 space-y-1">
              <li>• 编辑成功后，<strong>保留在当前编辑页面</strong></li>
              <li>• 显示绿色的"修改成功"提示（右上角或页面内）</li>
              <li>• 如果出错，显示红色错误提示，不跳转</li>
              <li>• 用户可以继续编辑或手动返回列表页</li>
            </ul>
          </div>

          {/* 技术细节 */}
          <details className="p-4 bg-gray-50 border border-gray-200 rounded-lg">
            <summary className="font-medium cursor-pointer">🔍 技术实现细节</summary>
            <div className="mt-2 text-sm text-gray-700 space-y-2">
              <p><strong>修改前：</strong></p>
              <pre className="bg-gray-100 p-2 rounded text-xs overflow-x-auto">
{`// 问题代码
const onSubmit = async (values) => {
  try {
    await updateVenue.mutateAsync(values);
    router.push("/admin/venues"); // 强制跳转！
  } catch (error) {
    console.error(error); // 只在控制台显示错误
  }
};`}
              </pre>
              
              <p><strong>修改后：</strong></p>
              <pre className="bg-gray-100 p-2 rounded text-xs overflow-x-auto">
{`// 修复后的代码
const onSubmit = (values) => {
  updateVenue.mutate(values); // 让 hook 处理成功/错误
};`}
              </pre>
              
              <p><strong>useAdminSuccessToast 自动处理：</strong></p>
              <ul className="list-disc list-inside text-xs space-y-1">
                <li>成功时显示"修改成功"提示</li>
                <li>错误时显示具体错误信息</li>
                <li>自动刷新相关缓存数据</li>
              </ul>
            </div>
          </details>
        </CardContent>
      </Card>
    </div>
  );
}
