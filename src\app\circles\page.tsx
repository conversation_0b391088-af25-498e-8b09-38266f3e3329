"use client";

import { useState, useCallback } from "react";
import { useInfiniteCircles } from "@/hooks/useInfiniteCircles";
import CirclesList from "@/components/circles/CirclesList";
import SearchBar from "@/components/circles/SearchBar";

export default function CirclesPage() {
  const [searchKeyword, setSearchKeyword] = useState("");

  // 使用无限滚动 hook
  const {
    circles,
    isLoading,
    isSearching,
    isLoadingMore,
    hasMore,
    error,
    loadMore,
    total,
  } = useInfiniteCircles({
    pageSize: 50,
    searchKeyword: searchKeyword,
  });

  const handleResetFilters = useCallback(() => {
    setSearchKeyword("");
  }, []);

  const handleSearchChange = useCallback((value: string) => {
    setSearchKeyword(value);
  }, []);

  if (error) {
    throw error;
  }

  // 结构化数据
  const structuredData = {
    "@context": "https://schema.org",
    "@type": "CollectionPage",
    "name": "社团列表",
    "description": "浏览所有参与同人展会的社团信息，发现你喜欢的创作者和作品",
    "url": `${process.env.NEXT_PUBLIC_SITE_URL || "https://ayafeed.com"}/circles`,
    "mainEntity": {
      "@type": "ItemList",
      "numberOfItems": total || 0,
      "itemListElement": circles.slice(0, 10).map((circle, index) => ({
        "@type": "Organization",
        "position": index + 1,
        "name": circle.name,
        "url": `${process.env.NEXT_PUBLIC_SITE_URL || "https://ayafeed.com"}/circles/${circle.id}`,
        ...(circle.category && { "category": circle.category }),
      })),
    },
    "breadcrumb": {
      "@type": "BreadcrumbList",
      "itemListElement": [
        {
          "@type": "ListItem",
          "position": 1,
          "name": "首页",
          "item": process.env.NEXT_PUBLIC_SITE_URL || "https://ayafeed.com",
        },
        {
          "@type": "ListItem",
          "position": 2,
          "name": "社团列表",
          "item": `${process.env.NEXT_PUBLIC_SITE_URL || "https://ayafeed.com"}/circles`,
        },
      ],
    },
  };

  return (
    <>
      {/* 结构化数据 */}
      <script
        type="application/ld+json"
        dangerouslySetInnerHTML={{ __html: JSON.stringify(structuredData) }}
      />

      <div className="min-h-screen bg-background">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6 sm:py-8">
        {/* 页面标题 */}
        <header className="mb-6 sm:mb-8">
          <h1 className="text-2xl sm:text-3xl font-bold mb-2">社团列表</h1>
          <p className="text-sm sm:text-base text-muted-foreground">
            浏览所有参与同人展会的社团信息
          </p>
        </header>

        {/* 搜索区域 */}
        <SearchBar
          searchKeyword={searchKeyword}
          onSearchChange={handleSearchChange}
          onReset={handleResetFilters}
          isSearching={isSearching}
        />

        {/* 结果统计 */}
        <div className="mb-4 sm:mb-6">
          <div className="flex items-center justify-between">
            <p className="text-sm text-muted-foreground" role="status" aria-live="polite">
              {isLoading ? (
                "加载中..."
              ) : (
                <>
                  显示 {circles.length} 个社团
                  {total > 0 && ` (共 ${total} 个)`}
                  {searchKeyword && (
                    <span className="ml-2 text-primary">
                      搜索: "{searchKeyword}"
                    </span>
                  )}
                </>
              )}
            </p>
            {isLoadingMore && (
              <div className="flex items-center gap-2 text-xs text-muted-foreground">
                <div className="animate-spin rounded-full h-3 w-3 border-b border-primary" />
                加载中
              </div>
            )}
          </div>
        </div>

        {/* 社团列表 */}
        <main className="mb-6 sm:mb-8" aria-label="社团列表">
          <CirclesList
            circles={circles}
            isLoading={isLoading}
            isLoadingMore={isLoadingMore}
            hasMore={hasMore}
            searchKeyword={searchKeyword}
            onLoadMore={loadMore}
            onResetFilters={handleResetFilters}
            total={total}
          />
        </main>
        </div>
      </div>
    </>
  );
}
