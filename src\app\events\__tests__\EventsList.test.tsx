/* eslint-disable @next/next/no-img-element */
import { beforeEach, expect, test, vi } from "vitest";
import type { Mock } from "vitest";

import EventsList from "@/app/events/EventsList";
import { renderWithProviders, screen } from "@test/test-utils";

// Mock Next.js specific components
vi.mock("next/image", () => ({
  default: (props: any) => {
    // eslint-disable-next-line jsx-a11y/alt-text
    return <img {...props} />;
  },
}));
vi.mock("next/link", () => ({
  default: (props: any) => {
    const { href, children, ...rest } = props;
    return (
      <a href={href} {...rest}>
        {children}
      </a>
    );
  },
}));

// Mock API hook
// 使用 var 来避免 TDZ
var mockUseGetEvents: Mock;
vi.mock("@/api/generated/ayafeedComponents", () => {
  // 先创建局部 mock，再赋值到外部变量，避免提前引用
  const useGetEventsMock = vi.fn();
  mockUseGetEvents = useGetEventsMock;
  return {
    __esModule: true,
    useGetEvents: useGetEventsMock,
  };
});

beforeEach(() => {
  vi.clearAllMocks();
});

test("renders empty state when no events", () => {
  mockUseGetEvents.mockReturnValue({
    data: { items: [], total: 0 },
    isLoading: false,
    isFetching: false,
  });

  renderWithProviders(<EventsList />);

  expect(screen.getByText("暂无展会信息")).toBeInTheDocument();
});

test("renders event cards when data loaded", () => {
  const sampleEvent = {
    id: "evt1",
    name: "例大祭",
    date: "2025-05-04",
    venue_name: "東京ビッグサイト",
    image_url: "/poster.png",
  };

  mockUseGetEvents.mockReturnValue({
    data: { items: [sampleEvent], total: 1 },
    isLoading: false,
    isFetching: false,
  });

  renderWithProviders(<EventsList />);

  expect(screen.getByText("例大祭")).toBeInTheDocument();
  expect(screen.getByRole("img")).toHaveAttribute("src", sampleEvent.image_url);
});

test("shows load more button when has more pages", () => {
  mockUseGetEvents.mockReturnValue({
    data: { items: [], total: 30 },
    isLoading: false,
    isFetching: false,
  });

  renderWithProviders(<EventsList />);

  expect(screen.getByRole("button", { name: "加载更多" })).toBeInTheDocument();
}); 