import { cn } from '@/lib/utils'

interface SkeletonProps {
  className?: string
}

export function Skeleton({ className }: SkeletonProps) {
  return (
    <div
      className={cn(
        "skeleton rounded-md",
        className
      )}
    />
  )
}

/**
 * 增强版事件头部骨架屏
 */
export function EventHeaderSkeleton() {
  return (
    <header className="bg-background text-foreground">
      {/* 英雄区域骨架 */}
      <div className="hero-gradient">
        <div className="max-w-7xl mx-auto px-4 py-12">
          <div className="grid grid-cols-1 lg:grid-cols-[auto_1fr] gap-10 items-start">
            {/* 左侧：海报骨架 */}
            <div className="lg:w-80">
              <Skeleton className="w-full aspect-[4/5.6] rounded-md" />
            </div>

            {/* 右侧：信息骨架 */}
            <div className="space-y-6">
              {/* 状态徽章骨架 */}
              <Skeleton className="h-6 w-20 rounded-full" />

              {/* 标题骨架 */}
              <div className="space-y-2">
                <Skeleton className="h-12 w-3/4" />
                <Skeleton className="h-6 w-1/2" />
              </div>

              {/* 描述骨架 */}
              <div className="space-y-2">
                <Skeleton className="h-4 w-full" />
                <Skeleton className="h-4 w-4/5" />
                <Skeleton className="h-4 w-3/5" />
              </div>

              {/* 按钮骨架 */}
              <div className="flex flex-wrap gap-3">
                <Skeleton className="h-10 w-24" />
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* 快速信息卡片骨架 */}
      <div className="border-b bg-background/95 backdrop-blur">
        <div className="max-w-7xl mx-auto px-4 py-6">
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
            {Array.from({ length: 4 }).map((_, i) => (
              <div key={i} className="flex items-center space-x-3 p-4 rounded-lg bg-card border">
                <Skeleton className="h-5 w-5 rounded" />
                <div className="min-w-0 flex-1 space-y-2">
                  <Skeleton className="h-3 w-12" />
                  <Skeleton className="h-4 w-16" />
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>

      {/* 地图部分骨架 */}
      <div className="max-w-7xl mx-auto px-4 py-10">
        <div className="space-y-6">
          <Skeleton className="h-8 w-32" />
          <div className="map-container">
            <Skeleton className="h-[24rem] w-full" />
          </div>
        </div>
      </div>
    </header>
  )
}

/**
 * 标签页内容骨架屏
 */
export function TabContentSkeleton() {
  return (
    <div className="space-y-6">
      {/* 筛选栏骨架 */}
      <div className="p-6 bg-card rounded-lg border space-y-4">
        <div className="flex flex-col sm:flex-row gap-4 items-start sm:items-center justify-between">
          <Skeleton className="h-10 w-80" />
          <div className="flex items-center gap-2">
            <Skeleton className="h-10 w-32" />
            <div className="flex border rounded-md">
              {Array.from({ length: 3 }).map((_, i) => (
                <Skeleton key={i} className="h-8 w-8 rounded-none first:rounded-l-md last:rounded-r-md" />
              ))}
            </div>
          </div>
        </div>
        
        <div className="space-y-3">
          <Skeleton className="h-5 w-20" />
          <div className="flex flex-wrap gap-2">
            {Array.from({ length: 6 }).map((_, i) => (
              <Skeleton key={i} className="h-6 w-16 rounded-full" />
            ))}
          </div>
        </div>
      </div>

      {/* 网格内容骨架 */}
      <div className="grid grid-cols-[repeat(auto-fill,minmax(220px,1fr))] gap-4">
        {Array.from({ length: 12 }).map((_, i) => (
          <div key={i} className="card-hover p-4 rounded-lg bg-card border space-y-3">
            <div className="flex justify-between items-start">
              <Skeleton className="h-5 w-24" />
              <Skeleton className="h-5 w-12 rounded-full" />
            </div>
            <div className="space-y-2">
              <Skeleton className="h-4 w-full" />
              <Skeleton className="h-4 w-3/4" />
            </div>
            <div className="flex flex-wrap gap-1">
              {Array.from({ length: 2 }).map((_, j) => (
                <Skeleton key={j} className="h-4 w-12 rounded-full" />
              ))}
            </div>
          </div>
        ))}
      </div>
    </div>
  )
}

/**
 * 概览标签页骨架屏
 */
export function OverviewTabSkeleton() {
  return (
    <div className="grid gap-6 md:grid-cols-2">
      {Array.from({ length: 2 }).map((_, i) => (
        <div key={i} className="card-hover p-6 rounded-lg bg-card border space-y-4">
          <div className="flex items-center gap-2">
            <Skeleton className="h-5 w-5" />
            <Skeleton className="h-6 w-24" />
          </div>
          <div className="space-y-3">
            {Array.from({ length: 4 }).map((_, j) => (
              <div key={j} className="flex justify-between items-center">
                <Skeleton className="h-4 w-16" />
                <Skeleton className="h-4 w-20" />
              </div>
            ))}
          </div>
          <div className="space-y-2">
            <Skeleton className="h-4 w-20" />
            <Skeleton className="h-3 w-full" />
            <Skeleton className="h-3 w-4/5" />
          </div>
        </div>
      ))}
    </div>
  )
}

/**
 * 会场信息标签页骨架屏
 */
export function VenueTabSkeleton() {
  return (
    <div className="space-y-6">
      <div className="card-hover p-6 rounded-lg bg-card border space-y-4">
        <div className="space-y-2">
          <Skeleton className="h-6 w-24" />
          <Skeleton className="h-4 w-48" />
        </div>
        <div className="map-container">
          <Skeleton className="h-[400px] w-full" />
        </div>
      </div>
    </div>
  )
}

/**
 * 交通住宿标签页骨架屏
 */
export function TravelTabSkeleton() {
  return (
    <div className="grid gap-6 md:grid-cols-2">
      {Array.from({ length: 2 }).map((_, i) => (
        <div key={i} className="card-hover p-6 rounded-lg bg-card border space-y-4">
          <div className="flex items-center gap-2">
            <Skeleton className="h-5 w-5" />
            <Skeleton className="h-6 w-20" />
          </div>
          <div className="space-y-4">
            {Array.from({ length: 2 }).map((_, j) => (
              <div key={j} className="space-y-2">
                <Skeleton className="h-4 w-16" />
                <div className="space-y-1">
                  {Array.from({ length: 3 }).map((_, k) => (
                    <Skeleton key={k} className="h-3 w-full" />
                  ))}
                </div>
              </div>
            ))}
          </div>
        </div>
      ))}
    </div>
  )
}
