# 模块文档索引

> 本节包含Ayafeed API各个功能模块的详细文档，每个模块都有独立的API说明、使用示例和最佳实践。

## 📋 模块列表

| 模块         | 状态      | 功能描述             | 文档链接                           |
| ------------ | --------- | -------------------- | ---------------------------------- |
| **搜索模块** | ✅ 已实现 | 多语言搜索事件和社团 | [search.md](./search.md)           |
| **Feed模块** | ✅ 已实现 | 多语言内容流和分页   | [feed.md](./feed.md)               |
| **事件模块** | ✅ 已实现 | 事件管理和查询       | [events.md](./events.md)           |
| **社团模块** | ✅ 已实现 | 社团管理和查询       | [circles.md](./circles.md)         |
| **用户模块** | ✅ 已实现 | 用户认证和管理       | [users.md](./users.md)             |
| **收藏模块** | ✅ 已实现 | 用户收藏功能         | [bookmarks.md](./bookmarks.md)     |
| **作者模块** | ✅ 已实现 | 作者信息管理         | [artists.md](./artists.md)         |
| **出展模块** | ✅ 已实现 | 作者出展记录         | [appearances.md](./appearances.md) |

## 🚀 新增模块 (v0.4.3+)

### 搜索模块

- **端点**: `GET /search`
- **功能**: 多语言全文搜索
- **特性**: 相关性评分、缓存优化、类型过滤
- **文档**: [search.md](./search.md)

### Feed模块

- **端点**: `GET /feed`
- **功能**: 多语言内容流
- **特性**: 分页支持、时间排序、类型过滤
- **文档**: [feed.md](./feed.md)

## 🌐 多语言支持

所有模块都支持多语言功能：

- **支持语言**: `en` (English), `zh` (中文), `ja` (日本語)
- **语言检测**: X-Locale > Cookie > Accept-Language > 默认语言
- **缓存隔离**: 每种语言独立缓存
- **标准响应**: 包含locale字段和时间戳

## 📖 文档规范

每个模块文档包含以下标准章节：

1. **概述** - 模块功能和特性简介
2. **API端点** - 详细的接口说明
3. **数据结构** - 请求和响应格式
4. **使用示例** - 代码示例和cURL命令
5. **缓存策略** - 缓存键设计和TTL设置
6. **性能优化** - 性能考虑和最佳实践
7. **限制和注意事项** - 已知限制和使用建议
8. **未来规划** - 计划中的功能改进

## 🔗 相关文档

### API文档

- [API概览](../api/README.md)
- [请求规范](../api/request-spec.md)
- [错误码对照表](../api/error-codes.md)

### 指南文档

- [国际化指南](../development/i18n.md)
- [性能指南](../development/performance.md)
- [测试指南](../development/testing.md)

### 架构文档

- [系统设计](../architecture/system-design.md)
- [后端架构](../architecture/backend.md)
- [架构决策记录](../reference/adr/)

## 📝 贡献指南

### 新增模块文档

1. **创建文档文件**: `docs-site/docs/modules/{module-name}.md`
2. **遵循文档模板**: 参考现有模块文档结构
3. **更新索引**: 在本文件中添加模块条目
4. **添加交叉引用**: 在相关文档中添加链接

### 文档维护

- **及时更新**: API变更时同步更新文档
- **示例验证**: 确保代码示例可以正常运行
- **链接检查**: 定期检查文档间的链接有效性
- **版本标记**: 新功能标注版本号

## 🏷️ 版本历史

- **v0.4.3** - 新增搜索和Feed模块，完善多语言支持
- **v0.4.2** - 模块化重构，feature-first目录结构
- **v0.4.1** - OpenAPI规范整改，统一响应格式
- **v0.4.0** - 分层架构改造，Service层解耦

---

_最后更新: 2025-07-28_
