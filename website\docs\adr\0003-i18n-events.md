# 3. Events 国际化实现方案

日期: 2025-07-25

## 状态

已实施 (2025-07-27)

## 背景

目前 Events 相关的文本内容(名称、场馆等)只支持单一语言。随着用户群的扩大，需要支持多语言显示，使不同语言的用户都能获得良好的使用体验。

## 决策

我们决定通过以下方案实现 Events 的国际化:

1. 支持的语言范围
   - 英语 (en)：默认语言，美式英语
   - 日语 (ja)：日本市场
   - 中文 (zh)：简体中文
   - 注：项目长期内仅支持这三种语言，不考虑扩展其他语言
   - SEO 考虑：选择英语作为默认语言以提升全球可见性

2. 用户语言偏好存储
   - 在 `auth_user` 表中添加 `locale` 字段
   - 默认值为 'en'
   - 通过数据库约束限制只能使用支持的语言代码
   - `/auth/me` 接口返回此偏好设置

3. Events 多语言存储
   - 采用动态字段方案：每个需要国际化的字段创建对应的语言后缀版本
   - 必填字段：名称(name)、场馆名(venue_name)
   - 可选字段：地址(venue_address)、描述(description)
   - 每个字段都有 en/ja/zh 三个版本

4. 语言选择策略
   优先级从高到低：
   1. Cookie（key: 'locale'）中的语言设置
   2. 已登录用户：使用用户的 locale 设置并同步到 Cookie
   3. 未登录用户：使用请求头 Accept-Language 中的首选语言并设置 Cookie
   4. 默认值：'en'

5. API 设计原则
   - 普通用户接口：只返回当前语言版本的内容
   - 管理接口：返回所有语言版本
   - 在响应中标注当前使用的语言代码
   - 确保英文版本始终可用，作为 fallback

## 影响

### 积极影响

1. 提升国际用户的使用体验
2. 语言偏好跨设备同步
3. 减少传输数据量(按需返回单语言)
4. 查询性能好（无需 JOIN）
5. 实现简单，维护成本低
6. 类型安全，编译时即可发现问题
7. 更好的 SEO 表现（英语默认）
8. 降低国际用户首访跳出率

### 消极影响

1. 数据库表结构变更
2. 需要补充历史数据的多语言版本
3. API 响应结构变更
4. 语言切换时需要重新加载数据
5. 不易扩展新语言（需要改表）
6. 日本用户首次访问可能需要手动切换语言

## 风险

1. 数据完整性
   - 需要确保所有必填的语言版本都已填写
   - 通过数据库约束和应用层验证保证
   - 英文版本作为基准，必须最先完成

2. 性能影响
   - 数据库表行宽增加
   - 语言切换时的额外请求开销

3. 兼容性
   - 旧版本客户端可能未发送 `Accept-Language`
   - 需要合理的默认语言策略
   - 浏览器语言格式可能与系统支持的语言代码不完全匹配(如 zh-CN vs zh)

## 行动项目

1. 第一阶段（1周）：数据库改造
   - [x] 修改 events 表结构，添加多语言字段
   - [x] 添加 auth_user.locale 字段，默认值改为 'en'
   - [x] 将现有日语内容迁移到 `*_ja` 字段，同时补充英文翻译

2. 第二阶段（2周）：后端改造
   - [x] 实现 i18n 相关类型定义
   - [x] 实现 locale 中间件
   - [x] 修改 /auth/me 接口返回 locale
   - [x] 改造 events 相关接口支持国际化
   - [x] 实现管理接口的多语言验证
   - [x] 补充中文翻译数据

3. 第三阶段（1周）：测试与文档
   - [x] 实现用户语言切换功能
   - [x] 添加多语言相关测试用例
   - [x] 更新 OpenAPI 文档
   - [x] 编写国际化指南文档
   - [x] 验证搜索引擎爬虫获取到英文版本

4. 第四阶段（2周）：前端改造
   - [x] 实现语言检测中间件
   - [x] 实现全局语言状态管理
   - [x] 实现语言切换组件和功能
   - [x] 配置 SWR 缓存策略
   - [x] 补充必要的单元测试

## 参考资料

- [HTTP 规范中的 Accept-Language](https://developer.mozilla.org/docs/Web/HTTP/Headers/Accept-Language)
- [Next.js 国际化](https://nextjs.org/docs/app/building-your-application/routing/internationalization)
- [Google 搜索引擎国际化指南](https://developers.google.com/search/docs/advanced/crawling/managing-multi-regional-sites)

## 实现指南

具体的实现细节（包括类型定义、中间件实现、数据验证等）将在单独的实现指南文档中详细说明：[国际化指南](/docs/guides/i18n)

## 前端实现方案

### Cookie 处理

- Cookie 名称：'locale'
- 有效期：365天
- 作用域：'/'
- SameSite：'lax'

### 中间件实现

- 位置：src/middleware.ts
- 职责：
  1. 读取 Cookie 中的语言设置
  2. 未设置时应用语言选择策略
  3. 设置响应头以供客户端使用

### 状态管理

- 使用 React Context 维护当前语言状态
- 提供全局的语言切换函数
- 语言切换时自动更新 Cookie

### 数据刷新策略

- 语言切换时触发 SWR 全局重新验证
- 配置 SWR 中间件处理语言相关的缓存键

### 优势

1. 简化的实现
   - 无需配置复杂的路由规则
   - 更容易处理动态内容
   - 便于分享链接

2. 更好的用户体验
   - URL 保持简洁
   - 语言切换不影响路由历史
   - 跨页面保持语言一致性

3. 更简单的部署
   - 无需特殊的路由配置
   - 更容易进行 CDN 缓存

### 注意事项

1. 状态同步
   - 确保用户设置与 Cookie 的同步
   - 处理登录/登出时的语言状态

2. 缓存策略
   - 需要在缓存键中包含语言信息
   - 考虑不同语言版本的缓存策略

3. 测试覆盖
   - 中间件的单元测试
   - Cookie 处理的集成测试
   - 语言切换流程的 E2E 测试

## 后端配合方案

### API 端点

1. 语言设置

```typescript
// PUT /api/users/me/locale
interface UpdateLocaleRequest {
  locale: 'en' | 'ja' | 'zh';
}

interface UpdateLocaleResponse {
  locale: string;
  updated_at: string;
}

// 响应头
Set-Cookie: locale=en; Path=/; Max-Age=31536000; SameSite=Lax
```

2. 用户信息

```typescript
// GET /api/auth/me
interface UserResponse {
  // ... 其他字段
  locale: string;
  locale_updated_at: string; // 新增
}
```

### 状态同步

1. Cookie 与用户设置同步
   - 登录时比较 Cookie 与用户设置的时间戳
   - 采用最新的设置并返回对应 Cookie
   - 记录语言变更历史用于审计

2. 响应头处理
   - Content-Language: 当前响应语言
   - Vary: Accept-Language
   - 便于 CDN 和客户端缓存

3. 缓存策略
   - 在缓存键中包含语言信息
   - 支持 CDN 多语言缓存
   - 配置合适的缓存过期时间

### 安全考虑

1. Cookie 保护
   - 验证 Cookie 值在允许范围内
   - 防止 Cookie 篡改
   - 限制语言切换频率

2. 用户设置保护
   - 记录语言变更历史
   - 异常行为检测
   - 限制未登录用户的语言切换

### 性能优化

1. 数据库查询
   - 用户表添加 locale_updated_at 字段
   - 合理使用索引
   - 批量预加载关联数据

2. 缓存策略
   - 合理设置 Cache-Control
   - 利用 CDN 多语言缓存
   - 实现高效的本地缓存

## 实施总结 (2025-07-27)

### 完成情况

多语言事件管理系统已成功实施并投入使用，所有计划的功能均已完成：

1. **技术实现**
   - ✅ 数据库结构完全重构，支持 name_zh/name_ja/name_en 等多语言字段
   - ✅ 前端使用 React Hook Form + Zod 实现类型安全的表单验证
   - ✅ 生成的 API hooks 确保前后端数据一致性
   - ✅ 全面的 TypeScript 类型定义

2. **质量保证**
   - ✅ 119个测试用例全部通过
   - ✅ ESLint 零警告，代码质量达标
   - ✅ 完整的单元测试和集成测试覆盖

3. **用户体验**
   - ✅ 支持中文/日文/英文三种语言
   - ✅ 语言切换功能正常工作
   - ✅ 表单验证支持多语言错误提示

### 经验教训

1. **架构决策验证**
   - 动态字段方案证明是正确的选择，查询性能良好
   - TypeScript 类型系统有效防止了多语言字段的使用错误
   - React Hook Form + Zod 的组合提供了优秀的开发体验

2. **开发流程**
   - 先完成数据结构设计再进行前端开发的顺序是明智的
   - 生成的 API hooks 大大减少了手动编写的样板代码
   - 完善的测试覆盖确保了重构的安全性

3. **技术选型**
   - Next.js + TypeScript 技术栈适合此类多语言应用
   - Zod 的运行时验证对多语言数据完整性至关重要
   - SWR 的缓存策略有效支持了语言切换场景

### 后续优化建议

1. **性能监控**
   - 监控多语言数据的查询性能
   - 观察语言切换的用户行为模式

2. **内容管理**
   - 考虑建立翻译工作流程
   - 监控各语言版本的内容完整性

3. **用户体验**
   - 收集用户对多语言功能的反馈
   - 优化语言检测和切换的用户体验
