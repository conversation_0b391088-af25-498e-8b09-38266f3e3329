"use client";

// mock sonner toast 必须在其它导入之前，确保正确替换
vi.mock("sonner", () => ({
  toast: {
    error: vi.fn(),
    success: vi.fn(),
  },
}));

import { useRouter } from "next/navigation";
import React from "react";
import type { Mock } from "vitest";
import { toast } from "sonner";

import AdminCirclesPage from "../page";

import { request } from "@/lib/http";
import { showApiError } from "@/lib/show-error";
import {
  renderWithProviders,
  screen,
  waitFor,
  fireEvent,
} from "@test/test-utils";

// Mock next/navigation
vi.mock("next/navigation", () => ({
  useRouter: vi.fn(),
}));

vi.mock("@/lib/http", () => ({
  request: vi.fn(),
}));

// 新增 mock show-error
vi.mock("@/lib/show-error", () => ({
  showApiError: vi.fn(),
}));

const mockPush = vi.fn();
const mockCircles = [
  { id: "1", name: "社团 1", author: "作者 1" },
  { id: "2", name: "社团 2", author: "作者 2" },
];

describe("AdminCirclesPage", () => {
  beforeEach(() => {
    vi.resetAllMocks();
    (useRouter as unknown as Mock).mockReturnValue({ push: mockPush });
  });

  test("should show loading state and then display circles", async () => {
    (request as Mock).mockResolvedValue(mockCircles);

    renderWithProviders(<AdminCirclesPage />);

    expect(screen.getByText("加载中...")).toBeInTheDocument();

    await waitFor(() => {
      expect(screen.getByText("社团 1")).toBeInTheDocument();
      expect(screen.getByText("社团 2")).toBeInTheDocument();
    });
  });

  test("should navigate to new circle page on button click", async () => {
    (request as Mock).mockResolvedValue([]);
    renderWithProviders(<AdminCirclesPage />);

    await waitFor(() => fireEvent.click(screen.getByText("新增社团")));

    expect(mockPush).toHaveBeenCalledWith("/admin/circles/new");
  });

  test("should call delete handler and refetch circles", async () => {
    (request as Mock).mockResolvedValue(mockCircles);
    renderWithProviders(<AdminCirclesPage />);

    // 等待首次列表渲染
    await waitFor(() => expect(screen.getByText("社团 1")).toBeInTheDocument());

    // 模拟 confirm 与多次请求: 第一次 DELETE, 第二次为 refetch
    window.confirm = vi.fn(() => true) as any;
    (request as Mock)
      .mockResolvedValueOnce({}) // DELETE 成功
      .mockResolvedValueOnce([mockCircles[1]]); // 重新获取列表

    const deleteBtn = screen.getAllByText("删除")[0];
    fireEvent.click(deleteBtn);

    expect(window.confirm).toHaveBeenCalledWith("确定删除该社团吗？");

    await waitFor(() => {
      expect(request).toHaveBeenCalledWith("/admin/circles/1", { method: "DELETE" });
    });

    // 等待重新获取列表完成
    await waitFor(() => {
      expect(request).toHaveBeenCalledWith("/admin/circles");
    });

    // 验证 UI 更新
    await waitFor(() => {
      expect(screen.queryByText("社团 1")).not.toBeInTheDocument();
      expect(screen.getByText("社团 2")).toBeInTheDocument();
    });
  });

  test("should show toast on delete failure", async () => {
    (request as Mock).mockResolvedValue(mockCircles);
    renderWithProviders(<AdminCirclesPage />);

    await waitFor(() => expect(screen.getByText("社团 1")).toBeInTheDocument());

    // 模拟删除失败
    window.confirm = vi.fn(() => true) as any;
    const error = { code: 500, message: "删除失败" };
    (request as Mock).mockRejectedValueOnce(error);

    const deleteBtn = screen.getAllByText("删除")[0];
    fireEvent.click(deleteBtn);

    await waitFor(() => {
      expect(showApiError).toHaveBeenCalledWith(error, "删除失败");
    });
  });
}); 