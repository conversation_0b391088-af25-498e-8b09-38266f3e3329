import type { D1Database } from '@cloudflare/workers-types';

import { getVenueById } from '../venue/service';
import type { Event, EventUpdateInput } from './schema';
import { createEventRepository } from './repository';
import { <PERSON><PERSON>, Logger } from '@/infrastructure';
import { NewEventData } from '@/infrastructure/db/eventRepository';
import type { Locale } from '@/middlewares/locale';

// ---------------- Locale Helper ----------------

/**
 * 将多语言列裁剪为单语言公共视图。
 */
function pickLocaleRow(row: any, locale: Locale) {
  const suf = `_${locale}`;
  return {
    id: row.id,
    name: row[`name${suf}`],
    date: row[`date${suf}`],
    date_sort: row.date_sort,
    image_url: row.image_url,
    venue_name: row[`venue_name${suf}`],
    venue_address: row[`venue_address${suf}`],
    venue_lat: row.venue_lat,
    venue_lng: row.venue_lng,
    url: row.url,
    created_at: row.created_at,
    updated_at: row.updated_at,
  } as const;
}

export type PublicEvent = ReturnType<typeof pickLocaleRow>;
// ----------------------------------
// Types
// ----------------------------------

export type PaginatedEvents = {
  items: PublicEvent[];
  total: number;
  page: number;
  pageSize: number;
};

/**
 * 展会列表查询（支持分页 / 关键字 / 日期区间）。
 *
 * 说明：
 * 1. 为保持简洁，复杂过滤直接在 Service 内拼接 SQL；
 * 2. 结果按 `date_sort` 倒序；
 * 3. 使用 `Cache`（若注入）对完整 URL 查询结果做 5 分钟缓存；
 */
export async function listEvents(
  db: D1Database,
  searchParams: URLSearchParams,
  locale: Locale,
  cache?: Cache,
  logger?: Logger
): Promise<PaginatedEvents> {
  const page = Math.max(Number(searchParams.get('page') || '1'), 1);
  const pageSize = Math.max(Number(searchParams.get('pageSize') || '50'), 1);
  const offset = (page - 1) * pageSize;

  const keyword = searchParams.get('keyword')?.trim();
  const dateFrom = searchParams.get('date_from')?.trim();
  const dateTo = searchParams.get('date_to')?.trim();

  // 构造缓存 key（避免参数顺序导致重复）
  const cacheKey = `events_${locale}_${[...searchParams.entries()]
    .sort()
    .map(([k, v]) => `${k}=${v}`)
    .join('&')}`;

  if (cache) {
    const cached = await cache.get<PaginatedEvents>(cacheKey);
    if (cached) {
      logger?.debug?.('listEvents: hit cache', { key: cacheKey });

      return cached;
    }
  }

  // 动态条件
  const conditions: string[] = [];
  const params: (string | number)[] = [];
  if (keyword) {
    conditions.push(`(
      e.name_en LIKE ? OR e.name_ja LIKE ? OR e.name_zh LIKE ? OR
      v.name_en LIKE ? OR v.name_ja LIKE ? OR v.name_zh LIKE ?
    )`);
    const kw = `%${keyword}%`;
    // push 6 times
    params.push(kw, kw, kw, kw, kw, kw);
  }
  if (dateFrom) {
    conditions.push('e.date_sort >= ?');
    params.push(Number(dateFrom));
  }
  if (dateTo) {
    conditions.push('e.date_sort <= ?');
    params.push(Number(dateTo));
  }
  const whereClause = conditions.length
    ? 'WHERE ' + conditions.join(' AND ')
    : '';

  // total
  const totalRes = await db
    .prepare(
      `
      SELECT COUNT(*) AS total
      FROM events e
      LEFT JOIN venues v ON e.venue_id = v.id
      ${whereClause}
    `
    )
    .bind(...params)
    .first<{ total: number }>();
  const total = totalRes?.total || 0;

  // data - 查询events并关联venue信息
  const stmt = db.prepare(`
    SELECT
      e.*,
      v.id as venue_id,
      v.name_en as venue_name_en,
      v.name_ja as venue_name_ja,
      v.name_zh as venue_name_zh,
      v.address_en as venue_address_en,
      v.address_ja as venue_address_ja,
      v.address_zh as venue_address_zh,
      v.lat as venue_lat,
      v.lng as venue_lng,
      v.capacity as venue_capacity,
      v.website_url as venue_website_url,
      v.phone as venue_phone,
      v.description_en as venue_description_en,
      v.description_ja as venue_description_ja,
      v.description_zh as venue_description_zh,
      v.facilities as venue_facilities,
      v.transportation as venue_transportation,
      v.parking_info as venue_parking_info
    FROM events e
    LEFT JOIN venues v ON e.venue_id = v.id
    ${whereClause}
    ORDER BY e.date_sort DESC
    LIMIT ? OFFSET ?
  `);
  const bound = stmt.bind(...params, pageSize, offset);
  type Allable = { all: () => Promise<{ results: unknown[] }> };
  const allFn: () => Promise<{ results: unknown[] }> =
    typeof (bound as unknown as Allable).all === 'function'
      ? (bound as unknown as Allable).all.bind(bound)
      : stmt.all.bind(stmt);
  const { results: raw } = await allFn();

  // 处理包含venue信息的结果
  const items = (raw as any[]).map((row) => {
    const event = pickLocaleRow(
      {
        id: row.id,
        name_en: row.name_en,
        name_ja: row.name_ja,
        name_zh: row.name_zh,
        date_en: row.date_en,
        date_ja: row.date_ja,
        date_zh: row.date_zh,
        date_sort: row.date_sort,
        image_url: row.image_url,
        url: row.url,
        created_at: row.created_at,
        updated_at: row.updated_at,
        venue_id: row.venue_id,
        venue_name_en: row.venue_name_en,
        venue_name_ja: row.venue_name_ja,
        venue_name_zh: row.venue_name_zh,
        venue_address_en: row.venue_address_en,
        venue_address_ja: row.venue_address_ja,
        venue_address_zh: row.venue_address_zh,
        venue_lat: row.venue_lat,
        venue_lng: row.venue_lng,
      },
      locale
    );

    // 添加venue信息（如果存在）
    if (row.venue_id) {
      event.venue = {
        id: row.venue_id,
        name: row[`venue_name_${locale}`] || row.venue_name_en,
        address: row[`venue_address_${locale}`] || row.venue_address_en,
        lat: row.venue_lat,
        lng: row.venue_lng,
        capacity: row.venue_capacity,
        website_url: row.venue_website_url,
        phone: row.venue_phone,
        description:
          row[`venue_description_${locale}`] || row.venue_description_en,
        facilities: row.venue_facilities,
        transportation: row.venue_transportation,
        parking_info: row.venue_parking_info,
      };
    }

    return event;
  });

  const result: PaginatedEvents = { items, total, page, pageSize };

  if (cache) {
    await cache.set(cacheKey, result, 300);
  }
  return result;
}

/** 获取展会详情 */
export async function getEvent(
  db: D1Database,
  id: string,
  locale: Locale
): Promise<PublicEvent | null> {
  // 查询event基本信息
  const row = await db
    .prepare('SELECT * FROM events WHERE id = ?')
    .bind(id)
    .first();

  if (!row) return null;

  const event = pickLocaleRow(
    {
      id: row.id,
      name_en: row.name_en,
      name_ja: row.name_ja,
      name_zh: row.name_zh,
      date_en: row.date_en,
      date_ja: row.date_ja,
      date_zh: row.date_zh,
      date_sort: row.date_sort,
      image_url: row.image_url,
      url: row.url,
      created_at: row.created_at,
      updated_at: row.updated_at,
      venue_id: row.venue_id,
    },
    locale
  );

  // 通过venue service获取venue信息
  if (row.venue_id) {
    const venue = await getVenueById(db, row.venue_id, locale);
    if (venue) {
      event.venue = venue;
    }
  }

  return event;
}

/** 列出某展会参展社团 */
export async function listCirclesByEvent(
  db: D1Database,
  eventId: string
): Promise<unknown[]> {
  // 需求：在返回的社团信息中带上 booth_id（如存在复数 appearance，取首个）。
  // 说明：SQLite 允许在 GROUP BY 之外选择未聚合列，此处使用 MIN(a.booth_id)
  //       既能去重，又能稳定返回一个 booth_id。
  const { results } = await db
    .prepare(
      `SELECT c.*, MIN(a.booth_id) AS booth_id
       FROM circles c
       JOIN appearances a ON a.circle_id = c.id
       WHERE a.event_id = ?
       GROUP BY c.id
       ORDER BY c.name ASC`
    )
    .bind(eventId)
    .all();
  return results;
}

/** 参展记录分页查询（支持关键字 & 分类筛选） */
export async function listAppearances(
  db: D1Database,
  eventId: string,
  searchParams: URLSearchParams
): Promise<{
  items: unknown[];
  total: number;
  page: number;
  pageSize: number;
}> {
  const page = Math.max(Number(searchParams.get('page') || '1'), 1);
  const pageSize = Math.max(Number(searchParams.get('pageSize') || '50'), 1);
  const offset = (page - 1) * pageSize;

  const keyword = searchParams.get('keyword')?.trim();
  const categories: string[] = [
    ...searchParams.getAll('category'),
    ...(searchParams.get('category') || '').split(',').filter(Boolean),
  ];
  const uniqueCategories = Array.from(new Set(categories));

  const baseSelect = `
    FROM appearances a
    JOIN circles   c  ON a.circle_id = c.id
    LEFT JOIN artists ar ON a.artist_id = ar.id
  `;

  const conditions: string[] = ['a.event_id = ?'];
  const params: (string | number)[] = [eventId];
  if (uniqueCategories.length) {
    const placeholders = uniqueCategories.map(() => '?').join(', ');
    conditions.push(`c.category IN (${placeholders})`);
    params.push(...uniqueCategories);
  }
  if (keyword) {
    conditions.push(`(
      a.booth_id LIKE ? OR
      c.name LIKE ? OR
      ar.name LIKE ?
    )`);
    const kwLike = `%${keyword}%`;
    params.push(kwLike, kwLike, kwLike);
  }
  const whereClause = conditions.join(' AND ');

  // total
  const totalRes = await db
    .prepare(`SELECT COUNT(*) AS total ${baseSelect} WHERE ${whereClause}`)
    .bind(...params)
    .first<{ total: number }>();
  const total = totalRes?.total || 0;

  // data
  const { results: items } = await db
    .prepare(
      `SELECT 
        a.circle_id,
        c.name  AS circle_name,
        c.urls  AS circle_urls,
        c.category AS category,
        a.artist_id,
        ar.name AS artist_name,
        a.booth_id
        ${baseSelect}
      WHERE ${whereClause}
      ORDER BY a.booth_id ASC
      LIMIT ? OFFSET ?`
    )
    .bind(...params, pageSize, offset)
    .all();

  return { items, total, page, pageSize };
}

/** 创建展会 */
export async function createEvent(
  db: D1Database,
  data: NewEventData
): Promise<Event> {
  const repo = createEventRepository(db);
  return repo.create(data);
}

/** 更新展会 */
export async function updateEvent(
  db: D1Database,
  id: string,
  partial: EventUpdateInput & { date_sort?: number }
): Promise<void> {
  const repo = createEventRepository(db);
  await repo.update(id, partial);
}

/** 删除展会 */
export async function deleteEvent(db: D1Database, id: string): Promise<void> {
  const repo = createEventRepository(db);
  await repo.delete(id);
}

// ---- Admin: full-field access ----

export async function listEventsAdmin(
  db: D1Database,
  searchParams: URLSearchParams,
  cache?: Cache,
  logger?: Logger
): Promise<PaginatedEvents> {
  const page = Math.max(Number(searchParams.get('page') || '1'), 1);
  const pageSize = Math.max(Number(searchParams.get('pageSize') || '50'), 1);
  const offset = (page - 1) * pageSize;

  const keyword = searchParams.get('keyword')?.trim();
  const dateFrom = searchParams.get('date_from')?.trim();
  const dateTo = searchParams.get('date_to')?.trim();

  const cacheKey = `events_admin_${[...searchParams.entries()]
    .sort()
    .map(([k, v]) => `${k}=${v}`)
    .join('&')}`;

  if (cache) {
    const cached = await cache.get<PaginatedEvents>(cacheKey);
    if (cached) {
      logger?.debug?.('adminListEvents: hit cache', { key: cacheKey });
      return cached;
    }
  }

  const conditions: string[] = [];
  const params: (string | number)[] = [];
  if (keyword) {
    conditions.push(`(
      e.name_en LIKE ? OR e.name_ja LIKE ? OR e.name_zh LIKE ? OR
      v.name_en LIKE ? OR v.name_ja LIKE ? OR v.name_zh LIKE ?
    )`);
    const kw = `%${keyword}%`;
    params.push(kw, kw, kw, kw, kw, kw);
  }
  if (dateFrom) {
    conditions.push('e.date_sort >= ?');
    params.push(Number(dateFrom));
  }
  if (dateTo) {
    conditions.push('e.date_sort <= ?');
    params.push(Number(dateTo));
  }
  const whereClause = conditions.length
    ? 'WHERE ' + conditions.join(' AND ')
    : '';

  const totalRes = await db
    .prepare(
      `
      SELECT COUNT(*) AS total
      FROM events e
      LEFT JOIN venues v ON e.venue_id = v.id
      ${whereClause}
    `
    )
    .bind(...params)
    .first<{ total: number }>();
  const total = totalRes?.total || 0;

  const stmt = db.prepare(`
    SELECT
      e.*,
      v.id as venue_id,
      v.name_en as venue_name_en,
      v.name_ja as venue_name_ja,
      v.name_zh as venue_name_zh,
      v.address_en as venue_address_en,
      v.address_ja as venue_address_ja,
      v.address_zh as venue_address_zh,
      v.lat as venue_lat,
      v.lng as venue_lng
    FROM events e
    LEFT JOIN venues v ON e.venue_id = v.id
    ${whereClause}
    ORDER BY e.date_sort DESC
    LIMIT ? OFFSET ?
  `);
  const { results: raw } = await stmt.bind(...params, pageSize, offset).all();
  const items = raw as any[];
  const result: PaginatedEvents = {
    items: items as any,
    total,
    page,
    pageSize,
  };

  if (cache) {
    await cache.set(cacheKey, result, 300);
  }
  return result;
}

export async function getEventAdmin(
  db: D1Database,
  id: string
): Promise<Event | null> {
  const repo = createEventRepository(db);
  return repo.findById(id);
}
