import { http as mswHttp, HttpResponse } from "msw";
import { describe, test, expect, vi, beforeEach } from "vitest";
import { z } from "zod";

import {
  fetchGetEvents,
  fetchGetEventsId,
  fetchGetEventsIdAppearances,
  fetchPostCirclesCircleIdBookmark,
} from "@/api/generated/ayafeedComponents";
import { API_BASE } from "@/lib/http";
import { EventSchema } from "@/schemas";
import { server } from "@test/testServer";


// Mock data
const eventsMock = [
  { id: "e1", name: "Reitaisa<PERSON>", startDate: "2025-03-05" },
];
const eventDetailMock = {
  id: "e1",
  name: "<PERSON>ita<PERSON><PERSON>",
  date: "2025-03-05 10:00",
  date_sort: 20250305,
  url: "https://example.com",
  description: "Comic event",
};
const circlesMock = {
  items: [
    { id: "c1", name: "Circle 1", booth: "A01" },
    { id: "c2", name: "Circle 2", booth: "B02" },
  ],
  total: 2,
};

describe("events & circles api", () => {
  beforeEach(() => {
    vi.resetAllMocks();
  });

  test("getEvents returns list", async () => {
    server.use(
      mswHttp.get(`${API_BASE}/events`, () => {
        return HttpResponse.json(eventsMock);
      })
    );

    const data = await fetchGetEvents({});
    expect(data).toEqual(eventsMock);
  });

  test("getEventDetail validates via schema", async () => {
    // schema 在 getEventDetail 内部已包含 id/name 等校验
    server.use(
      mswHttp.get(`${API_BASE}/events/e1`, () => {
        return HttpResponse.json(eventDetailMock);
      })
    );
    const res = await fetchGetEventsId({ pathParams: { id: "e1" } });
    const data = EventSchema.parse(res);
    expect(data).toEqual(eventDetailMock);
  });

  test("getEventDetail throws on invalid schema", async () => {
    const invalid = { ...eventDetailMock, date: 123 as any }; // date 应为 string
    server.use(
      mswHttp.get(`${API_BASE}/events/e1`, () => {
        return HttpResponse.json(invalid);
      })
    );
    await expect(
      fetchGetEventsId({ pathParams: { id: "e1" } }).then((d) =>
        EventSchema.parse(d)
      )
    ).rejects.toBeInstanceOf(z.ZodError);
  });

  test("getCirclesOfEvent cleans empty params", async () => {
    let capturedUrl = "";
    server.use(
      mswHttp.get(`${API_BASE}/events/e1/appearances`, ({ request }) => {
        capturedUrl = request.url;
        return HttpResponse.json({ items: circlesMock.items, total: 2 });
      })
    );

    // 模拟原先包装层的参数清理逻辑
    const cleanedParams: Record<string, string> = {
      page: "1",
      pageSize: "20",
      // keyword 与 category 均为空，不应进入 queryParams
    };

    await fetchGetEventsIdAppearances({
      pathParams: { id: "e1" },
      queryParams: cleanedParams,
    });

    // 关键：keyword 为空字符串、category 为空数组均不应出现在查询串，且必须携带 event_id
    const url = new URL(capturedUrl)
    expect(url.pathname).toBe('/events/e1/appearances')
    expect(url.searchParams.get('page')).toBe('1')
    expect(url.searchParams.get('pageSize')).toBe('20')
    expect(url.searchParams.get('keyword')).toBe(null)
    expect(url.searchParams.get('category')).toBe(null)
  });

  test("bookmark API triggers POST", async () => {
    let method = "";
    server.use(
      mswHttp.post(`${API_BASE}/circles/c1/bookmark`, ({ request }) => {
        method = request.method;
        return HttpResponse.json({ message: "OK", data: { isBookmarked: true } });
      })
    );

    const res = await fetchPostCirclesCircleIdBookmark({ pathParams: { circleId: "c1" } });
    expect(res.data?.isBookmarked).toBe(true);
    expect(method).toBe("POST");
  });
}); 