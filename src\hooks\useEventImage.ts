/**
 * 事件图片相关的 Hooks
 */

import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { toast } from 'sonner';

import { getImages, uploadImage, deleteImages } from '@/services/imageService';
import type {
  ImageInfo,
  ImageUploadRequest,
  ImageListParams,
  ImageDeleteRequest,
  ImageVariant
} from '@/types/image';

// 批量图片查询结果
export interface BatchImageResult {
  [eventId: string]: ImageInfo | null;
}

/**
 * 获取单个事件的图片
 */
export function useEventImage(eventId: string, variant?: ImageVariant) {
  return useQuery({
    queryKey: ['event-images', eventId, variant],
    queryFn: async () => {
      const params: ImageListParams = {
        category: 'event',
        resourceId: eventId,
        imageType: 'poster',
        ...(variant && { variant })
      };

      const result = await getImages(params);

      // 如果指定了变体，返回第一个匹配的图片
      if (variant && result.images.length > 0) {
        return result.images.find(img => img.variant === variant) || null;
      }

      // 否则返回所有图片
      return result.images;
    },
    enabled: !!eventId,
    staleTime: 5 * 60 * 1000, // 5分钟缓存
    retry: 2,
  });
}

/**
 * 批量获取多个事件的图片
 */
export function useEventImagesBatch(eventIds: string[], variant: ImageVariant = 'medium') {
  return useQuery({
    queryKey: ['event-images-batch', eventIds, variant],
    queryFn: async (): Promise<BatchImageResult> => {
      // 由于后端暂时没有批量查询接口，我们使用并发查询
      const promises = eventIds.map(async (eventId) => {
        try {
          const params: ImageListParams = {
            category: 'event',
            resourceId: eventId,
            imageType: 'poster',
            variant
          };
          
          const result = await getImages(params);
          const image = result.images.find(img => img.variant === variant);
          return { eventId, image: image || null };
        } catch (error) {
          console.warn(`Failed to fetch image for event ${eventId}:`, error);
          return { eventId, image: null };
        }
      });

      const results = await Promise.all(promises);
      
      // 转换为对象格式
      const batchResult: BatchImageResult = {};
      results.forEach(({ eventId, image }) => {
        batchResult[eventId] = image;
      });
      
      return batchResult;
    },
    enabled: eventIds.length > 0,
    staleTime: 5 * 60 * 1000, // 5分钟缓存
    retry: 1, // 批量查询失败时减少重试次数
  });
}

/**
 * 事件图片上传 Hook
 */
export function useEventImageUpload(options?: {
  onSuccess?: (images: any[]) => void;
  onError?: (error: Error) => void;
  showToast?: boolean;
}) {
  const queryClient = useQueryClient();
  const { onSuccess, onError, showToast = true } = options || {};

  return useMutation({
    mutationFn: async (uploadData: ImageUploadRequest) => {
      return await uploadImage(uploadData);
    },
    onSuccess: (data, variables) => {
      // 刷新相关查询
      queryClient.invalidateQueries({
        queryKey: ['event-images', variables.resourceId]
      });
      
      if (showToast) {
        toast.success('图片上传成功');
      }
      
      onSuccess?.([data]);
    },
    onError: (error) => {
      console.error('Image upload failed:', error);
      
      if (showToast) {
        toast.error(error.message || '图片上传失败');
      }
      
      onError?.(error as Error);
    },
  });
}

/**
 * 批量事件图片上传 Hook
 */
export function useBatchEventImageUpload(options?: {
  onProgress?: (progress: { completed: number; total: number }) => void;
  onSuccess?: (images: any[]) => void;
  onError?: (error: Error) => void;
  showToast?: boolean;
}) {
  const queryClient = useQueryClient();
  const { onProgress, onSuccess, onError, showToast = true } = options || {};

  return useMutation({
    mutationFn: async (uploads: ImageUploadRequest[]) => {
      const results = [];
      
      for (let i = 0; i < uploads.length; i++) {
        try {
          const result = await uploadImage(uploads[i]);
          results.push(result);
          
          onProgress?.({
            completed: i + 1,
            total: uploads.length
          });
        } catch (error) {
          console.error(`Upload failed for item ${i}:`, error);
          throw error;
        }
      }
      
      return results;
    },
    onSuccess: (data, variables) => {
      // 刷新相关查询
      const eventIds = [...new Set(variables.map(v => v.resourceId))];
      eventIds.forEach(eventId => {
        queryClient.invalidateQueries({
          queryKey: ['event-images', eventId]
        });
      });
      
      if (showToast) {
        toast.success(`成功上传 ${data.length} 张图片`);
      }
      
      onSuccess?.(data);
    },
    onError: (error) => {
      console.error('Batch upload failed:', error);
      
      if (showToast) {
        toast.error(error.message || '批量上传失败');
      }
      
      onError?.(error as Error);
    },
  });
}

/**
 * 事件图片删除 Hook
 */
export function useEventImageDelete(eventId: string, options?: {
  onSuccess?: () => void;
  onError?: (error: Error) => void;
  showToast?: boolean;
}) {
  const queryClient = useQueryClient();
  const { onSuccess, onError, showToast = true } = options || {};

  return useMutation({
    mutationFn: async (relativePaths: string[]) => {
      return await deleteImages(relativePaths);
    },
    onSuccess: (data) => {
      // 刷新相关查询
      queryClient.invalidateQueries({
        queryKey: ['event-images', eventId]
      });
      
      if (showToast) {
        toast.success(`成功删除 ${data?.deletedCount || 0} 张图片`);
      }
      
      onSuccess?.();
    },
    onError: (error) => {
      console.error('Image delete failed:', error);
      
      if (showToast) {
        toast.error(error.message || '图片删除失败');
      }
      
      onError?.(error as Error);
    },
  });
}

/**
 * 获取事件图片 URL 的便捷 Hook
 */
export function useEventImageUrl(eventId: string, variant: ImageVariant = 'medium') {
  const { data: image, isLoading, error } = useEventImage(eventId, variant);
  
  // 如果是单个图片对象
  if (image && typeof image === 'object' && 'relativePath' in image) {
    return {
      imageUrl: image.relativePath,
      isLoading,
      error
    };
  }
  
  // 如果是图片数组，取第一个
  if (Array.isArray(image) && image.length > 0) {
    const targetImage = image.find(img => img.variant === variant) || image[0];
    return {
      imageUrl: targetImage.relativePath,
      isLoading,
      error
    };
  }
  
  return {
    imageUrl: null,
    isLoading,
    error
  };
}

/**
 * 检查事件是否有图片的 Hook
 */
export function useEventHasImage(eventId: string) {
  const { data: images, isLoading } = useEventImage(eventId);
  
  const hasImage = Array.isArray(images) ? images.length > 0 : !!images;
  
  return {
    hasImage,
    isLoading
  };
}
