import { OpenAPIHono, createRoute, z } from '@hono/zod-openapi';

import { toggleCircleBookmark } from './controller';
import { roleGuard } from '@/middlewares/roleGuard';
import { successResponse, errorResponse } from '@/utils/schemas';
import { HonoApp } from '@/types';
import { registerOpenApiRoute } from '@/utils/openapiHelper';

const bookmarkRoutes = new OpenAPIHono<HonoApp>();

// ---------- OpenAPI ----------
const toggleBookmarkRoute = createRoute({
  method: 'post',
  path: '/{circleId}/bookmark',
  summary: '切换收藏状态',
  tags: ['Bookmarks'],
  request: {
    params: z.object({
      circleId: z.string().openapi({ example: 'circle-uuid' }),
    }),
  },
  responses: {
    200: {
      description: '收藏状态已切换',
      content: {
        'application/json': {
          schema: successResponse.extend({
            data: z.object({ isBookmarked: z.boolean() }),
          }),
        },
      },
    },
    401: {
      description: '未登录',
      content: { 'application/json': { schema: errorResponse } },
    },
  },
});

bookmarkRoutes.use('/*', roleGuard());
registerOpenApiRoute(bookmarkRoutes, toggleBookmarkRoute, toggleCircleBookmark);

export { bookmarkRoutes as routes };
