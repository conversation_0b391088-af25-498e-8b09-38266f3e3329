'use client'

import { useState } from 'react'
import dynamic from 'next/dynamic'

// 动态导入地图组件，避免 SSR 问题
const VenueLocationMap = dynamic(() => import('@/components/VenueLocationMap'), {
  ssr: false,
  loading: () => <div className="h-72 bg-gray-200 animate-pulse rounded-lg flex items-center justify-center">加载地图中...</div>
})

// 测试用的场馆数据
const testVenues = [
  {
    id: "tokyo-big-sight",
    name: "東京ビッグサイト 西ホール",
    address: "東京都江東区有明3-11-1",
    lat: 35.6298,
    lng: 139.7976
  },
  {
    id: "makuhari-messe",
    name: "幕張メッセ",
    address: "千葉県千葉市美浜区中瀬2-1",
    lat: 35.6472,
    lng: 140.0342
  },
  {
    id: "pacifico-yokohama",
    name: "パシフィコ横浜",
    address: "神奈川県横浜市西区みなとみらい1-1-1",
    lat: 35.4564,
    lng: 139.6317
  }
]

export default function MapTestPage() {
  const [selectedVenue, setSelectedVenue] = useState(testVenues[0])
  const [mapHeight, setMapHeight] = useState('h-72')

  return (
    <div className="container mx-auto p-6 space-y-6">
      <div className="bg-yellow-100 border border-yellow-400 rounded-lg p-4">
        <h1 className="text-2xl font-bold mb-2">地图组件测试页面</h1>
        <p className="text-sm text-gray-600">
          此页面用于测试地图组件的 z-index 问题。请滚动页面并观察地图是否会覆盖导航栏。
        </p>
      </div>

      {/* 控制面板 */}
      <div className="bg-white border rounded-lg p-4 space-y-4">
        <h2 className="text-lg font-semibold">控制面板</h2>
        
        <div className="space-y-2">
          <label className="block text-sm font-medium">选择场馆：</label>
          <select 
            value={selectedVenue.id}
            onChange={(e) => {
              const venue = testVenues.find(v => v.id === e.target.value)
              if (venue) setSelectedVenue(venue)
            }}
            className="border border-gray-300 rounded px-3 py-2"
          >
            {testVenues.map(venue => (
              <option key={venue.id} value={venue.id}>
                {venue.name}
              </option>
            ))}
          </select>
        </div>

        <div className="space-y-2">
          <label className="block text-sm font-medium">地图高度：</label>
          <select 
            value={mapHeight}
            onChange={(e) => setMapHeight(e.target.value)}
            className="border border-gray-300 rounded px-3 py-2"
          >
            <option value="h-48">小 (192px)</option>
            <option value="h-72">中 (288px)</option>
            <option value="h-96">大 (384px)</option>
            <option value="h-screen">全屏</option>
          </select>
        </div>
      </div>

      {/* 地图容器 */}
      <div className="bg-white border rounded-lg p-4">
        <h2 className="text-lg font-semibold mb-4">
          {selectedVenue.name}
        </h2>
        <p className="text-sm text-gray-600 mb-4">
          地址: {selectedVenue.address}
        </p>
        
        <div className={`${mapHeight} w-full border rounded-lg overflow-hidden`}>
          <VenueLocationMap venue={selectedVenue} />
        </div>
      </div>

      {/* 测试内容 - 用于测试滚动 */}
      <div className="space-y-4">
        <h2 className="text-lg font-semibold">测试滚动内容</h2>
        {Array.from({ length: 10 }, (_, i) => (
          <div key={i} className="bg-gray-50 border rounded-lg p-4">
            <h3 className="font-medium">测试内容块 {i + 1}</h3>
            <p className="text-gray-600 mt-2">
              这是一些测试内容，用于验证页面滚动时地图组件的行为。
              请向上滚动页面，观察地图是否会覆盖在导航栏上方。
              如果修复成功，地图应该始终保持在导航栏下方。
            </p>
          </div>
        ))}
      </div>

      {/* z-index 信息 */}
      <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
        <h2 className="text-lg font-semibold mb-2">Z-Index 层级信息</h2>
        <ul className="text-sm space-y-1">
          <li>• 导航栏: z-50 (最高优先级)</li>
          <li>• 地图容器: z-0 (基础层级)</li>
          <li>• Leaflet 地图元素: z-1 到 z-30 (受限制)</li>
          <li>• 地图控件: z-10</li>
          <li>• 地图弹窗: z-20</li>
          <li>• 地图标记: z-30</li>
        </ul>
      </div>
    </div>
  )
}
