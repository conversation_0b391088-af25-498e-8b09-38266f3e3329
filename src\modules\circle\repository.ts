import type { D1Database } from '@cloudflare/workers-types';

import type { Circle, CircleCreateInput, CircleUpdateInput } from './schema';
import { D1CircleRepository } from '@/infrastructure/db/circleRepository';

/**
 * CircleRepository 定义了社团相关的持久化操作接口
 */
export interface CircleRepository {
  /** 列出所有社团，按名称升序 */
  list(): Promise<Circle[]>;
  /** 根据 ID 获取社团，若不存在返回 null */
  findById(id: string): Promise<Circle | null>;
  /** 创建社团，返回创建后的完整记录 */
  create(input: CircleCreateInput): Promise<Circle>;
  /** 更新社团，返回更新后记录（可能为 null） */
  update(id: string, input: CircleUpdateInput): Promise<Circle | null>;
  /** 根据 ID 删除社团 */
  delete(id: string): Promise<void>;
}

/**
 * 工厂：根据运行时环境创建仓库实现
 */
export function createCircleRepository(db: D1Database): CircleRepository {
  // 目前仅支持 D1
  return new D1CircleRepository(db);
}
