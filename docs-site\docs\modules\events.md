# 事件模块 (Events Module)

## 概述

事件模块提供展会、活动等事件的管理和查询功能，支持多语言显示和国际化。

## 功能特性

- ✅ 多语言事件信息 (en, zh, ja)
- ✅ 事件列表查询和分页
- ✅ 单个事件详情获取
- ✅ 事件管理 (CRUD操作)
- ✅ 日期排序和过滤
- ✅ 多语言缓存支持

## API端点

### 公开接口

#### GET /events

**功能**: 获取事件列表

**查询参数**:

- `page` (可选) - 页码，默认 `1`
- `pageSize` (可选) - 每页数量，默认 `50`
- `fields` (可选) - 返回字段过滤

**请求头**:

- `X-Locale` (推荐) - 指定显示语言
- `Accept-Language` - 标准语言头部

**响应格式**:

```json
{
  "items": [
    {
      "id": "550e8400-e29b-41d4-a716-446655440000",
      "name": "Comiket 103",
      "venue_name": "东京国际展示场",
      "date": "2024-12-30T10:00:00Z",
      "image_url": "https://example.com/comiket103.jpg",
      "created_at": "2024-01-01T00:00:00Z",
      "updated_at": "2024-01-15T10:30:00Z"
    }
  ],
  "total": 150,
  "page": 1,
  "pageSize": 50
}
```

#### GET /events/\{id\}

**功能**: 获取单个事件详情

**路径参数**:

- `id` (必需) - 事件ID

**响应格式**:

```json
{
  "id": "550e8400-e29b-41d4-a716-446655440000",
  "name": "Comiket 103",
  "venue_name": "东京国际展示场",
  "date": "2024-12-30T10:00:00Z",
  "image_url": "https://example.com/comiket103.jpg",
  "created_at": "2024-01-01T00:00:00Z",
  "updated_at": "2024-01-15T10:30:00Z"
}
```

### 管理接口 (需要认证)

#### POST /admin/events

**功能**: 创建新事件

**请求体**:

```json
{
  "name_en": "Comiket 103",
  "name_zh": "Comic Market 103",
  "name_ja": "コミックマーケット103",
  "venue_name_en": "Tokyo Big Sight",
  "venue_name_zh": "东京国际展示场",
  "venue_name_ja": "東京ビッグサイト",
  "date_en": "2024-12-30T10:00:00Z",
  "date_zh": "2024-12-30T10:00:00Z",
  "date_ja": "2024-12-30T10:00:00Z",
  "image_url": "https://example.com/comiket103.jpg"
}
```

#### PUT /admin/events/\{id\}

**功能**: 更新事件信息

#### DELETE /admin/events/\{id\}

**功能**: 删除事件

## 数据结构

### Event Schema

| 字段          | 类型   | 说明         | 多语言支持    |
| ------------- | ------ | ------------ | ------------- |
| id            | string | 事件唯一标识 | -             |
| name\_\*      | string | 事件名称     | ✅ (en/zh/ja) |
| venue*name*\* | string | 场馆名称     | ✅ (en/zh/ja) |
| date\_\*      | string | 事件日期     | ✅ (en/zh/ja) |
| date_sort     | string | 排序用日期   | -             |
| image_url     | string | 事件图片URL  | -             |
| created_at    | string | 创建时间     | -             |
| updated_at    | string | 更新时间     | -             |

### 多语言字段映射

根据请求的locale，API会自动选择对应的语言字段：

```typescript
// locale = 'zh' 时
{
  "name": event.name_zh,
  "venue_name": event.venue_name_zh,
  "date": event.date_zh
}

// locale = 'ja' 时
{
  "name": event.name_ja,
  "venue_name": event.venue_name_ja,
  "date": event.date_ja
}
```

## 缓存策略

### 缓存键格式

```
events:{locale}:all
events:{locale}:detail:{id}
```

### 示例

```
events:zh:all
events:en:all
events:ja:detail:550e8400-e29b-41d4-a716-446655440000
```

### 缓存时间

- 列表缓存: 5分钟
- 详情缓存: 10分钟
- 自动失效: 事件数据更新时

## 使用示例

### JavaScript/TypeScript

```typescript
// 获取事件列表
const response = await fetch('/api/events?page=1&pageSize=20', {
  headers: { 'X-Locale': 'zh' },
});
const data = await response.json();

// 获取事件详情
const eventResponse = await fetch(
  '/api/events/550e8400-e29b-41d4-a716-446655440000',
  {
    headers: { 'X-Locale': 'ja' },
  }
);
const event = await eventResponse.json();

// 使用openapi-typescript-fetch
import { createClient } from '@/lib/api/client';
const api = createClient();

// 获取事件列表
const { data: events } = await api.GET('/events', {
  params: {
    query: { page: '1', pageSize: '20' },
  },
  headers: { 'X-Locale': 'zh' },
});

// 获取事件详情
const { data: event } = await api.GET('/events/\{id\}', {
  params: {
    path: { id: '550e8400-e29b-41d4-a716-446655440000' },
  },
  headers: { 'X-Locale': 'ja' },
});
```

### cURL

```bash
# 获取事件列表
curl -H "X-Locale: zh" \
  "https://api.example.com/events?page=1&pageSize=20"

# 获取事件详情
curl -H "Accept-Language: ja-JP,ja;q=0.9" \
  "https://api.example.com/events/550e8400-e29b-41d4-a716-446655440000"

# 创建事件 (需要认证)
curl -X POST \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -H "X-Locale: zh" \
  -d '{
    "name_en": "New Event",
    "name_zh": "新活动",
    "name_ja": "新しいイベント",
    "venue_name_en": "Convention Center",
    "venue_name_zh": "会展中心",
    "venue_name_ja": "コンベンションセンター",
    "date_en": "2024-12-31T10:00:00Z",
    "date_zh": "2024-12-31T10:00:00Z",
    "date_ja": "2024-12-31T10:00:00Z"
  }' \
  "https://api.example.com/admin/events"
```

## 排序和过滤

### 默认排序

- 按 `date_sort` 字段降序排列
- 相同日期按创建时间降序

### 字段过滤

使用 `fields` 参数可以只返回需要的字段：

```bash
# 只返回id、name和date字段
curl "https://api.example.com/events?fields=id,name,date"
```

## 性能优化

### 数据库优化

- `date_sort` 字段建立索引
- `created_at` 字段建立索引
- 分页查询使用LIMIT和OFFSET

### 缓存优化

- 语言隔离缓存，避免语言切换时的缓存失效
- 列表和详情分别缓存，提高命中率
- 数据更新时主动清理相关缓存

## 权限控制

### 公开接口

- 无需认证
- 支持所有HTTP方法的CORS
- 有基础的速率限制

### 管理接口

- 需要JWT认证
- 需要admin或editor角色
- 操作会记录审计日志

## 错误处理

### 常见错误

**事件不存在**:

```json
{
  "code": 10002,
  "message": "资源不存在",
  "data": {}
}
```

**参数验证错误**:

```json
{
  "code": 40002,
  "message": "缺少必填字段",
  "data": {
    "name_en": "必填字段"
  }
}
```

## 限制和注意事项

1. **多语言一致性**: 创建事件时建议提供所有语言版本的字段
2. **日期格式**: 所有日期字段必须使用ISO 8601格式
3. **图片URL**: 仅支持HTTPS协议的图片链接
4. **分页限制**: 单页最大返回100条记录

## 未来规划

- 🔄 支持事件分类和标签
- 🔄 添加事件搜索和过滤功能
- 🔄 支持事件关联的出展信息
- 🔄 实现事件状态管理(进行中、已结束等)
- 🔄 添加事件图片批量上传功能

## 相关文档

- [API规范](../api/request-spec.md)
- [i18n指南](../development/i18n.md)
- [社团模块](./circles.md)
- [搜索模块](./search.md)
