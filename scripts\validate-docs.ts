#!/usr/bin/env tsx

/**
 * 文档链接验证脚本
 * 检查所有 Markdown 文件中的内部链接是否有效
 */

import { readFileSync, existsSync } from 'fs';
import { join, dirname, resolve } from 'path';
import { glob } from 'fast-glob';

interface LinkIssue {
  file: string;
  line: number;
  link: string;
  issue: string;
}

const DOCS_ROOT = 'docs-site/docs';
const issues: LinkIssue[] = [];

/**
 * 解析 Markdown 文件中的链接
 */
function extractLinks(content: string): Array<{ link: string; line: number }> {
  const links: Array<{ link: string; line: number }> = [];
  const lines = content.split('\n');

  lines.forEach((line, index) => {
    // 匹配 [text](link) 格式的链接
    const linkRegex = /\[([^\]]+)\]\(([^)]+)\)/g;
    let match;

    while ((match = linkRegex.exec(line)) !== null) {
      const link = match[2];
      // 只检查相对链接，跳过外部链接和锚点
      if (
        !link.startsWith('http') &&
        !link.startsWith('#') &&
        !link.startsWith('mailto:')
      ) {
        links.push({
          link: link,
          line: index + 1,
        });
      }
    }
  });

  return links;
}

/**
 * 验证链接是否存在
 */
function validateLink(filePath: string, link: string): boolean {
  const fileDir = dirname(filePath);
  let targetPath: string;

  // 处理相对路径
  if (link.startsWith('./')) {
    targetPath = resolve(fileDir, link.substring(2));
  } else if (link.startsWith('../')) {
    targetPath = resolve(fileDir, link);
  } else {
    // 相对于 docs 根目录
    targetPath = resolve(DOCS_ROOT, link);
  }

  // 移除锚点
  const [pathOnly] = targetPath.split('#');

  // 如果没有扩展名，尝试添加 .md
  if (!pathOnly.includes('.')) {
    return (
      existsSync(pathOnly + '.md') ||
      existsSync(pathOnly + '/README.md') ||
      existsSync(pathOnly)
    );
  }

  return existsSync(pathOnly);
}

/**
 * 验证单个文件
 */
function validateFile(filePath: string): void {
  console.log(`验证文件: ${filePath}`);

  try {
    const content = readFileSync(filePath, 'utf-8');
    const links = extractLinks(content);

    links.forEach(({ link, line }) => {
      if (!validateLink(filePath, link)) {
        issues.push({
          file: filePath,
          line,
          link,
          issue: '链接目标不存在',
        });
      }
    });
  } catch (error) {
    issues.push({
      file: filePath,
      line: 0,
      link: '',
      issue: `读取文件失败: ${error}`,
    });
  }
}

/**
 * 主函数
 */
async function main() {
  console.log('🔍 开始验证文档链接...\n');

  // 查找所有 Markdown 文件
  const markdownFiles = await glob(`${DOCS_ROOT}/**/*.md`);

  console.log(`找到 ${markdownFiles.length} 个 Markdown 文件\n`);

  // 验证每个文件
  markdownFiles.forEach(validateFile);

  // 输出结果
  if (issues.length === 0) {
    console.log('✅ 所有链接验证通过！');
  } else {
    console.log(`❌ 发现 ${issues.length} 个问题:\n`);

    issues.forEach((issue) => {
      console.log(`📄 ${issue.file}:${issue.line}`);
      console.log(`   链接: ${issue.link}`);
      console.log(`   问题: ${issue.issue}\n`);
    });

    process.exit(1);
  }
}

// 运行验证
main().catch(console.error);
